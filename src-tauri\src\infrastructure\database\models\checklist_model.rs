// 清单系统数据模型
// 对应数据库中的清单相关表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 清单模板数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ChecklistTemplateModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub category: Option<String>,
    pub is_public: bool,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 清单项目数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ChecklistItemModel {
    pub id: String,
    pub template_id: String,
    pub item_text: String,
    pub description: Option<String>,
    pub sort_order: i32,
    pub is_required: bool,
    pub estimated_minutes: Option<i32>,
    pub created_at: DateTime<Utc>,
}

/// 清单实例数据模型
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct ChecklistInstanceModel {
    pub id: String,
    pub template_id: String,
    pub title: String,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub status: String,
    pub completion_percentage: i32,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 清单实例项目数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ChecklistInstanceItemModel {
    pub id: String,
    pub instance_id: String,
    pub template_item_id: String,
    pub is_completed: bool,
    pub completion_note: Option<String>,
    pub completed_by: Option<String>,
    pub completed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

/// 清单分类枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChecklistCategory {
    Project,
    Area,
    General,
}

impl ChecklistCategory {
    pub fn as_str(&self) -> &'static str {
        match self {
            ChecklistCategory::Project => "project",
            ChecklistCategory::Area => "area",
            ChecklistCategory::General => "general",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "project" => Some(ChecklistCategory::Project),
            "area" => Some(ChecklistCategory::Area),
            "general" => Some(ChecklistCategory::General),
            _ => None,
        }
    }
}

/// 清单实例状态枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChecklistInstanceStatus {
    Active,
    Completed,
    Cancelled,
}

impl ChecklistInstanceStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            ChecklistInstanceStatus::Active => "active",
            ChecklistInstanceStatus::Completed => "completed",
            ChecklistInstanceStatus::Cancelled => "cancelled",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "active" => Some(ChecklistInstanceStatus::Active),
            "completed" => Some(ChecklistInstanceStatus::Completed),
            "cancelled" => Some(ChecklistInstanceStatus::Cancelled),
            _ => None,
        }
    }
}

/// 清单模板统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistTemplateStats {
    pub template_id: String,
    pub template_name: String,
    pub category: Option<String>,
    pub is_public: bool,
    pub total_items: i64,
    pub required_items: i64,
    pub usage_count: i64,
    pub avg_completion_rate: f64,
    pub created_at: DateTime<Utc>,
}

/// 清单实例详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistInstanceDetails {
    pub instance_id: String,
    pub title: String,
    pub status: String,
    pub completion_percentage: i32,
    pub template_name: String,
    pub template_category: Option<String>,
    pub project_name: Option<String>,
    pub area_name: Option<String>,
    pub total_items: i64,
    pub completed_items: i64,
    pub pending_required_items: i64,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 清单模板完整数据（包含项目）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistTemplateWithItems {
    pub template: ChecklistTemplateModel,
    pub items: Vec<ChecklistItemModel>,
}

/// 清单实例完整数据（包含项目）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistInstanceWithItems {
    pub instance: ChecklistInstanceModel,
    pub template: ChecklistTemplateModel,
    pub items: Vec<ChecklistInstanceItemWithTemplate>,
}

/// 清单实例项目与模板项目的组合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistInstanceItemWithTemplate {
    pub instance_item: ChecklistInstanceItemModel,
    pub template_item: ChecklistItemModel,
}

/// 清单创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateChecklistTemplateRequest {
    pub name: String,
    pub description: Option<String>,
    pub category: Option<ChecklistCategory>,
    pub is_public: bool,
    pub items: Vec<CreateChecklistItemRequest>,
}

/// 清单项目创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateChecklistItemRequest {
    pub item_text: String,
    pub description: Option<String>,
    pub is_required: bool,
    pub estimated_minutes: Option<i32>,
}

/// 清单实例创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateChecklistInstanceRequest {
    pub template_id: String,
    pub title: String,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
}

/// 清单模板更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateChecklistTemplateRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub category: Option<ChecklistCategory>,
    pub is_public: Option<bool>,
}

/// 清单实例更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateChecklistInstanceRequest {
    pub title: Option<String>,
    pub status: Option<ChecklistInstanceStatus>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
}

/// 清单项目完成请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompleteChecklistItemRequest {
    pub is_completed: bool,
    pub completion_note: Option<String>,
}

/// 清单批量操作请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchChecklistItemRequest {
    pub item_ids: Vec<String>,
    pub action: ChecklistBatchAction,
}

/// 清单批量操作类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChecklistBatchAction {
    Complete,
    Uncomplete,
    Delete,
}

/// 清单进度统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistProgress {
    pub total_items: i32,
    pub completed_items: i32,
    pub required_items: i32,
    pub completed_required_items: i32,
    pub completion_percentage: f64,
    pub required_completion_percentage: f64,
    pub estimated_total_minutes: i32,
    pub estimated_remaining_minutes: i32,
}

/// 清单使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChecklistUsageStats {
    pub template_id: String,
    pub template_name: String,
    pub total_instances: i64,
    pub completed_instances: i64,
    pub active_instances: i64,
    pub cancelled_instances: i64,
    pub avg_completion_time_hours: f64,
    pub completion_rate: f64,
    pub most_skipped_items: Vec<String>,
}

impl ChecklistTemplateWithItems {
    /// 获取必填项目数量
    pub fn required_items_count(&self) -> usize {
        self.items.iter().filter(|item| item.is_required).count()
    }

    /// 获取总项目数量
    pub fn total_items_count(&self) -> usize {
        self.items.len()
    }

    /// 按排序顺序获取项目
    pub fn sorted_items(&self) -> Vec<&ChecklistItemModel> {
        let mut items: Vec<&ChecklistItemModel> = self.items.iter().collect();
        items.sort_by_key(|item| item.sort_order);
        items
    }

    /// 计算预估总时间（分钟）
    pub fn estimated_total_minutes(&self) -> i32 {
        self.items
            .iter()
            .filter_map(|item| item.estimated_minutes)
            .sum()
    }
}

impl ChecklistInstanceWithItems {
    /// 计算进度统计
    pub fn calculate_progress(&self) -> ChecklistProgress {
        let total_items = self.items.len() as i32;
        let completed_items = self.items
            .iter()
            .filter(|item| item.instance_item.is_completed)
            .count() as i32;

        let required_items = self.items
            .iter()
            .filter(|item| item.template_item.is_required)
            .count() as i32;

        let completed_required_items = self.items
            .iter()
            .filter(|item| {
                item.template_item.is_required && item.instance_item.is_completed
            })
            .count() as i32;

        let completion_percentage = if total_items > 0 {
            (completed_items as f64 / total_items as f64) * 100.0
        } else {
            0.0
        };

        let required_completion_percentage = if required_items > 0 {
            (completed_required_items as f64 / required_items as f64) * 100.0
        } else {
            100.0
        };

        let estimated_total_minutes = self.items
            .iter()
            .filter_map(|item| item.template_item.estimated_minutes)
            .sum();

        let estimated_remaining_minutes = self.items
            .iter()
            .filter(|item| !item.instance_item.is_completed)
            .filter_map(|item| item.template_item.estimated_minutes)
            .sum();

        ChecklistProgress {
            total_items,
            completed_items,
            required_items,
            completed_required_items,
            completion_percentage,
            required_completion_percentage,
            estimated_total_minutes,
            estimated_remaining_minutes,
        }
    }

    /// 检查是否可以完成
    pub fn can_complete(&self) -> bool {
        let progress = self.calculate_progress();
        progress.completed_required_items == progress.required_items
    }

    /// 获取未完成的必填项目
    pub fn pending_required_items(&self) -> Vec<&ChecklistInstanceItemWithTemplate> {
        self.items
            .iter()
            .filter(|item| {
                item.template_item.is_required && !item.instance_item.is_completed
            })
            .collect()
    }
}
