// 领域应用服务
// 实现领域相关的业务用例

use crate::application::services::{Service, ServiceContext, ServiceResult, PagedResult, ServiceUtils};
use crate::domain::entities::area::{Area, CreateAreaData, UpdateAreaData, AreaQuery};
use crate::domain::repositories::area_repository::{AreaRepository, AreaSummary};
use crate::shared::types::{EntityId, QueryParams, Pagination};
use crate::shared::errors::{AppError, AppResult};
use std::sync::Arc;

/// 领域应用服务
pub struct AreaService {
    area_repository: Arc<dyn AreaRepository>,
}

impl AreaService {
    /// 创建新的领域服务
    pub fn new(area_repository: Arc<dyn AreaRepository>) -> Self {
        Self { area_repository }
    }

    /// 创建领域
    pub async fn create_area(
        &self,
        context: &ServiceContext,
        data: CreateAreaData,
    ) -> AppResult<ServiceResult<Area>> {
        let user_id = context.user_id()?;

        // 确保创建者是当前用户
        if data.created_by != *user_id {
            return Err(AppError::forbidden("只能创建自己的领域"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_name = %data.name,
            "Creating new area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.create(data).await
        }).await;

        match result {
            Ok(area) => {
                ServiceUtils::log_audit_event(
                    context,
                    "create_area",
                    "area",
                    &area.id,
                    Some(&format!("Created area: {}", area.name)),
                );

                Ok(ServiceResult::new(area)
                    .with_execution_time(execution_time)
                    .with_affected_entity(area.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to create area"
                );
                Err(e)
            }
        }
    }

    /// 获取领域
    pub async fn get_area(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<Option<Area>>> {
        tracing::debug!(
            request_id = %context.request_id,
            area_id = %id,
            "Getting area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.find_by_id(id).await
        }).await;

        match result {
            Ok(area_opt) => {
                if let Some(ref area) = area_opt {
                    // 检查权限：只能查看自己的领域
                    let user_id = context.user_id()?;
                    if area.created_by != *user_id {
                        return Err(AppError::forbidden("无权访问此领域"));
                    }

                    ServiceUtils::log_audit_event(
                        context,
                        "get_area",
                        "area",
                        id,
                        Some("Area retrieved successfully"),
                    );
                }

                Ok(ServiceResult::new(area_opt).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to get area"
                );
                Err(e)
            }
        }
    }

    /// 更新领域
    pub async fn update_area(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        data: UpdateAreaData,
    ) -> AppResult<ServiceResult<Area>> {
        // 先获取领域检查权限
        let existing_area = self.area_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        let user_id = context.user_id()?;
        if existing_area.created_by != *user_id {
            return Err(AppError::forbidden("无权修改此领域"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %id,
            "Updating area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.update(id, data).await
        }).await;

        match result {
            Ok(area) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_area",
                    "area",
                    &area.id,
                    Some(&format!("Updated area: {}", area.name)),
                );

                Ok(ServiceResult::new(area)
                    .with_execution_time(execution_time)
                    .with_affected_entity(area.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to update area"
                );
                Err(e)
            }
        }
    }

    /// 删除领域
    pub async fn delete_area(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 先获取领域检查权限
        let existing_area = self.area_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        let user_id = context.user_id()?;
        if existing_area.created_by != *user_id {
            return Err(AppError::forbidden("无权删除此领域"));
        }

        tracing::warn!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %id,
            "Deleting area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.delete(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "delete_area",
                    "area",
                    id,
                    Some("Area deleted successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to delete area"
                );
                Err(e)
            }
        }
    }

    /// 列出用户的领域
    pub async fn list_user_areas(
        &self,
        context: &ServiceContext,
        page: u32,
        page_size: u32,
        active_only: bool,
    ) -> AppResult<ServiceResult<PagedResult<Area>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            page = %page,
            page_size = %page_size,
            active_only = %active_only,
            "Listing user areas"
        );

        let query = AreaQuery {
            name: None,
            is_active: if active_only { Some(true) } else { None },
            created_by: Some(user_id.clone()),
            created_after: None,
            created_before: None,
        };

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: None,
            sort: None,
            filters: None,
        };

        let (areas_result, execution_time) = ServiceUtils::measure_time(async {
            let areas = self.area_repository.find_all(query.clone(), params).await?;
            let total_count = self.area_repository.count(query).await?;
            Ok::<(Vec<Area>, u64), AppError>((areas, total_count))
        }).await;

        match areas_result {
            Ok((areas, total_count)) => {
                let paged_result = PagedResult::new(areas, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "list_user_areas",
                    "area",
                    "multiple",
                    Some(&format!("Retrieved {} areas", paged_result.len())),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to list user areas"
                );
                Err(e)
            }
        }
    }

    /// 激活领域
    pub async fn activate_area(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限
        let existing_area = self.area_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        let user_id = context.user_id()?;
        if existing_area.created_by != *user_id {
            return Err(AppError::forbidden("无权激活此领域"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %id,
            "Activating area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.activate(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "activate_area",
                    "area",
                    id,
                    Some("Area activated successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to activate area"
                );
                Err(e)
            }
        }
    }

    /// 停用领域
    pub async fn deactivate_area(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限
        let existing_area = self.area_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        let user_id = context.user_id()?;
        if existing_area.created_by != *user_id {
            return Err(AppError::forbidden("无权停用此领域"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %id,
            "Deactivating area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.deactivate(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "deactivate_area",
                    "area",
                    id,
                    Some("Area deactivated successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to deactivate area"
                );
                Err(e)
            }
        }
    }

    /// 归档领域
    pub async fn archive_area(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限
        let existing_area = self.area_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        let user_id = context.user_id()?;
        if existing_area.created_by != *user_id {
            return Err(AppError::forbidden("无权归档此领域"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %id,
            "Archiving area"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.archive(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "archive_area",
                    "area",
                    id,
                    Some("Area archived successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to archive area"
                );
                Err(e)
            }
        }
    }

    /// 获取领域摘要
    pub async fn get_area_summary(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<AreaSummary>> {
        // 检查权限
        let existing_area = self.area_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        let user_id = context.user_id()?;
        if existing_area.created_by != *user_id {
            return Err(AppError::forbidden("无权查看此领域摘要"));
        }

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %id,
            "Getting area summary"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.get_area_summary(id).await
        }).await;

        match result {
            Ok(summary) => {
                ServiceUtils::log_audit_event(
                    context,
                    "get_area_summary",
                    "area",
                    id,
                    Some("Area summary retrieved successfully"),
                );

                Ok(ServiceResult::new(summary).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %id,
                    "Failed to get area summary"
                );
                Err(e)
            }
        }
    }

    /// 搜索领域
    pub async fn search_areas(
        &self,
        context: &ServiceContext,
        keyword: &str,
        page: u32,
        page_size: u32,
    ) -> AppResult<ServiceResult<PagedResult<Area>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            keyword = %keyword,
            "Searching areas"
        );

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: Some(keyword.to_string()),
            sort: None,
            filters: None,
        };

        let (result, execution_time) = ServiceUtils::measure_time(async {
            let mut areas = self.area_repository.search(keyword, params).await?;
            // 只返回当前用户的领域
            areas.retain(|a| a.created_by == *user_id);
            let total_count = areas.len() as u64;
            Ok::<(Vec<Area>, u64), AppError>((areas, total_count))
        }).await;

        match result {
            Ok((areas, total_count)) => {
                let paged_result = PagedResult::new(areas, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "search_areas",
                    "area",
                    "multiple",
                    Some(&format!("Found {} areas for keyword: {}", paged_result.len(), keyword)),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    keyword = %keyword,
                    "Failed to search areas"
                );
                Err(e)
            }
        }
    }

    /// 记录习惯
    pub async fn record_habit(
        &self,
        context: &ServiceContext,
        area_id: &EntityId,
        habit_name: &str,
    ) -> AppResult<ServiceResult<()>> {
        let user_id = context.user_id()?;

        // 检查领域是否存在且属于当前用户
        let area = self.area_repository.find_by_id(area_id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        if area.created_by != *user_id {
            return Err(AppError::forbidden("无权在此领域记录习惯"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %area_id,
            habit_name = %habit_name,
            "Recording habit"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.record_habit(area_id, habit_name).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "record_habit",
                    "area",
                    area_id,
                    Some(&format!("Recorded habit: {}", habit_name)),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(area_id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %area_id,
                    habit_name = %habit_name,
                    "Failed to record habit"
                );
                Err(e)
            }
        }
    }

    /// 获取习惯连续记录天数
    pub async fn get_habit_streak(
        &self,
        context: &ServiceContext,
        area_id: &EntityId,
        habit_name: &str,
    ) -> AppResult<ServiceResult<u32>> {
        let user_id = context.user_id()?;

        // 检查领域是否存在且属于当前用户
        let area = self.area_repository.find_by_id(area_id).await?
            .ok_or_else(|| AppError::not_found("领域"))?;

        if area.created_by != *user_id {
            return Err(AppError::forbidden("无权查看此领域的习惯记录"));
        }

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            area_id = %area_id,
            habit_name = %habit_name,
            "Getting habit streak"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.area_repository.get_habit_streak(area_id, habit_name).await
        }).await;

        match result {
            Ok(streak) => {
                Ok(ServiceResult::new(streak)
                    .with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    area_id = %area_id,
                    habit_name = %habit_name,
                    "Failed to get habit streak"
                );
                Err(e)
            }
        }
    }
}

impl Service for AreaService {
    fn name(&self) -> &'static str {
        "AreaService"
    }

    async fn health_check(&self) -> AppResult<()> {
        let query = AreaQuery {
            name: None,
            is_active: None,
            created_by: None,
            created_after: None,
            created_before: None,
        };
        
        let params = QueryParams {
            pagination: Some(Pagination::new(1, 1)),
            search: None,
            sort: None,
            filters: None,
        };

        self.area_repository.find_all(query, params).await?;
        Ok(())
    }
}
