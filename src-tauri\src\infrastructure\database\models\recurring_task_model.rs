// 定期任务系统数据模型
// 对应数据库中的定期任务相关表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate};

/// 定期任务数据模型
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct RecurringTaskModel {
    pub id: String,
    pub area_id: String,
    pub title: String,
    pub description: Option<String>,
    pub recurrence_pattern: String,
    pub recurrence_interval: i32,
    pub recurrence_days: Option<String>, // JSON格式
    pub start_date: NaiveDate,
    pub end_date: Option<NaiveDate>,
    pub next_due_date: NaiveDate,
    pub is_active: bool,
    pub auto_create_tasks: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 定期任务执行记录数据模型
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct RecurringTaskExecutionModel {
    pub id: String,
    pub recurring_task_id: String,
    pub scheduled_date: NaiveDate,
    pub created_task_id: Option<String>,
    pub execution_status: String,
    pub execution_note: Option<String>,
    pub executed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

/// 重复模式枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecurrencePattern {
    Daily,
    Weekly,
    Monthly,
    Yearly,
}

impl RecurrencePattern {
    pub fn as_str(&self) -> &'static str {
        match self {
            RecurrencePattern::Daily => "daily",
            RecurrencePattern::Weekly => "weekly",
            RecurrencePattern::Monthly => "monthly",
            RecurrencePattern::Yearly => "yearly",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "daily" => Some(RecurrencePattern::Daily),
            "weekly" => Some(RecurrencePattern::Weekly),
            "monthly" => Some(RecurrencePattern::Monthly),
            "yearly" => Some(RecurrencePattern::Yearly),
            _ => None,
        }
    }
}

/// 执行状态枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExecutionStatus {
    Pending,
    Created,
    Skipped,
    Failed,
}

impl ExecutionStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            ExecutionStatus::Pending => "pending",
            ExecutionStatus::Created => "created",
            ExecutionStatus::Skipped => "skipped",
            ExecutionStatus::Failed => "failed",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "pending" => Some(ExecutionStatus::Pending),
            "created" => Some(ExecutionStatus::Created),
            "skipped" => Some(ExecutionStatus::Skipped),
            "failed" => Some(ExecutionStatus::Failed),
            _ => None,
        }
    }
}

/// 重复规则配置（用于JSON序列化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecurrenceRule {
    pub pattern: RecurrencePattern,
    pub interval: i32,
    pub weekdays: Option<Vec<i32>>, // 1-7 (周一到周日)
    pub monthdays: Option<Vec<i32>>, // 1-31
    pub yeardays: Option<Vec<YearDay>>,
}

/// 年度日期配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct YearDay {
    pub month: i32, // 1-12
    pub day: i32,   // 1-31
}

impl RecurrenceRule {
    /// 创建每日重复规则
    pub fn daily(interval: i32) -> Self {
        Self {
            pattern: RecurrencePattern::Daily,
            interval,
            weekdays: None,
            monthdays: None,
            yeardays: None,
        }
    }

    /// 创建工作日重复规则
    pub fn weekdays() -> Self {
        Self {
            pattern: RecurrencePattern::Weekly,
            interval: 1,
            weekdays: Some(vec![1, 2, 3, 4, 5]), // 周一到周五
            monthdays: None,
            yeardays: None,
        }
    }

    /// 创建每周重复规则
    pub fn weekly(interval: i32, weekdays: Vec<i32>) -> Self {
        Self {
            pattern: RecurrencePattern::Weekly,
            interval,
            weekdays: Some(weekdays),
            monthdays: None,
            yeardays: None,
        }
    }

    /// 创建每月重复规则
    pub fn monthly(interval: i32, monthdays: Vec<i32>) -> Self {
        Self {
            pattern: RecurrencePattern::Monthly,
            interval,
            weekdays: None,
            monthdays: Some(monthdays),
            yeardays: None,
        }
    }

    /// 创建每年重复规则
    pub fn yearly(interval: i32, yeardays: Vec<YearDay>) -> Self {
        Self {
            pattern: RecurrencePattern::Yearly,
            interval,
            weekdays: None,
            monthdays: None,
            yeardays: Some(yeardays),
        }
    }

    /// 序列化为JSON字符串
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }

    /// 从JSON字符串反序列化
    pub fn from_json(json: &str) -> Result<Self, serde_json::Error> {
        serde_json::from_str(json)
    }
}

/// 定期任务统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecurringTaskStats {
    pub task_id: String,
    pub title: String,
    pub area_id: String,
    pub recurrence_pattern: String,
    pub is_active: bool,
    pub total_executions: i64,
    pub successful_executions: i64,
    pub skipped_executions: i64,
    pub failed_executions: i64,
    pub success_rate: f64,
    pub next_due_date: NaiveDate,
    pub created_at: DateTime<Utc>,
}

/// 定期任务执行历史
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecurringTaskHistory {
    pub task: RecurringTaskModel,
    pub executions: Vec<RecurringTaskExecutionModel>,
    pub stats: RecurringTaskStats,
}

/// 定期任务调度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecurringTaskSchedule {
    pub task_id: String,
    pub title: String,
    pub area_id: String,
    pub next_due_date: NaiveDate,
    pub is_overdue: bool,
    pub days_overdue: i32,
    pub auto_create_tasks: bool,
    pub recurrence_rule: RecurrenceRule,
}

/// 定期任务创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateRecurringTaskRequest {
    pub area_id: String,
    pub title: String,
    pub description: Option<String>,
    pub recurrence_rule: RecurrenceRule,
    pub start_date: NaiveDate,
    pub end_date: Option<NaiveDate>,
    pub auto_create_tasks: bool,
}

/// 定期任务更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateRecurringTaskRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub recurrence_rule: Option<RecurrenceRule>,
    pub end_date: Option<NaiveDate>,
    pub is_active: Option<bool>,
    pub auto_create_tasks: Option<bool>,
}

/// 定期任务执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecurringTaskExecutionResult {
    pub execution_id: String,
    pub recurring_task_id: String,
    pub scheduled_date: NaiveDate,
    pub status: ExecutionStatus,
    pub created_task_id: Option<String>,
    pub error_message: Option<String>,
    pub executed_at: DateTime<Utc>,
}

/// 定期任务批量处理结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchProcessResult {
    pub processed_count: i32,
    pub successful_count: i32,
    pub failed_count: i32,
    pub skipped_count: i32,
    pub results: Vec<RecurringTaskExecutionResult>,
    pub errors: Vec<String>,
}

impl RecurringTaskModel {
    /// 获取重复规则
    pub fn get_recurrence_rule(&self) -> Result<RecurrenceRule, serde_json::Error> {
        if let Some(days_json) = &self.recurrence_days {
            RecurrenceRule::from_json(days_json)
        } else {
            // 如果没有详细规则，创建基本规则
            let pattern = RecurrencePattern::from_str(&self.recurrence_pattern)
                .unwrap_or(RecurrencePattern::Daily);
            Ok(RecurrenceRule {
                pattern,
                interval: self.recurrence_interval,
                weekdays: None,
                monthdays: None,
                yeardays: None,
            })
        }
    }

    /// 检查是否过期
    pub fn is_overdue(&self, current_date: NaiveDate) -> bool {
        self.is_active && self.next_due_date < current_date
    }

    /// 计算过期天数
    pub fn days_overdue(&self, current_date: NaiveDate) -> i32 {
        if self.is_overdue(current_date) {
            (current_date - self.next_due_date).num_days() as i32
        } else {
            0
        }
    }

    /// 检查是否应该结束
    pub fn should_end(&self, current_date: NaiveDate) -> bool {
        if let Some(end_date) = self.end_date {
            current_date > end_date
        } else {
            false
        }
    }
}

impl RecurringTaskExecutionModel {
    /// 检查是否成功执行
    pub fn is_successful(&self) -> bool {
        self.execution_status == ExecutionStatus::Created.as_str()
    }

    /// 检查是否失败
    pub fn is_failed(&self) -> bool {
        self.execution_status == ExecutionStatus::Failed.as_str()
    }

    /// 检查是否被跳过
    pub fn is_skipped(&self) -> bool {
        self.execution_status == ExecutionStatus::Skipped.as_str()
    }

    /// 检查是否待处理
    pub fn is_pending(&self) -> bool {
        self.execution_status == ExecutionStatus::Pending.as_str()
    }
}
