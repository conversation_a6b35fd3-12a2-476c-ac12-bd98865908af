// 数据库连接管理
// 负责创建和管理SQLite数据库连接池

use sqlx::{SqlitePool, sqlite::SqlitePoolOptions};
use crate::shared::errors::{AppError, AppResult};
use crate::infrastructure::database::migration_manager::MigrationManager;
use std::time::Duration;
use std::path::Path;

/// 数据库连接管理器
pub struct DatabaseConnection {
    pool: SqlitePool,
}

impl DatabaseConnection {
    /// 创建新的数据库连接
    pub async fn new(database_url: &str) -> AppResult<Self> {
        // 确保数据库目录存在
        if let Some(parent) = Path::new(database_url.trim_start_matches("sqlite://")).parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| AppError::database(format!("Failed to create database directory: {}", e)))?;
        }

        // 创建连接池
        let pool = SqlitePoolOptions::new()
            .max_connections(20)
            .min_connections(5)
            .acquire_timeout(Duration::from_secs(30))
            .idle_timeout(Duration::from_secs(600))
            .max_lifetime(Duration::from_secs(1800))
            .connect(database_url)
            .await
            .map_err(|e| AppError::database(format!("Failed to connect to database: {}", e)))?;

        // 启用外键约束
        sqlx::query("PRAGMA foreign_keys = ON")
            .execute(&pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to enable foreign keys: {}", e)))?;

        // 设置WAL模式以提高并发性能
        sqlx::query("PRAGMA journal_mode = WAL")
            .execute(&pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to set WAL mode: {}", e)))?;

        // 设置同步模式
        sqlx::query("PRAGMA synchronous = NORMAL")
            .execute(&pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to set synchronous mode: {}", e)))?;

        // 设置缓存大小 (10MB)
        sqlx::query("PRAGMA cache_size = -10000")
            .execute(&pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to set cache size: {}", e)))?;

        // 设置临时存储为内存
        sqlx::query("PRAGMA temp_store = MEMORY")
            .execute(&pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to set temp store: {}", e)))?;

        tracing::info!(
            database_url = %database_url,
            "Database connection established"
        );

        Ok(Self { pool })
    }

    /// 创建内存数据库连接 (用于测试)
    pub async fn new_in_memory() -> AppResult<Self> {
        Self::new("sqlite::memory:").await
    }

    /// 获取连接池
    pub fn get_pool(&self) -> SqlitePool {
        self.pool.clone()
    }

    /// 运行数据库迁移
    pub async fn migrate(&self) -> AppResult<()> {
        let migration_manager = MigrationManager::new(self.pool.clone());
        migration_manager.migrate().await?;
        
        tracing::info!("Database migrations completed");
        Ok(())
    }

    /// 检查数据库健康状态
    pub async fn health_check(&self) -> AppResult<DatabaseHealth> {
        let start = std::time::Instant::now();

        // 执行简单查询测试连接
        let result = sqlx::query("SELECT 1 as test")
            .fetch_one(&self.pool)
            .await;

        let response_time = start.elapsed();

        match result {
            Ok(_) => {
                let migration_manager = MigrationManager::new(self.pool.clone());
                let status = migration_manager.check_database_status().await?;
                let stats = migration_manager.get_database_stats().await?;

                Ok(DatabaseHealth {
                    is_healthy: true,
                    response_time_ms: response_time.as_millis() as u64,
                    pool_size: self.get_pool_info().await?,
                    migration_status: status,
                    database_stats: stats,
                    error: None,
                })
            }
            Err(e) => {
                tracing::error!(error = %e, "Database health check failed");
                Ok(DatabaseHealth {
                    is_healthy: false,
                    response_time_ms: response_time.as_millis() as u64,
                    pool_size: PoolInfo::default(),
                    migration_status: Default::default(),
                    database_stats: Default::default(),
                    error: Some(e.to_string()),
                })
            }
        }
    }

    /// 获取连接池信息
    async fn get_pool_info(&self) -> AppResult<PoolInfo> {
        Ok(PoolInfo {
            size: self.pool.size() as u32,
            idle: self.pool.num_idle() as u32,
            used: (self.pool.size() - self.pool.num_idle()) as u32,
        })
    }

    /// 关闭数据库连接
    pub async fn close(&self) {
        self.pool.close().await;
        tracing::info!("Database connection closed");
    }

    /// 执行数据库备份
    pub async fn backup(&self, backup_path: &str) -> AppResult<()> {
        // 创建备份目录
        if let Some(parent) = Path::new(backup_path).parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| AppError::database(format!("Failed to create backup directory: {}", e)))?;
        }

        // 执行VACUUM INTO命令进行备份
        let sql = format!("VACUUM INTO '{}'", backup_path);
        sqlx::query(&sql)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to backup database: {}", e)))?;

        tracing::info!(backup_path = %backup_path, "Database backup completed");
        Ok(())
    }

    /// 优化数据库
    pub async fn optimize(&self) -> AppResult<()> {
        // 分析查询计划
        sqlx::query("ANALYZE")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to analyze database: {}", e)))?;

        // 重建索引
        sqlx::query("REINDEX")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to reindex database: {}", e)))?;

        // 清理未使用的空间
        sqlx::query("VACUUM")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to vacuum database: {}", e)))?;

        tracing::info!("Database optimization completed");
        Ok(())
    }

    /// 获取数据库版本信息
    pub async fn get_version_info(&self) -> AppResult<VersionInfo> {
        let sqlite_version = sqlx::query("SELECT sqlite_version() as version")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get SQLite version: {}", e)))?;

        let migration_manager = MigrationManager::new(self.pool.clone());
        let migration_status = migration_manager.check_database_status().await?;

        Ok(VersionInfo {
            sqlite_version: sqlite_version.get("version"),
            schema_version: migration_status.latest_applied,
            total_migrations: migration_status.total_migrations,
            applied_migrations: migration_status.applied_migrations,
        })
    }
}

/// 数据库健康状态
#[derive(Debug, Clone)]
pub struct DatabaseHealth {
    pub is_healthy: bool,
    pub response_time_ms: u64,
    pub pool_size: PoolInfo,
    pub migration_status: crate::infrastructure::database::migration_manager::DatabaseStatus,
    pub database_stats: crate::infrastructure::database::migration_manager::DatabaseStats,
    pub error: Option<String>,
}

/// 连接池信息
#[derive(Debug, Clone, Default)]
pub struct PoolInfo {
    pub size: u32,
    pub idle: u32,
    pub used: u32,
}

/// 版本信息
#[derive(Debug, Clone)]
pub struct VersionInfo {
    pub sqlite_version: String,
    pub schema_version: Option<String>,
    pub total_migrations: usize,
    pub applied_migrations: usize,
}

impl Default for crate::infrastructure::database::migration_manager::DatabaseStatus {
    fn default() -> Self {
        Self {
            total_migrations: 0,
            applied_migrations: 0,
            pending_migrations: 0,
            latest_applied: None,
            is_up_to_date: true,
        }
    }
}

impl Default for crate::infrastructure::database::migration_manager::DatabaseStats {
    fn default() -> Self {
        Self {
            table_counts: std::collections::HashMap::new(),
            database_size: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_database_connection() {
        let db = DatabaseConnection::new_in_memory().await.unwrap();
        
        // 测试健康检查
        let health = db.health_check().await.unwrap();
        assert!(health.is_healthy);

        // 测试迁移
        db.migrate().await.unwrap();

        // 再次检查健康状态
        let health_after_migration = db.health_check().await.unwrap();
        assert!(health_after_migration.is_healthy);
        assert!(health_after_migration.migration_status.is_up_to_date);

        // 测试版本信息
        let version_info = db.get_version_info().await.unwrap();
        assert!(!version_info.sqlite_version.is_empty());
    }

    #[tokio::test]
    async fn test_database_optimization() {
        let db = DatabaseConnection::new_in_memory().await.unwrap();
        db.migrate().await.unwrap();
        
        // 测试优化
        db.optimize().await.unwrap();
    }
}
