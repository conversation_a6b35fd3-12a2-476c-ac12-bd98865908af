-- PaoLife 复盘系统表结构
-- 创建时间: 2025-01-29
-- 版本: 1.1.0

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 复盘模板表
CREATE TABLE review_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_type TEXT NOT NULL DEFAULT 'weekly'
        CHECK (template_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'project')),
    is_default BOOLEAN DEFAULT FALSE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 复盘模板问题表
CREATE TABLE review_template_questions (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES review_templates(id) ON DELETE CASCADE,
    question_text TEXT NOT NULL,
    question_type TEXT NOT NULL DEFAULT 'text'
        CHECK (question_type IN ('text', 'rating', 'boolean', 'number', 'date')),
    sort_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT FALSE,
    placeholder_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 复盘记录表
CREATE TABLE reviews (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES review_templates(id),
    title TEXT NOT NULL,
    review_period_start DATE NOT NULL,
    review_period_end DATE NOT NULL,
    status TEXT DEFAULT 'draft'
        CHECK (status IN ('draft', 'completed', 'archived')),
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 10),
    key_insights TEXT,
    action_items TEXT, -- JSON数组存储行动项
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 复盘答案表
CREATE TABLE review_answers (
    id TEXT PRIMARY KEY,
    review_id TEXT NOT NULL REFERENCES reviews(id) ON DELETE CASCADE,
    question_id TEXT NOT NULL REFERENCES review_template_questions(id),
    answer_text TEXT,
    answer_number DECIMAL(15,4),
    answer_boolean BOOLEAN,
    answer_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(review_id, question_id)
);

-- ================================
-- 索引定义
-- ================================

-- 复盘模板表索引
CREATE INDEX idx_review_templates_type ON review_templates(template_type);
CREATE INDEX idx_review_templates_created_by ON review_templates(created_by);
CREATE INDEX idx_review_templates_is_default ON review_templates(is_default);

-- 复盘模板问题表索引
CREATE INDEX idx_review_template_questions_template ON review_template_questions(template_id);
CREATE INDEX idx_review_template_questions_sort ON review_template_questions(template_id, sort_order);

-- 复盘记录表索引
CREATE INDEX idx_reviews_template ON reviews(template_id);
CREATE INDEX idx_reviews_created_by ON reviews(created_by);
CREATE INDEX idx_reviews_status ON reviews(status);
CREATE INDEX idx_reviews_period ON reviews(review_period_start, review_period_end);
CREATE INDEX idx_reviews_created_at ON reviews(created_at);

-- 复盘答案表索引
CREATE INDEX idx_review_answers_review ON review_answers(review_id);
CREATE INDEX idx_review_answers_question ON review_answers(question_id);

-- ================================
-- 触发器定义
-- ================================

-- 自动更新 updated_at 字段的触发器
CREATE TRIGGER trigger_review_templates_updated_at
    AFTER UPDATE ON review_templates
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE review_templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_review_answers_updated_at
    AFTER UPDATE ON review_answers
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE review_answers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 复盘完成时自动设置 completed_at
CREATE TRIGGER trigger_reviews_completed_at
    AFTER UPDATE ON reviews
    FOR EACH ROW
    WHEN NEW.status = 'completed' AND OLD.status != 'completed'
BEGIN
    UPDATE reviews SET completed_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 复盘状态变更时清除 completed_at
CREATE TRIGGER trigger_reviews_uncompleted_at
    AFTER UPDATE ON reviews
    FOR EACH ROW
    WHEN NEW.status != 'completed' AND OLD.status = 'completed'
BEGIN
    UPDATE reviews SET completed_at = NULL WHERE id = NEW.id;
END;

-- ================================
-- 初始数据插入
-- ================================

-- 插入默认复盘模板
INSERT INTO review_templates (id, name, description, template_type, is_default, created_by) VALUES
('template_daily', '每日复盘', '每日工作和生活复盘模板', 'daily', TRUE, 'system'),
('template_weekly', '每周复盘', '每周工作和生活复盘模板', 'weekly', TRUE, 'system'),
('template_monthly', '每月复盘', '每月工作和生活复盘模板', 'monthly', TRUE, 'system'),
('template_quarterly', '季度复盘', '季度工作和生活复盘模板', 'quarterly', TRUE, 'system'),
('template_project', '项目复盘', '项目结束后的复盘模板', 'project', TRUE, 'system');

-- 插入每日复盘问题
INSERT INTO review_template_questions (id, template_id, question_text, question_type, sort_order, is_required) VALUES
('q_daily_1', 'template_daily', '今天完成了哪些重要任务？', 'text', 1, TRUE),
('q_daily_2', 'template_daily', '今天遇到了什么挑战？', 'text', 2, FALSE),
('q_daily_3', 'template_daily', '今天学到了什么？', 'text', 3, FALSE),
('q_daily_4', 'template_daily', '明天的重点任务是什么？', 'text', 4, TRUE),
('q_daily_5', 'template_daily', '今天的整体满意度（1-10分）', 'rating', 5, FALSE);

-- 插入每周复盘问题
INSERT INTO review_template_questions (id, template_id, question_text, question_type, sort_order, is_required) VALUES
('q_weekly_1', 'template_weekly', '本周最重要的成就是什么？', 'text', 1, TRUE),
('q_weekly_2', 'template_weekly', '本周遇到的主要挑战和解决方案', 'text', 2, TRUE),
('q_weekly_3', 'template_weekly', '本周的学习和成长', 'text', 3, FALSE),
('q_weekly_4', 'template_weekly', '下周的重点目标', 'text', 4, TRUE),
('q_weekly_5', 'template_weekly', '需要改进的地方', 'text', 5, FALSE),
('q_weekly_6', 'template_weekly', '本周整体评分（1-10分）', 'rating', 6, FALSE);

-- 插入每月复盘问题
INSERT INTO review_template_questions (id, template_id, question_text, question_type, sort_order, is_required) VALUES
('q_monthly_1', 'template_monthly', '本月最重要的成就和里程碑', 'text', 1, TRUE),
('q_monthly_2', 'template_monthly', '本月目标完成情况分析', 'text', 2, TRUE),
('q_monthly_3', 'template_monthly', '本月遇到的主要挑战和教训', 'text', 3, TRUE),
('q_monthly_4', 'template_monthly', '个人能力和技能的提升', 'text', 4, FALSE),
('q_monthly_5', 'template_monthly', '下月的重点计划和目标', 'text', 5, TRUE),
('q_monthly_6', 'template_monthly', '需要调整的策略和方法', 'text', 6, FALSE),
('q_monthly_7', 'template_monthly', '本月整体满意度（1-10分）', 'rating', 7, FALSE);

-- 插入季度复盘问题
INSERT INTO review_template_questions (id, template_id, question_text, question_type, sort_order, is_required) VALUES
('q_quarterly_1', 'template_quarterly', '本季度最重要的成就和突破', 'text', 1, TRUE),
('q_quarterly_2', 'template_quarterly', '季度目标达成情况详细分析', 'text', 2, TRUE),
('q_quarterly_3', 'template_quarterly', '本季度的重大挑战和应对策略', 'text', 3, TRUE),
('q_quarterly_4', 'template_quarterly', '个人成长和能力提升总结', 'text', 4, TRUE),
('q_quarterly_5', 'template_quarterly', '下季度的战略重点和目标', 'text', 5, TRUE),
('q_quarterly_6', 'template_quarterly', '需要建立或改进的系统和流程', 'text', 6, FALSE),
('q_quarterly_7', 'template_quarterly', '本季度整体评分（1-10分）', 'rating', 7, FALSE);

-- 插入项目复盘问题
INSERT INTO review_template_questions (id, template_id, question_text, question_type, sort_order, is_required) VALUES
('q_project_1', 'template_project', '项目目标达成情况', 'text', 1, TRUE),
('q_project_2', 'template_project', '项目过程中的亮点和成功因素', 'text', 2, TRUE),
('q_project_3', 'template_project', '遇到的主要问题和解决方案', 'text', 3, TRUE),
('q_project_4', 'template_project', '团队协作和沟通情况', 'text', 4, FALSE),
('q_project_5', 'template_project', '资源使用和时间管理', 'text', 5, FALSE),
('q_project_6', 'template_project', '经验教训和改进建议', 'text', 6, TRUE),
('q_project_7', 'template_project', '项目整体评分（1-10分）', 'rating', 7, FALSE);
