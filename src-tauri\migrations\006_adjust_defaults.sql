-- PaoLife 默认值调整
-- 创建时间: 2025-01-29
-- 版本: 1.1.0
-- 说明: 根据用户要求，保持默认语言为中文

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- ================================
-- 应用配置调整
-- ================================

-- 更新默认语言配置为中文（确保与当前实现一致）
UPDATE app_settings 
SET setting_value = 'zh-CN' 
WHERE setting_key = 'default_language';

-- 更新默认时区为中国时区
UPDATE app_settings 
SET setting_value = 'Asia/Shanghai' 
WHERE setting_key = 'default_timezone';

-- ================================
-- 添加新的应用配置项
-- ================================

-- 插入新的配置项（如果不存在）
INSERT OR IGNORE INTO app_settings (setting_key, setting_value, setting_type, description, is_user_configurable) VALUES
-- 界面相关配置
('ui_language', 'zh-CN', 'string', '用户界面语言', TRUE),
('ui_date_format', 'YYYY-MM-DD', 'string', '日期显示格式', TRUE),
('ui_time_format', '24h', 'string', '时间显示格式：12h/24h', TRUE),
('ui_first_day_of_week', '1', 'number', '一周的第一天：0=周日，1=周一', TRUE),

-- 通知相关配置
('notification_enabled', 'true', 'boolean', '是否启用通知', TRUE),
('notification_sound_enabled', 'true', 'boolean', '是否启用通知声音', TRUE),
('notification_task_reminder_minutes', '15', 'number', '任务提醒提前分钟数', TRUE),
('notification_habit_reminder_enabled', 'true', 'boolean', '是否启用习惯提醒', TRUE),

-- 数据同步配置
('auto_save_interval_seconds', '30', 'number', '自动保存间隔（秒）', TRUE),
('sync_enabled', 'false', 'boolean', '是否启用数据同步', TRUE),
('sync_interval_minutes', '60', 'number', '同步间隔（分钟）', TRUE),

-- 性能相关配置
('cache_enabled', 'true', 'boolean', '是否启用缓存', FALSE),
('cache_size_mb', '50', 'number', '缓存大小（MB）', TRUE),
('max_search_results', '100', 'number', '搜索结果最大数量', TRUE),
('pagination_page_size', '20', 'number', '分页大小', TRUE),

-- 安全相关配置
('session_timeout_minutes', '480', 'number', '会话超时时间（分钟）', TRUE),
('password_min_length', '8', 'number', '密码最小长度', FALSE),
('login_attempt_limit', '5', 'number', '登录尝试次数限制', FALSE),

-- 导入导出配置
('export_include_archived', 'false', 'boolean', '导出时是否包含已归档数据', TRUE),
('import_duplicate_strategy', 'skip', 'string', '导入重复数据策略：skip/update/create', TRUE),

-- 高级功能配置
('ai_suggestions_enabled', 'false', 'boolean', '是否启用AI建议功能', TRUE),
('analytics_enabled', 'true', 'boolean', '是否启用数据分析', TRUE),
('debug_mode', 'false', 'boolean', '是否启用调试模式', FALSE);

-- ================================
-- 用户表默认值优化
-- ================================

-- 注意：SQLite 不支持直接修改列的默认值
-- 如果需要修改用户表的默认语言，需要重建表
-- 但由于用户要求保持中文，当前实现已经正确，无需修改

-- 为新用户设置默认的用户设置
-- 这些设置将在用户注册时自动创建

-- ================================
-- 数据验证和清理
-- ================================

-- 清理可能存在的无效数据
DELETE FROM user_settings WHERE setting_value = '';
DELETE FROM app_settings WHERE setting_value = '';

-- 确保所有用户都有基本的设置项
-- 这个逻辑需要在应用层实现，这里只是记录需要的设置项

-- ================================
-- 性能优化配置
-- ================================

-- 更新SQLite性能相关设置
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456; -- 256MB

-- ================================
-- 数据完整性检查
-- ================================

-- 检查并修复可能的数据不一致问题

-- 确保所有任务都有有效的关联
UPDATE tasks 
SET area_id = (SELECT id FROM areas WHERE name = '默认领域' LIMIT 1)
WHERE project_id IS NULL AND area_id IS NULL;

-- 确保所有项目都有创建者
UPDATE projects 
SET created_by = 'system'
WHERE created_by IS NULL OR created_by = '';

-- 确保所有领域都有创建者
UPDATE areas 
SET created_by = 'system'
WHERE created_by IS NULL OR created_by = '';

-- ================================
-- 索引维护
-- ================================

-- 重建所有索引以确保最佳性能
REINDEX;

-- 更新统计信息
ANALYZE;

-- ================================
-- 版本信息更新
-- ================================

-- 更新数据库版本信息
UPDATE app_settings 
SET setting_value = '1.1.0' 
WHERE setting_key = 'database_version';

-- 记录迁移完成时间
INSERT OR REPLACE INTO app_settings (setting_key, setting_value, setting_type, description, is_user_configurable) 
VALUES ('last_migration_date', datetime('now'), 'string', '最后迁移日期', FALSE);

-- ================================
-- 清理临时数据
-- ================================

-- 清理过期的收件箱项目（超过30天的已处理项目）
DELETE FROM inbox_items 
WHERE processing_status = 'processed' 
AND processed_at < datetime('now', '-30 days');

-- 清理过期的定期任务执行记录（超过90天）
DELETE FROM recurring_task_executions 
WHERE executed_at < datetime('now', '-90 days');

-- ================================
-- 数据库优化
-- ================================

-- 压缩数据库
VACUUM;

-- ================================
-- 完成日志
-- ================================

-- 记录迁移完成
INSERT INTO app_settings (setting_key, setting_value, setting_type, description, is_user_configurable) 
VALUES ('migration_006_completed', 'true', 'boolean', '迁移006是否完成', FALSE);
