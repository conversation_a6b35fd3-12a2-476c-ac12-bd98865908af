// 数据库配置和连接管理
// 基于SQLx的数据库连接池和配置管理

use crate::shared::errors::{AppError, AppResult};
use super::DatabaseConfig;
use sqlx::{
    sqlite::{SqliteConnectOptions, SqliteJournalMode, SqlitePoolOptions, SqliteSynchronous},
    Pool, Sqlite, SqlitePool,
};
use std::str::FromStr;
use std::time::Duration;

/// 数据库管理器
pub struct DatabaseManager {
    config: DatabaseConfig,
    pool: Option<SqlitePool>,
}

impl DatabaseManager {
    /// 创建新的数据库管理器
    pub fn new(config: DatabaseConfig) -> Self {
        Self {
            config,
            pool: None,
        }
    }

    /// 初始化数据库连接池
    pub async fn initialize(&mut self) -> AppResult<()> {
        tracing::info!("Initializing database connection pool");
        
        // 创建数据库目录
        if let Some(parent) = std::path::Path::new(&self.config.url)
            .strip_prefix("sqlite:")
            .unwrap_or(std::path::Path::new(&self.config.url))
            .parent()
        {
            std::fs::create_dir_all(parent).map_err(|e| {
                AppError::database(format!("Failed to create database directory: {}", e))
            })?;
        }

        // 创建连接选项
        let connect_options = self.create_connect_options()?;

        // 创建连接池
        let pool = SqlitePoolOptions::new()
            .max_connections(self.config.max_connections)
            .min_connections(self.config.min_connections)
            .acquire_timeout(Duration::from_secs(self.config.connection_timeout_seconds))
            .idle_timeout(Duration::from_secs(self.config.idle_timeout_seconds))
            .max_lifetime(Duration::from_secs(self.config.max_lifetime_seconds))
            .test_before_acquire(true)
            .connect_with(connect_options)
            .await
            .map_err(|e| AppError::database(format!("Failed to create connection pool: {}", e)))?;

        // 运行数据库迁移
        if self.config.enable_migrations {
            self.run_migrations(&pool).await?;
        }

        // 验证连接
        self.validate_connection(&pool).await?;

        self.pool = Some(pool);
        tracing::info!("Database connection pool initialized successfully");
        
        Ok(())
    }

    /// 创建连接选项
    fn create_connect_options(&self) -> AppResult<SqliteConnectOptions> {
        let database_url = self.config.url.strip_prefix("sqlite:").unwrap_or(&self.config.url);
        
        let mut options = SqliteConnectOptions::from_str(database_url)
            .map_err(|e| AppError::database(format!("Invalid database URL: {}", e)))?;

        // 配置SQLite选项
        options = options
            .create_if_missing(true)
            .journal_mode(SqliteJournalMode::Wal)
            .synchronous(SqliteSynchronous::Normal)
            .busy_timeout(Duration::from_secs(30))
            .pragma("cache_size", "1000")
            .pragma("temp_store", "memory")
            .pragma("mmap_size", "268435456"); // 256MB

        // 启用外键约束
        if self.config.enable_foreign_keys {
            options = options.foreign_keys(true);
        }

        Ok(options)
    }

    /// 运行数据库迁移
    async fn run_migrations(&self, pool: &SqlitePool) -> AppResult<()> {
        tracing::info!("Running database migrations");
        
        sqlx::migrate!("./migrations")
            .run(pool)
            .await
            .map_err(|e| AppError::database(format!("Migration failed: {}", e)))?;

        tracing::info!("Database migrations completed successfully");
        Ok(())
    }

    /// 验证数据库连接
    async fn validate_connection(&self, pool: &SqlitePool) -> AppResult<()> {
        tracing::debug!("Validating database connection");
        
        let row: (i64,) = sqlx::query_as("SELECT 1")
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::database(format!("Connection validation failed: {}", e)))?;

        if row.0 != 1 {
            return Err(AppError::database("Connection validation returned unexpected result"));
        }

        tracing::debug!("Database connection validated successfully");
        Ok(())
    }

    /// 获取数据库连接池
    pub fn get_pool(&self) -> AppResult<&SqlitePool> {
        self.pool
            .as_ref()
            .ok_or_else(|| AppError::database("Database pool not initialized"))
    }

    /// 获取数据库连接池的克隆
    pub fn get_pool_clone(&self) -> AppResult<SqlitePool> {
        Ok(self.get_pool()?.clone())
    }

    /// 关闭数据库连接池
    pub async fn close(&mut self) -> AppResult<()> {
        if let Some(pool) = self.pool.take() {
            tracing::info!("Closing database connection pool");
            pool.close().await;
            tracing::info!("Database connection pool closed");
        }
        Ok(())
    }

    /// 获取连接池统计信息
    pub fn get_pool_stats(&self) -> AppResult<PoolStats> {
        let pool = self.get_pool()?;
        
        Ok(PoolStats {
            size: pool.size(),
            idle: pool.num_idle() as u32,
            max_size: self.config.max_connections,
            min_size: self.config.min_connections,
        })
    }

    /// 执行健康检查
    pub async fn health_check(&self) -> AppResult<HealthStatus> {
        let pool = self.get_pool()?;
        
        let start = std::time::Instant::now();
        
        // 尝试执行简单查询
        let result = sqlx::query("SELECT 1")
            .fetch_one(pool)
            .await;

        let response_time = start.elapsed();
        
        match result {
            Ok(_) => Ok(HealthStatus {
                is_healthy: true,
                response_time_ms: response_time.as_millis() as u64,
                error_message: None,
                pool_stats: self.get_pool_stats().ok(),
            }),
            Err(e) => Ok(HealthStatus {
                is_healthy: false,
                response_time_ms: response_time.as_millis() as u64,
                error_message: Some(e.to_string()),
                pool_stats: self.get_pool_stats().ok(),
            }),
        }
    }

    /// 备份数据库
    pub async fn backup_database(&self, backup_path: &std::path::Path) -> AppResult<()> {
        tracing::info!("Starting database backup to: {:?}", backup_path);
        
        let pool = self.get_pool()?;
        
        // 创建备份目录
        if let Some(parent) = backup_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| {
                AppError::database(format!("Failed to create backup directory: {}", e))
            })?;
        }

        // 执行VACUUM INTO命令进行备份
        let backup_path_str = backup_path.to_string_lossy();
        let query = format!("VACUUM INTO '{}'", backup_path_str);
        
        sqlx::query(&query)
            .execute(pool)
            .await
            .map_err(|e| AppError::database(format!("Backup failed: {}", e)))?;

        tracing::info!("Database backup completed successfully");
        Ok(())
    }

    /// 优化数据库
    pub async fn optimize_database(&self) -> AppResult<()> {
        tracing::info!("Starting database optimization");
        
        let pool = self.get_pool()?;
        
        // 执行VACUUM命令
        sqlx::query("VACUUM")
            .execute(pool)
            .await
            .map_err(|e| AppError::database(format!("VACUUM failed: {}", e)))?;

        // 执行ANALYZE命令
        sqlx::query("ANALYZE")
            .execute(pool)
            .await
            .map_err(|e| AppError::database(format!("ANALYZE failed: {}", e)))?;

        tracing::info!("Database optimization completed successfully");
        Ok(())
    }
}

/// 连接池统计信息
#[derive(Debug, Clone)]
pub struct PoolStats {
    pub size: u32,
    pub idle: u32,
    pub max_size: u32,
    pub min_size: u32,
}

/// 健康状态
#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub is_healthy: bool,
    pub response_time_ms: u64,
    pub error_message: Option<String>,
    pub pool_stats: Option<PoolStats>,
}

/// 数据库工具函数
pub struct DatabaseUtils;

impl DatabaseUtils {
    /// 检查表是否存在
    pub async fn table_exists(pool: &SqlitePool, table_name: &str) -> AppResult<bool> {
        let row: (i64,) = sqlx::query_as(
            "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=?",
        )
        .bind(table_name)
        .fetch_one(pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check table existence: {}", e)))?;

        Ok(row.0 > 0)
    }

    /// 获取表的行数
    pub async fn get_table_row_count(pool: &SqlitePool, table_name: &str) -> AppResult<i64> {
        let query = format!("SELECT COUNT(*) FROM {}", table_name);
        let row: (i64,) = sqlx::query_as(&query)
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get row count: {}", e)))?;

        Ok(row.0)
    }

    /// 获取数据库大小
    pub async fn get_database_size(pool: &SqlitePool) -> AppResult<i64> {
        let row: (i64,) = sqlx::query_as("SELECT page_count * page_size FROM pragma_page_count(), pragma_page_size()")
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get database size: {}", e)))?;

        Ok(row.0)
    }

    /// 获取数据库版本信息
    pub async fn get_database_version(pool: &SqlitePool) -> AppResult<String> {
        let row: (String,) = sqlx::query_as("SELECT sqlite_version()")
            .fetch_one(pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get database version: {}", e)))?;

        Ok(row.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_database_manager_creation() {
        let config = DatabaseConfig::default();
        let manager = DatabaseManager::new(config);
        assert!(manager.pool.is_none());
    }

    #[tokio::test]
    async fn test_connect_options_creation() {
        let config = DatabaseConfig::default();
        let manager = DatabaseManager::new(config);
        let options = manager.create_connect_options();
        assert!(options.is_ok());
    }

    #[test]
    fn test_pool_stats() {
        let stats = PoolStats {
            size: 5,
            idle: 3,
            max_size: 10,
            min_size: 1,
        };
        assert_eq!(stats.size, 5);
        assert_eq!(stats.idle, 3);
    }

    #[test]
    fn test_health_status() {
        let status = HealthStatus {
            is_healthy: true,
            response_time_ms: 50,
            error_message: None,
            pool_stats: None,
        };
        assert!(status.is_healthy);
        assert_eq!(status.response_time_ms, 50);
    }
}
