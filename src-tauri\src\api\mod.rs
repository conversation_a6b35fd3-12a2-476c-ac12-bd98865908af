// API接口层
// 实现Tauri命令，连接前端和后端

pub mod commands;
pub mod handlers;
pub mod dto;
pub mod common;

// 重新导出
pub use commands::*;
pub use handlers::*;
pub use dto::*;
pub use common::*;

use crate::application::services::{UserService, ProjectService, TaskService, AreaService};
use crate::shared::errors::AppResult;
use std::sync::Arc;
use tauri::State;

/// API状态管理
pub struct ApiState {
    pub user_service: Arc<UserService>,
    pub project_service: Arc<ProjectService>,
    pub task_service: Arc<TaskService>,
    pub area_service: Arc<AreaService>,
}

impl ApiState {
    /// 创建新的API状态
    pub fn new(
        user_service: Arc<UserService>,
        project_service: Arc<ProjectService>,
        task_service: Arc<TaskService>,
        area_service: Arc<AreaService>,
    ) -> Self {
        Self {
            user_service,
            project_service,
            task_service,
            area_service,
        }
    }
}

/// API响应包装器
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub message: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub request_id: Option<String>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: None,
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 创建成功响应带消息
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: Some(message),
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 创建错误响应
    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
            message: None,
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// 设置请求ID
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
}

/// 从AppResult转换为ApiResponse
impl<T> From<AppResult<T>> for ApiResponse<T> {
    fn from(result: AppResult<T>) -> Self {
        match result {
            Ok(data) => ApiResponse::success(data),
            Err(error) => ApiResponse::error(error.to_string()),
        }
    }
}

/// 分页请求参数
#[derive(Debug, Clone, serde::Deserialize)]
pub struct PaginationRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

impl Default for PaginationRequest {
    fn default() -> Self {
        Self {
            page: Some(1),
            page_size: Some(20),
        }
    }
}

impl PaginationRequest {
    /// 获取页码
    pub fn page(&self) -> u32 {
        self.page.unwrap_or(1)
    }

    /// 获取页面大小
    pub fn page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(100) // 限制最大页面大小
    }
}

/// 搜索请求参数
#[derive(Debug, Clone, serde::Deserialize)]
pub struct SearchRequest {
    pub keyword: String,
    #[serde(flatten)]
    pub pagination: PaginationRequest,
}

/// API工具函数
pub struct ApiUtils;

impl ApiUtils {
    /// 处理API调用
    pub async fn handle_api_call<F, T, R>(
        operation: F,
        operation_name: &str,
    ) -> ApiResponse<T>
    where
        F: std::future::Future<Output = AppResult<R>>,
        T: From<R>,
    {
        let start = std::time::Instant::now();
        
        match operation.await {
            Ok(result) => {
                let duration = start.elapsed();
                tracing::debug!(
                    operation = %operation_name,
                    duration_ms = %duration.as_millis(),
                    "API operation completed successfully"
                );
                ApiResponse::success(T::from(result))
            }
            Err(error) => {
                let duration = start.elapsed();
                tracing::error!(
                    operation = %operation_name,
                    duration_ms = %duration.as_millis(),
                    error = %error,
                    "API operation failed"
                );
                ApiResponse::error(error.to_string())
            }
        }
    }

    /// 验证请求参数
    pub fn validate_required_string<'a>(value: &'a Option<String>, field_name: &str) -> AppResult<&'a String> {
        value.as_ref().ok_or_else(|| {
            crate::shared::errors::AppError::validation(format!("{} is required", field_name))
        })
    }

    /// 验证ID格式
    pub fn validate_id(id: &str) -> AppResult<()> {
        if id.trim().is_empty() {
            return Err(crate::shared::errors::AppError::validation("ID不能为空"));
        }
        if id.len() > 50 {
            return Err(crate::shared::errors::AppError::validation("ID长度不能超过50个字符"));
        }
        Ok(())
    }

    /// 验证分页参数
    pub fn validate_pagination(pagination: &PaginationRequest) -> AppResult<()> {
        let page = pagination.page();
        let page_size = pagination.page_size();

        if page == 0 {
            return Err(crate::shared::errors::AppError::validation("页码必须大于0"));
        }

        if page_size == 0 || page_size > 100 {
            return Err(crate::shared::errors::AppError::validation("页面大小必须在1-100之间"));
        }

        Ok(())
    }

    /// 记录API访问日志
    pub fn log_api_access(
        command_name: &str,
        user_id: Option<&str>,
        request_id: &str,
        success: bool,
        duration_ms: u64,
    ) {
        if success {
            tracing::info!(
                command = %command_name,
                user_id = ?user_id,
                request_id = %request_id,
                duration_ms = %duration_ms,
                "API command executed successfully"
            );
        } else {
            tracing::warn!(
                command = %command_name,
                user_id = ?user_id,
                request_id = %request_id,
                duration_ms = %duration_ms,
                "API command failed"
            );
        }
    }
}

/// API错误处理宏
#[macro_export]
macro_rules! handle_api_error {
    ($result:expr) => {
        match $result {
            Ok(value) => value,
            Err(error) => {
                tracing::error!(error = %error, "API operation failed");
                return ApiResponse::error(error.to_string());
            }
        }
    };
}

/// API成功响应宏
#[macro_export]
macro_rules! api_success {
    ($data:expr) => {
        ApiResponse::success($data)
    };
    ($data:expr, $message:expr) => {
        ApiResponse::success_with_message($data, $message.to_string())
    };
}

/// API错误响应宏
#[macro_export]
macro_rules! api_error {
    ($error:expr) => {
        ApiResponse::error($error.to_string())
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pagination_request_defaults() {
        let pagination = PaginationRequest::default();
        assert_eq!(pagination.page(), 1);
        assert_eq!(pagination.page_size(), 20);
    }

    #[test]
    fn test_pagination_request_limits() {
        let pagination = PaginationRequest {
            page: Some(0),
            page_size: Some(200),
        };
        assert_eq!(pagination.page(), 0); // 原始值
        assert_eq!(pagination.page_size(), 100); // 被限制到最大值
    }

    #[test]
    fn test_api_response_success() {
        let response = ApiResponse::success("test data".to_string());
        assert!(response.success);
        assert_eq!(response.data, Some("test data".to_string()));
        assert!(response.error.is_none());
    }

    #[test]
    fn test_api_response_error() {
        let response: ApiResponse<String> = ApiResponse::error("test error".to_string());
        assert!(!response.success);
        assert!(response.data.is_none());
        assert_eq!(response.error, Some("test error".to_string()));
    }

    #[test]
    fn test_api_utils_validate_id() {
        assert!(ApiUtils::validate_id("valid_id").is_ok());
        assert!(ApiUtils::validate_id("").is_err());
        assert!(ApiUtils::validate_id(&"a".repeat(51)).is_err());
    }

    #[test]
    fn test_api_utils_validate_pagination() {
        let valid_pagination = PaginationRequest {
            page: Some(1),
            page_size: Some(20),
        };
        assert!(ApiUtils::validate_pagination(&valid_pagination).is_ok());

        let invalid_pagination = PaginationRequest {
            page: Some(0),
            page_size: Some(20),
        };
        assert!(ApiUtils::validate_pagination(&invalid_pagination).is_err());
    }
}
