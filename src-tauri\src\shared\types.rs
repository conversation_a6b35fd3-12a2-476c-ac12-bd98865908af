// 共享类型定义
// 定义应用中通用的类型、常量和枚举

use chrono::{DateTime, Utc, NaiveDate};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 实体ID类型
pub type EntityId = String;

/// 时间戳类型
pub type Timestamp = DateTime<Utc>;

/// 日期类型
pub type Date = NaiveDate;

/// 项目状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ProjectStatus {
    NotStarted,
    InProgress,
    AtRisk,
    Paused,
    Completed,
    Archived,
}

impl std::fmt::Display for ProjectStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProjectStatus::NotStarted => write!(f, "not_started"),
            ProjectStatus::InProgress => write!(f, "in_progress"),
            ProjectStatus::AtRisk => write!(f, "at_risk"),
            ProjectStatus::Paused => write!(f, "paused"),
            ProjectStatus::Completed => write!(f, "completed"),
            ProjectStatus::Archived => write!(f, "archived"),
        }
    }
}

impl ProjectStatus {
    /// 从字符串创建ProjectStatus
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "not_started" => Some(ProjectStatus::NotStarted),
            "in_progress" => Some(ProjectStatus::InProgress),
            "at_risk" => Some(ProjectStatus::AtRisk),
            "paused" => Some(ProjectStatus::Paused),
            "completed" => Some(ProjectStatus::Completed),
            "archived" => Some(ProjectStatus::Archived),
            _ => None,
        }
    }
}

impl ProjectStatus {
    pub fn all() -> Vec<Self> {
        vec![
            Self::NotStarted,
            Self::InProgress,
            Self::AtRisk,
            Self::Paused,
            Self::Completed,
            Self::Archived,
        ]
    }

    pub fn is_active(&self) -> bool {
        matches!(self, Self::InProgress | Self::AtRisk | Self::Paused)
    }

    pub fn is_completed(&self) -> bool {
        matches!(self, Self::Completed | Self::Archived)
    }
}

/// 任务状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum TaskStatus {
    Todo,
    InProgress,
    Waiting,
    Completed,
    Cancelled,
}

impl std::fmt::Display for TaskStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TaskStatus::Todo => write!(f, "todo"),
            TaskStatus::InProgress => write!(f, "in_progress"),
            TaskStatus::Waiting => write!(f, "waiting"),
            TaskStatus::Completed => write!(f, "completed"),
            TaskStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

impl TaskStatus {
    /// 从字符串创建TaskStatus
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "todo" => Some(TaskStatus::Todo),
            "in_progress" => Some(TaskStatus::InProgress),
            "waiting" => Some(TaskStatus::Waiting),
            "completed" => Some(TaskStatus::Completed),
            "cancelled" => Some(TaskStatus::Cancelled),
            _ => None,
        }
    }
}

impl TaskStatus {
    pub fn all() -> Vec<Self> {
        vec![
            Self::Todo,
            Self::InProgress,
            Self::Waiting,
            Self::Completed,
            Self::Cancelled,
        ]
    }

    pub fn is_active(&self) -> bool {
        matches!(self, Self::Todo | Self::InProgress | Self::Waiting)
    }

    pub fn is_completed(&self) -> bool {
        matches!(self, Self::Completed)
    }
}

/// 优先级枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum Priority {
    Low = 1,
    Medium = 2,
    High = 3,
    Urgent = 4,
    Critical = 5,
}

impl std::fmt::Display for Priority {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Priority::Low => write!(f, "low"),
            Priority::Medium => write!(f, "medium"),
            Priority::High => write!(f, "high"),
            Priority::Urgent => write!(f, "urgent"),
            Priority::Critical => write!(f, "critical"),
        }
    }
}

impl Priority {
    /// 转换为i32值
    pub fn to_i32(&self) -> i32 {
        match self {
            Priority::Low => 1,
            Priority::Medium => 2,
            Priority::High => 3,
            Priority::Urgent => 4,
            Priority::Critical => 5,
        }
    }

    /// 从i32值创建Priority
    pub fn from_i32(value: i32) -> Option<Self> {
        match value {
            1 => Some(Priority::Low),
            2 => Some(Priority::Medium),
            3 => Some(Priority::High),
            4 => Some(Priority::Urgent),
            5 => Some(Priority::Critical),
            _ => None,
        }
    }
}

impl Default for Priority {
    fn default() -> Self {
        Priority::Medium
    }
}

impl Priority {
    pub fn all() -> Vec<Self> {
        vec![
            Self::Low,
            Self::Medium,
            Self::High,
            Self::Urgent,
            Self::Critical,
        ]
    }

    // 删除重复的方法定义，使用上面已定义的版本
}

/// 指标方向枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum MetricDirection {
    Increase,  // 增长型指标
    Decrease,  // 减少型指标
    Maintain,  // 维持型指标
}

/// 频率枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum Frequency {
    Daily,
    Weekly,
    Monthly,
    Quarterly,
    Yearly,
}

/// 资源类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ResourceType {
    File,
    Folder,
    Url,
    Note,
    Reference,
}

/// 收件箱项目类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum InboxItemType {
    QuickNote,
    Idea,
    Task,
    Reference,
    MeetingNote,
}

/// 处理状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum ProcessingStatus {
    Unprocessed,
    Processing,
    Processed,
    Archived,
}

/// 分页参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub page_size: u32,
    pub total: Option<u64>,
}

impl Default for Pagination {
    fn default() -> Self {
        Self {
            page: 1,
            page_size: 20,
            total: None,
        }
    }
}

impl Pagination {
    pub fn new(page: u32, page_size: u32) -> Self {
        Self {
            page,
            page_size,
            total: None,
        }
    }

    pub fn offset(&self) -> u32 {
        (self.page - 1) * self.page_size
    }

    pub fn limit(&self) -> u32 {
        self.page_size
    }
}

/// 排序参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Sort {
    pub field: String,
    pub direction: SortDirection,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SortDirection {
    Asc,
    Desc,
}

/// 筛选参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Filter {
    pub field: String,
    pub operator: FilterOperator,
    pub value: FilterValue,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum FilterOperator {
    Eq,      // 等于
    Ne,      // 不等于
    Gt,      // 大于
    Gte,     // 大于等于
    Lt,      // 小于
    Lte,     // 小于等于
    Like,    // 模糊匹配
    In,      // 在列表中
    NotIn,   // 不在列表中
    IsNull,  // 为空
    NotNull, // 不为空
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum FilterValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Array(Vec<String>),
    Null,
}

/// 查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryParams {
    pub pagination: Option<Pagination>,
    pub sort: Option<Vec<Sort>>,
    pub filters: Option<Vec<Filter>>,
    pub search: Option<String>,
}

impl Default for QueryParams {
    fn default() -> Self {
        Self {
            pagination: Some(Pagination::default()),
            sort: None,
            filters: None,
            search: None,
        }
    }
}

/// 统计数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Statistics {
    pub total_count: u64,
    pub active_count: u64,
    pub completed_count: u64,
    pub completion_rate: f64,
    pub trend: Option<TrendData>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendData {
    pub period: String,
    pub values: Vec<TrendPoint>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendPoint {
    pub date: Date,
    pub value: f64,
}

/// 配置键值对
pub type ConfigMap = HashMap<String, ConfigValue>;

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ConfigValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Object(ConfigMap),
    Array(Vec<ConfigValue>),
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_project_status() {
        let status = ProjectStatus::InProgress;
        assert!(status.is_active());
        assert!(!status.is_completed());
    }

    #[test]
    fn test_priority_conversion() {
        let priority = Priority::High;
        assert_eq!(priority.to_i32(), 3);
        assert_eq!(Priority::from_i32(3), Some(Priority::High));
    }

    #[test]
    fn test_pagination() {
        let pagination = Pagination::new(2, 10);
        assert_eq!(pagination.offset(), 10);
        assert_eq!(pagination.limit(), 10);
    }
}
