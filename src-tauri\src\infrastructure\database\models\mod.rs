// 数据库模型模块
// 包含所有数据库表对应的数据模型

use chrono::{DateTime, Utc, NaiveDate};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

// 核心模型模块
pub mod user_model;
pub mod project_model;
pub mod task_model;
pub mod area_model;

// 扩展功能模型模块
pub mod review_model;
pub mod recurring_task_model;
pub mod checklist_model;

// 重新导出核心模型
pub use user_model::*;
pub use project_model::*;
pub use task_model::*;
pub use area_model::*;

// 重新导出扩展功能模型
pub use review_model::*;
pub use recurring_task_model::*;
pub use checklist_model::*;

// 扩展模型 - 这些模型用于未来功能扩展

/// 指标数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MetricModel {
    pub id: String,
    pub owner_type: String,
    pub owner_id: String,
    pub name: String,
    pub description: Option<String>,
    pub current_value: f64,
    pub target_value: Option<f64>,
    pub unit: Option<String>,
    pub direction: String,
    pub frequency: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 指标记录数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MetricRecordModel {
    pub id: String,
    pub metric_id: String,
    pub recorded_value: f64,
    pub note: Option<String>,
    pub source: String,
    pub confidence_score: Option<f64>,
    pub recorded_date: NaiveDate,
    pub recorded_by: String,
    pub created_at: DateTime<Utc>,
}

/// 习惯数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HabitModel {
    pub id: String,
    pub area_id: String,
    pub name: String,
    pub description: Option<String>,
    pub target_frequency: i32,
    pub frequency_unit: String,
    pub difficulty_level: i32,
    pub reward_points: i32,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 习惯记录数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct HabitRecordModel {
    pub id: String,
    pub habit_id: String,
    pub completed_date: NaiveDate,
    pub completion_value: i32,
    pub note: Option<String>,
    pub mood_rating: Option<i32>,
    pub created_at: DateTime<Utc>,
}

/// 资源数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ResourceModel {
    pub id: String,
    pub resource_type: String,
    pub path: String,
    pub title: Option<String>,
    pub mime_type: Option<String>,
    pub file_size: Option<i64>,
    pub file_hash: Option<String>,
    pub metadata: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_accessed_at: Option<DateTime<Utc>>,
}

/// 引用数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ReferenceModel {
    pub id: String,
    pub source_entity_type: String,
    pub source_entity_id: String,
    pub target_resource_id: String,
    pub reference_type: String,
    pub context: Option<String>,
    pub display_text: Option<String>,
    pub line_number: Option<i32>,
    pub created_at: DateTime<Utc>,
}

/// 标签数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TagModel {
    pub id: String,
    pub name: String,
    pub color_hex: Option<String>,
    pub description: Option<String>,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
}

/// 标签关联数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TaggableModel {
    pub tag_id: String,
    pub taggable_type: String,
    pub taggable_id: String,
    pub created_at: DateTime<Utc>,
}

/// 收件箱项目数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct InboxItemModel {
    pub id: String,
    pub content: String,
    pub item_type: String,
    pub source: String,
    pub processing_status: String,
    pub processed_into_type: Option<String>,
    pub processed_into_id: Option<String>,
    pub priority: i32,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub processed_at: Option<DateTime<Utc>>,
    pub processed_by: Option<String>,
}

/// 应用设置数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AppSettingModel {
    pub setting_key: String,
    pub setting_value: String,
    pub setting_type: String,
    pub description: Option<String>,
    pub is_user_configurable: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 用户设置数据库模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserSettingModel {
    pub id: String,
    pub user_id: String,
    pub setting_key: String,
    pub setting_value: String,
    pub setting_type: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 数据库查询结果包装器
#[derive(Debug, Clone)]
pub struct QueryResult<T> {
    pub data: Vec<T>,
    pub total_count: Option<u64>,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

impl<T> QueryResult<T> {
    pub fn new(data: Vec<T>) -> Self {
        Self {
            data,
            total_count: None,
            page: None,
            page_size: None,
        }
    }

    pub fn with_pagination(data: Vec<T>, total_count: u64, page: u32, page_size: u32) -> Self {
        Self {
            data,
            total_count: Some(total_count),
            page: Some(page),
            page_size: Some(page_size),
        }
    }

    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    pub fn len(&self) -> usize {
        self.data.len()
    }

    pub fn has_more_pages(&self) -> bool {
        if let (Some(total), Some(page), Some(page_size)) = (self.total_count, self.page, self.page_size) {
            let total_pages = (total as f64 / page_size as f64).ceil() as u32;
            page < total_pages
        } else {
            false
        }
    }
}

impl<T> IntoIterator for QueryResult<T> {
    type Item = T;
    type IntoIter = std::vec::IntoIter<T>;

    fn into_iter(self) -> Self::IntoIter {
        self.data.into_iter()
    }
}

impl<T> std::ops::Deref for QueryResult<T> {
    type Target = Vec<T>;

    fn deref(&self) -> &Self::Target {
        &self.data
    }
}

impl<T> std::ops::DerefMut for QueryResult<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.data
    }
}
