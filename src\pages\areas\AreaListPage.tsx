// 领域列表页面
// 显示用户的所有领域，支持管理和组织

import { Component, createSignal, createEffect, Show, For } from 'solid-js';
import { useNavigate } from '@solidjs/router';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface Area {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  project_count?: number;
  task_count?: number;
}

export const AreaListPage: Component = () => {
  const navigate = useNavigate();
  
  const [areas, setAreas] = createSignal<Area[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [searchQuery, setSearchQuery] = createSignal('');
  const [showInactive, setShowInactive] = createSignal(false);
  const [viewMode, setViewMode] = createSignal<'grid' | 'list'>('grid');

  // 加载领域列表
  const loadAreas = async () => {
    try {
      setLoading(true);

      let response;
      if (searchQuery()) {
        // 如果有搜索查询，使用搜索API
        response = await api.searchAreas(searchQuery(), {
          page: 1,
          page_size: 100,
        });
      } else {
        // 否则使用列表API
        response = await api.listAreas({
          page: 1,
          page_size: 100,
        });
      }

      // 新的API返回数组而不是分页对象
      if (Array.isArray(response)) {
        let filteredAreas = response;

        // 客户端过滤非活跃领域
        if (!showInactive()) {
          filteredAreas = filteredAreas.filter(area => area.is_active);
        }

        setAreas(filteredAreas);
      } else {
        setAreas([]);
      }
    } catch (error) {
      console.error('Failed to load areas:', error);
      toast.error('加载领域列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  createEffect(() => {
    loadAreas();
  });

  // 筛选领域
  const filteredAreas = () => {
    return areas().filter(area => {
      const matchesSearch = !searchQuery() || 
        area.name.toLowerCase().includes(searchQuery().toLowerCase()) ||
        (area.description && area.description.toLowerCase().includes(searchQuery().toLowerCase()));
      
      const matchesActive = showInactive() || area.is_active;
      
      return matchesSearch && matchesActive;
    });
  };

  // 切换领域激活状态
  const toggleAreaStatus = async (areaId: string, isActive: boolean) => {
    try {
      if (isActive) {
        await api.deactivateArea(areaId);
      } else {
        await api.activateArea(areaId);
      }
      
      await loadAreas();
      toast.success(`领域已${isActive ? '停用' : '激活'}`);
    } catch (error) {
      toast.error(`${isActive ? '停用' : '激活'}领域失败`);
    }
  };

  // 删除领域
  const deleteArea = async (areaId: string, areaName: string) => {
    if (!confirm(`确定要删除领域"${areaName}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await api.deleteArea(areaId);
      await loadAreas();
      toast.success('领域已删除');
    } catch (error) {
      toast.error('删除领域失败');
    }
  };

  // 获取默认颜色
  const getDefaultColor = (index: number) => {
    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
      '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
    ];
    return colors[index % colors.length];
  };

  return (
    <MainLayout title="领域管理">
      <div class="space-y-6">
        {/* 页面头部 */}
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">领域管理</h1>
            <p class="mt-2 text-sm text-gray-700">
              组织和管理您的生活领域，让项目和任务更有条理
            </p>
          </div>
          <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            {/* 视图切换 */}
            <div class="flex rounded-lg border border-gray-300">
              <button
                class={`px-3 py-2 text-sm font-medium rounded-l-lg ${
                  viewMode() === 'grid'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setViewMode('grid')}
              >
                网格
              </button>
              <button
                class={`px-3 py-2 text-sm font-medium rounded-r-lg border-l ${
                  viewMode() === 'list'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setViewMode('list')}
              >
                列表
              </button>
            </div>
            
            <Button onClick={() => navigate('/areas/new')}>
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              新建领域
            </Button>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div class="flex-1">
              <Input
                type="search"
                placeholder="搜索领域名称或描述..."
                value={searchQuery()}
                onInput={(e) => setSearchQuery(e.currentTarget.value)}
                leftIcon={() => (
                  <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              />
            </div>

            {/* 显示选项 */}
            <div class="flex items-center">
              <label class="flex items-center">
                <input
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  checked={showInactive()}
                  onChange={(e) => setShowInactive(e.currentTarget.checked)}
                />
                <span class="ml-2 text-sm text-gray-700">显示已停用</span>
              </label>
            </div>
          </div>
        </div>

        {/* 领域列表 */}
        <Show
          when={!loading()}
          fallback={
            <div class="bg-white p-8 rounded-lg shadow text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p class="mt-2 text-gray-500">加载中...</p>
            </div>
          }
        >
          <Show
            when={filteredAreas().length > 0}
            fallback={
              <div class="bg-white p-8 rounded-lg shadow text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无领域</h3>
                <p class="mt-1 text-sm text-gray-500">开始创建您的第一个领域吧</p>
                <div class="mt-6">
                  <Button onClick={() => navigate('/areas/new')}>
                    新建领域
                  </Button>
                </div>
              </div>
            }
          >
            <Show
              when={viewMode() === 'grid'}
              fallback={
                // 列表视图
                <div class="bg-white shadow rounded-lg overflow-hidden">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          领域
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          项目数
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          任务数
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          创建时间
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                          <span class="sr-only">操作</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <For each={filteredAreas()}>
                        {(area, index) => (
                          <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="flex items-center">
                                <div
                                  class="flex-shrink-0 h-10 w-10 rounded-lg flex items-center justify-center text-white font-medium"
                                  style={{ 'background-color': area.color || getDefaultColor(index()) }}
                                >
                                  {area.icon || area.name.charAt(0).toUpperCase()}
                                </div>
                                <div class="ml-4">
                                  <div class="text-sm font-medium text-gray-900">
                                    <button
                                      type="button"
                                      class="hover:text-blue-600"
                                      onClick={() => navigate(`/areas/${area.id}`)}
                                    >
                                      {area.name}
                                    </button>
                                  </div>
                                  <Show when={area.description}>
                                    <div class="text-sm text-gray-500 truncate max-w-xs">
                                      {area.description}
                                    </div>
                                  </Show>
                                </div>
                              </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                area.is_active 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {area.is_active ? '激活' : '停用'}
                              </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {area.project_count || 0}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {area.task_count || 0}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(area.created_at).toLocaleDateString('zh-CN')}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div class="flex items-center space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => navigate(`/areas/${area.id}`)}
                                >
                                  查看
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => navigate(`/areas/${area.id}/edit`)}
                                >
                                  编辑
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleAreaStatus(area.id, area.is_active)}
                                >
                                  {area.is_active ? '停用' : '激活'}
                                </Button>
                              </div>
                            </td>
                          </tr>
                        )}
                      </For>
                    </tbody>
                  </table>
                </div>
              }
            >
              {/* 网格视图 */}
              <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                <For each={filteredAreas()}>
                  {(area, index) => (
                    <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                      <div class="p-6">
                        <div class="flex items-center">
                          <div
                            class="flex-shrink-0 h-12 w-12 rounded-lg flex items-center justify-center text-white font-medium text-lg"
                            style={{ 'background-color': area.color || getDefaultColor(index()) }}
                          >
                            {area.icon || area.name.charAt(0).toUpperCase()}
                          </div>
                          <div class="ml-4 flex-1">
                            <h3 class="text-lg font-medium text-gray-900 truncate">
                              <button
                                type="button"
                                class="hover:text-blue-600"
                                onClick={() => navigate(`/areas/${area.id}`)}
                              >
                                {area.name}
                              </button>
                            </h3>
                            <div class="flex items-center mt-1">
                              <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                area.is_active 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {area.is_active ? '激活' : '停用'}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <Show when={area.description}>
                          <p class="mt-3 text-sm text-gray-500 line-clamp-2">
                            {area.description}
                          </p>
                        </Show>
                        
                        <div class="mt-4 flex items-center justify-between text-sm text-gray-500">
                          <div class="flex space-x-4">
                            <span>{area.project_count || 0} 项目</span>
                            <span>{area.task_count || 0} 任务</span>
                          </div>
                        </div>
                        
                        <div class="mt-4 flex items-center justify-between">
                          <div class="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/areas/${area.id}/edit`)}
                            >
                              编辑
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleAreaStatus(area.id, area.is_active)}
                            >
                              {area.is_active ? '停用' : '激活'}
                            </Button>
                          </div>
                          
                          <button
                            type="button"
                            class="text-gray-400 hover:text-red-500"
                            onClick={() => deleteArea(area.id, area.name)}
                          >
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </For>
              </div>
            </Show>
          </Show>
        </Show>

        {/* 统计信息 */}
        <div class="bg-white p-6 rounded-lg shadow">
          <h3 class="text-lg font-medium text-gray-900 mb-4">统计概览</h3>
          <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div class="bg-blue-50 overflow-hidden rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总领域数</dt>
                      <dd class="text-lg font-medium text-gray-900">{areas().length}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-green-50 overflow-hidden rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">激活领域</dt>
                      <dd class="text-lg font-medium text-gray-900">
                        {areas().filter(a => a.is_active).length}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-yellow-50 overflow-hidden rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总项目数</dt>
                      <dd class="text-lg font-medium text-gray-900">
                        {areas().reduce((sum, area) => sum + (area.project_count || 0), 0)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-purple-50 overflow-hidden rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总任务数</dt>
                      <dd class="text-lg font-medium text-gray-900">
                        {areas().reduce((sum, area) => sum + (area.task_count || 0), 0)}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default AreaListPage;
