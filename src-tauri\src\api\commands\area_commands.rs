// 领域相关的Tauri命令
// 实现领域管理的前后端通信

use crate::api::common::{ApiState, CommandUtils, PaginationParams, SearchParams};
use crate::api::dto::{CreateAreaRequestDto, UpdateAreaRequestDto, AreaResponseDto};
use crate::domain::entities::area::CreateAreaData;
use crate::execute_simple_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

/// 创建领域命令
#[command]
pub async fn area_create(
    request: CreateAreaRequestDto,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<AreaResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "area_create",
        Some(user_id),
        async {
            let create_data = CreateAreaData {
                name: request.name,
                description: request.description,
                standard: request.standard,
                icon_name: request.icon_name,
                color_hex: request.color_hex,
                sort_order: request.sort_order,
                created_by: user_id,
            };

            let area_result = state.area_service.create_area(&context, create_data).await?;
            match area_result.data {
                Some(area) => Ok(AreaResponseDto::from(area)),
                None => Err(crate::shared::errors::AppError::internal_error("领域创建失败"))
            }
        }
    )
}

/// 获取领域命令
#[command]
pub async fn area_get(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<AreaResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "area_get",
        Some(user_id),
        async {
            let area_result = state.area_service.get_area(&context, &area_id).await?;
            match area_result.data {
                Some(area) => Ok(AreaResponseDto::from(area)),
                None => Err(crate::shared::errors::AppError::not_found("领域不存在"))
            }
        }
    )
}

/// 更新领域命令
#[command]
pub async fn area_update(
    area_id: String,
    request: UpdateAreaRequestDto,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<AreaResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "area_update",
        Some(user_id),
        async {
            let area_result = state.area_service.update_area(&context, &area_id, request).await?;
            match area_result.data {
                Some(area) => Ok(AreaResponseDto::from(area)),
                None => Err(crate::shared::errors::AppError::not_found("领域不存在"))
            }
        }
    )
}

/// 删除领域命令
#[command]
pub async fn area_delete(
    area_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "area_delete",
        Some(user_id),
        async {
            let result = state.area_service.delete_area(&context, &area_id).await?;
            Ok(())
        }
    )
}

/// 领域列表命令
#[command]
pub async fn area_list(
    pagination: Option<PaginationParams>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<AreaResponseDto>, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));
    let pagination = pagination.unwrap_or_default();

    execute_simple_command!(
        "area_list",
        Some(user_id),
        async {
            let areas_result = state.area_service.list_user_areas(&context, pagination.page, pagination.page_size, false).await?;
            match areas_result.data {
                Some(areas) => Ok(areas.into_iter().map(AreaResponseDto::from).collect()),
                None => Ok(Vec::new())
            }
        }
    )
}

/// 搜索领域命令
#[command]
pub async fn area_search(
    search_params: SearchParams,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<AreaResponseDto>, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "area_search",
        Some(user_id),
        async {
            let areas_result = state.area_service.search_areas(&context, &search_params.keyword.unwrap_or_default(), search_params.pagination.page, search_params.pagination.page_size).await?;
            match areas_result.data {
                Some(areas) => Ok(areas.into_iter().map(AreaResponseDto::from).collect()),
                None => Ok(Vec::new())
            }
        }
    )
}

/// 记录习惯命令
#[command]
pub async fn habit_record(
    area_id: String,
    habit_name: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "habit_record",
        Some(user_id),
        async {
            let result = state.area_service.record_habit(&context, &area_id, &habit_name).await?;
            Ok(())
        }
    )
}

/// 获取习惯连续记录命令
#[command]
pub async fn habit_get_streak(
    area_id: String,
    habit_name: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<u32, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "habit_get_streak",
        Some(user_id),
        async {
            let streak_result = state.area_service.get_habit_streak(&context, &area_id, &habit_name).await?;
            match streak_result.data {
                Some(streak) => Ok(streak),
                None => Ok(0)
            }
        }
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_area_commands_compilation() {
        // 这个测试确保所有命令都能正确编译
        // 实际的功能测试应该在集成测试中进行
        assert!(true);
    }
}
