// 任务相关的Tauri命令
// 实现任务管理的前后端通信

use crate::api::common::{ApiState, CommandUtils, PaginationParams, SearchParams};
use crate::api::dto::{CreateTaskRequestDto, UpdateTaskRequestDto, TaskResponseDto};
use crate::domain::entities::task::CreateTaskData;
use crate::execute_simple_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

/// 创建任务命令
#[command]
pub async fn task_create(
    request: CreateTaskRequestDto,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_create",
        Some(user_id),
        async {
            let create_data = CreateTaskData {
                title: request.title,
                description: request.description,
                priority: request.priority,
                parent_task_id: request.parent_task_id,
                project_id: request.project_id,
                area_id: request.area_id,
                assigned_to: request.assigned_to,
                due_date: request.due_date,
                estimated_minutes: request.estimated_minutes,
                created_by: user_id,
            };

            let task_result = state.task_service.create_task(&context, create_data).await?;
            match task_result.data {
                Some(task) => Ok(TaskResponseDto::from(task)),
                None => Err(crate::shared::errors::AppError::internal_error("任务创建失败"))
            }
        }
    )
}

/// 获取任务命令
#[command]
pub async fn task_get(
    task_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_get",
        Some(user_id),
        async {
            let task_result = state.task_service.get_task(&context, &task_id).await?;
            match task_result.data {
                Some(task) => Ok(TaskResponseDto::from(task)),
                None => Err(crate::shared::errors::AppError::not_found("任务不存在"))
            }
        }
    )
}

/// 更新任务命令
#[command]
pub async fn task_update(
    task_id: String,
    request: UpdateTaskRequestDto,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_update",
        Some(user_id),
        async {
            let task_result = state.task_service.update_task(&context, &task_id, request).await?;
            match task_result.data {
                Some(task) => Ok(TaskResponseDto::from(task)),
                None => Err(crate::shared::errors::AppError::not_found("任务不存在"))
            }
        }
    )
}

/// 删除任务命令
#[command]
pub async fn task_delete(
    task_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_delete",
        Some(user_id),
        async {
            let result = state.task_service.delete_task(&context, &task_id).await?;
            Ok(())
        }
    )
}

/// 任务列表命令
#[command]
pub async fn task_list(
    pagination: Option<PaginationParams>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<TaskResponseDto>, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));
    let pagination = pagination.unwrap_or_default();

    execute_simple_command!(
        "task_list",
        Some(user_id),
        async {
            let tasks_result = state.task_service.list_user_tasks(&context, pagination.page, pagination.page_size, None).await?;
            match tasks_result.data {
                Some(tasks) => Ok(tasks.into_iter().map(TaskResponseDto::from).collect()),
                None => Ok(Vec::new())
            }
        }
    )
}

/// 完成任务命令
#[command]
pub async fn task_complete(
    task_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_complete",
        Some(user_id),
        async {
            let task_result = state.task_service.complete_task(&context, &task_id).await?;
            match task_result.data {
                Some(task) => Ok(TaskResponseDto::from(task)),
                None => Err(crate::shared::errors::AppError::not_found("任务不存在"))
            }
        }
    )
}

/// 搜索任务命令
#[command]
pub async fn task_search(
    search_params: SearchParams,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<TaskResponseDto>, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_search",
        Some(user_id),
        async {
            let tasks_result = state.task_service.search_tasks(&context, &search_params.keyword.unwrap_or_default(), search_params.pagination.page, search_params.pagination.page_size).await?;
            match tasks_result.data {
                Some(tasks) => Ok(tasks.into_iter().map(TaskResponseDto::from).collect()),
                None => Ok(Vec::new())
            }
        }
    )
}

/// 更新任务状态命令
#[command]
pub async fn task_update_status(
    task_id: String,
    status: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<TaskResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_update_status",
        Some(user_id),
        async {
            // 首先需要将字符串状态转换为TaskStatus枚举
            let task_status = match status.as_str() {
                "todo" => crate::shared::types::TaskStatus::Todo,
                "in_progress" => crate::shared::types::TaskStatus::InProgress,
                "waiting" => crate::shared::types::TaskStatus::Waiting,
                "completed" => crate::shared::types::TaskStatus::Completed,
                "cancelled" => crate::shared::types::TaskStatus::Cancelled,
                _ => return Err(crate::shared::errors::AppError::validation("无效的任务状态")),
            };

            let result = state.task_service.update_task_status(&context, &task_id, task_status).await?;
            // 状态更新成功后，重新获取任务信息返回
            let task_result = state.task_service.get_task(&context, &task_id).await?;
            match task_result.data {
                Some(task) => Ok(TaskResponseDto::from(task)),
                None => Err(crate::shared::errors::AppError::not_found("任务不存在"))
            }
        }
    )
}

/// 获取任务统计命令
#[command]
pub async fn task_get_stats(
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<serde_json::Value, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "task_get_stats",
        Some(user_id),
        async {
            let stats_result = state.task_service.get_user_task_stats(&context).await?;
            match stats_result.data {
                Some(stats) => Ok(serde_json::to_value(stats).unwrap_or_default()),
                None => Ok(serde_json::Value::Null)
            }
        }
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_commands_compilation() {
        // 这个测试确保所有命令都能正确编译
        // 实际的功能测试应该在集成测试中进行
        assert!(true);
    }
}
