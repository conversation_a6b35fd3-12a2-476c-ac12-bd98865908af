// API通用结构和宏定义
// 定义统一的API响应格式和命令执行宏

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 通用API响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub request_id: Option<String>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: None,
            timestamp: Utc::now(),
            request_id: None,
        }
    }

    /// 创建成功响应（带消息）
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            message: Some(message),
            timestamp: Utc::now(),
            request_id: None,
        }
    }

    /// 创建错误响应
    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
            message: None,
            timestamp: Utc::now(),
            request_id: None,
        }
    }

    /// 设置请求ID
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
}

/// 空响应类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmptyResponse;

// 删除重复的宏定义，使用commands/mod.rs中的版本

// 删除重复的简化宏定义，使用commands/mod.rs中的版本

/// API状态管理结构
#[derive(Clone)]
pub struct ApiState {
    pub user_service: std::sync::Arc<crate::application::services::UserService>,
    pub project_service: std::sync::Arc<crate::application::services::ProjectService>,
    pub task_service: std::sync::Arc<crate::application::services::TaskService>,
    pub area_service: std::sync::Arc<crate::application::services::AreaService>,
}

impl ApiState {
    pub fn new(
        user_service: crate::application::services::UserService,
        project_service: crate::application::services::ProjectService,
        task_service: crate::application::services::TaskService,
        area_service: crate::application::services::AreaService,
    ) -> Self {
        Self {
            user_service: std::sync::Arc::new(user_service),
            project_service: std::sync::Arc::new(project_service),
            task_service: std::sync::Arc::new(task_service),
            area_service: std::sync::Arc::new(area_service),
        }
    }
}

/// 命令工具类
pub struct CommandUtils;

impl CommandUtils {
    /// 创建服务上下文
    pub fn create_service_context(user_id: Option<String>) -> crate::application::services::ServiceContext {
        crate::application::services::ServiceContext::new(user_id)
    }

    /// 验证用户权限
    pub fn validate_user_permission(
        context: &crate::application::services::ServiceContext,
        required_permission: &str,
    ) -> Result<(), crate::shared::errors::AppError> {
        // 简单的权限验证逻辑
        if context.is_authenticated() {
            Ok(())
        } else {
            Err(crate::shared::errors::AppError::unauthorized("用户未认证"))
        }
    }

    /// 记录审计日志
    pub fn log_audit_event(
        context: &crate::application::services::ServiceContext,
        action: &str,
        resource_type: &str,
        resource_id: &str,
        details: Option<&str>,
    ) {
        tracing::info!(
            user_id = ?context.user_id,
            request_id = %context.request_id,
            action = action,
            resource_type = resource_type,
            resource_id = resource_id,
            details = ?details,
            "Audit event"
        );
    }

    /// 验证分页参数
    pub fn validate_pagination(page: u32, page_size: u32) -> Result<(), String> {
        if page == 0 {
            return Err("页码必须大于0".to_string());
        }
        if page_size == 0 || page_size > 100 {
            return Err("页面大小必须在1-100之间".to_string());
        }
        Ok(())
    }

    /// 计算分页偏移量
    pub fn calculate_offset(page: u32, page_size: u32) -> u64 {
        ((page - 1) * page_size) as u64
    }

    /// 计算总页数
    pub fn calculate_total_pages(total: u64, page_size: u32) -> u32 {
        ((total as f64) / (page_size as f64)).ceil() as u32
    }
}

/// 分页查询参数
#[derive(Debug, Clone, Deserialize)]
pub struct PaginationParams {
    #[serde(default = "default_page")]
    pub page: u32,
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

fn default_page() -> u32 { 1 }
fn default_page_size() -> u32 { 20 }

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            page_size: 20,
        }
    }
}

impl PaginationParams {
    pub fn validate(&self) -> Result<(), String> {
        CommandUtils::validate_pagination(self.page, self.page_size)
    }

    pub fn offset(&self) -> u64 {
        CommandUtils::calculate_offset(self.page, self.page_size)
    }

    pub fn limit(&self) -> u64 {
        self.page_size as u64
    }
}

/// 分页响应
#[derive(Debug, Clone, Serialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: u64, page: u32, page_size: u32) -> Self {
        let total_pages = CommandUtils::calculate_total_pages(total, page_size);
        let has_next = page < total_pages;
        let has_prev = page > 1;

        Self {
            items,
            total,
            page,
            page_size,
            total_pages,
            has_next,
            has_prev,
        }
    }
}

/// 搜索参数
#[derive(Debug, Clone, Deserialize)]
pub struct SearchParams {
    pub keyword: Option<String>,
    #[serde(flatten)]
    pub pagination: PaginationParams,
    pub sort_by: Option<String>,
    pub sort_order: Option<SortOrder>,
}

/// 排序顺序
#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum SortOrder {
    Asc,
    Desc,
}

impl Default for SortOrder {
    fn default() -> Self {
        SortOrder::Desc
    }
}
