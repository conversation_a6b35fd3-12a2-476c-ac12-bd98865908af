-- PaoLife 索引优化
-- 创建时间: 2025-01-29
-- 版本: 1.1.0

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- ================================
-- 高频查询复合索引优化
-- ================================

-- 任务表复合索引
CREATE INDEX IF NOT EXISTS idx_tasks_status_priority ON tasks(status, priority DESC);
CREATE INDEX IF NOT EXISTS idx_tasks_project_status ON tasks(project_id, status);
CREATE INDEX IF NOT EXISTS idx_tasks_area_status ON tasks(area_id, status);
CREATE INDEX IF NOT EXISTS idx_tasks_parent_sort ON tasks(parent_task_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_tasks_due_status ON tasks(due_date, status);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_status ON tasks(assigned_to, status);

-- 项目表复合索引
CREATE INDEX IF NOT EXISTS idx_projects_status_area ON projects(status, area_id);
CREATE INDEX IF NOT EXISTS idx_projects_area_status ON projects(area_id, status);
CREATE INDEX IF NOT EXISTS idx_projects_created_by_status ON projects(created_by, status);
CREATE INDEX IF NOT EXISTS idx_projects_deadline_status ON projects(deadline, status);
CREATE INDEX IF NOT EXISTS idx_projects_priority_status ON projects(priority DESC, status);

-- 指标相关复合索引
CREATE INDEX IF NOT EXISTS idx_metrics_owner_active ON metrics(owner_type, owner_id, is_active);
CREATE INDEX IF NOT EXISTS idx_metrics_active_frequency ON metrics(is_active, frequency);
CREATE INDEX IF NOT EXISTS idx_metric_records_metric_date ON metric_records(metric_id, recorded_date DESC);
CREATE INDEX IF NOT EXISTS idx_metric_records_date_source ON metric_records(recorded_date DESC, source);

-- 习惯相关复合索引
CREATE INDEX IF NOT EXISTS idx_habits_area_active ON habits(area_id, is_active);
CREATE INDEX IF NOT EXISTS idx_habit_records_habit_date ON habit_records(habit_id, completed_date DESC);
CREATE INDEX IF NOT EXISTS idx_habit_records_date_habit ON habit_records(completed_date DESC, habit_id);

-- 资源相关复合索引
CREATE INDEX IF NOT EXISTS idx_resources_type_created ON resources(resource_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_resources_accessed_type ON resources(last_accessed_at DESC, resource_type);
CREATE INDEX IF NOT EXISTS idx_references_source_type ON references(source_entity_type, source_entity_id, reference_type);
CREATE INDEX IF NOT EXISTS idx_references_target_type ON references(target_resource_id, reference_type);

-- 标签相关复合索引
CREATE INDEX IF NOT EXISTS idx_taggables_entity_tag ON taggables(taggable_type, taggable_id, tag_id);
CREATE INDEX IF NOT EXISTS idx_taggables_tag_entity ON taggables(tag_id, taggable_type);

-- 收件箱复合索引
CREATE INDEX IF NOT EXISTS idx_inbox_items_status_priority ON inbox_items(processing_status, priority DESC);
CREATE INDEX IF NOT EXISTS idx_inbox_items_created_by_status ON inbox_items(created_by, processing_status);
CREATE INDEX IF NOT EXISTS idx_inbox_items_type_status ON inbox_items(item_type, processing_status);
CREATE INDEX IF NOT EXISTS idx_inbox_items_processed_into ON inbox_items(processed_into_type, processed_into_id);

-- 用户设置复合索引
CREATE INDEX IF NOT EXISTS idx_user_settings_user_key ON user_settings(user_id, setting_key);

-- ================================
-- 复盘系统索引优化
-- ================================

-- 复盘模板复合索引
CREATE INDEX IF NOT EXISTS idx_review_templates_type_default ON review_templates(template_type, is_default);
CREATE INDEX IF NOT EXISTS idx_review_templates_created_by_type ON review_templates(created_by, template_type);

-- 复盘记录复合索引
CREATE INDEX IF NOT EXISTS idx_reviews_template_period ON reviews(template_id, review_period_start, review_period_end);
CREATE INDEX IF NOT EXISTS idx_reviews_created_by_status ON reviews(created_by, status);
CREATE INDEX IF NOT EXISTS idx_reviews_period_status ON reviews(review_period_start DESC, status);
CREATE INDEX IF NOT EXISTS idx_reviews_status_rating ON reviews(status, overall_rating DESC);

-- 复盘答案复合索引
CREATE INDEX IF NOT EXISTS idx_review_answers_review_question ON review_answers(review_id, question_id);

-- ================================
-- 定期任务系统索引优化
-- ================================

-- 定期任务复合索引
CREATE INDEX IF NOT EXISTS idx_recurring_tasks_area_active ON recurring_tasks(area_id, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_tasks_next_due_active ON recurring_tasks(next_due_date, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_tasks_pattern_active ON recurring_tasks(recurrence_pattern, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_tasks_auto_create ON recurring_tasks(auto_create_tasks, is_active, next_due_date);

-- 定期任务执行记录复合索引
CREATE INDEX IF NOT EXISTS idx_recurring_task_executions_task_date ON recurring_task_executions(recurring_task_id, scheduled_date DESC);
CREATE INDEX IF NOT EXISTS idx_recurring_task_executions_status_date ON recurring_task_executions(execution_status, scheduled_date DESC);
CREATE INDEX IF NOT EXISTS idx_recurring_task_executions_date_status ON recurring_task_executions(scheduled_date DESC, execution_status);

-- ================================
-- 清单系统索引优化
-- ================================

-- 清单模板复合索引
CREATE INDEX IF NOT EXISTS idx_checklist_templates_category_public ON checklist_templates(category, is_public);
CREATE INDEX IF NOT EXISTS idx_checklist_templates_created_by_category ON checklist_templates(created_by, category);

-- 清单项目复合索引
CREATE INDEX IF NOT EXISTS idx_checklist_items_template_sort ON checklist_items(template_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_checklist_items_template_required ON checklist_items(template_id, is_required);

-- 清单实例复合索引
CREATE INDEX IF NOT EXISTS idx_checklist_instances_template_status ON checklist_instances(template_id, status);
CREATE INDEX IF NOT EXISTS idx_checklist_instances_project_status ON checklist_instances(project_id, status);
CREATE INDEX IF NOT EXISTS idx_checklist_instances_area_status ON checklist_instances(area_id, status);
CREATE INDEX IF NOT EXISTS idx_checklist_instances_created_by_status ON checklist_instances(created_by, status);
CREATE INDEX IF NOT EXISTS idx_checklist_instances_status_completion ON checklist_instances(status, completion_percentage DESC);

-- 清单实例项目复合索引
CREATE INDEX IF NOT EXISTS idx_checklist_instance_items_instance_completed ON checklist_instance_items(instance_id, is_completed);
CREATE INDEX IF NOT EXISTS idx_checklist_instance_items_completed_by_date ON checklist_instance_items(completed_by, completed_at DESC);

-- ================================
-- 全文搜索索引（为未来的搜索功能准备）
-- ================================

-- 创建虚拟表用于全文搜索（SQLite FTS5）
-- 注意：这些表需要在应用层维护数据同步

-- 项目全文搜索
CREATE VIRTUAL TABLE IF NOT EXISTS projects_fts USING fts5(
    id UNINDEXED,
    name,
    description,
    content='projects',
    content_rowid='rowid'
);

-- 任务全文搜索
CREATE VIRTUAL TABLE IF NOT EXISTS tasks_fts USING fts5(
    id UNINDEXED,
    title,
    description,
    content='tasks',
    content_rowid='rowid'
);

-- 领域全文搜索
CREATE VIRTUAL TABLE IF NOT EXISTS areas_fts USING fts5(
    id UNINDEXED,
    name,
    description,
    standard,
    content='areas',
    content_rowid='rowid'
);

-- 资源全文搜索
CREATE VIRTUAL TABLE IF NOT EXISTS resources_fts USING fts5(
    id UNINDEXED,
    title,
    path,
    content='resources',
    content_rowid='rowid'
);

-- ================================
-- 性能监控相关索引
-- ================================

-- 为性能监控查询优化的索引
CREATE INDEX IF NOT EXISTS idx_tasks_created_at_status ON tasks(created_at DESC, status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at_status ON projects(created_at DESC, status);
CREATE INDEX IF NOT EXISTS idx_habit_records_created_at ON habit_records(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_metric_records_created_at ON metric_records(created_at DESC);

-- 用于统计查询的索引
CREATE INDEX IF NOT EXISTS idx_tasks_completed_at ON tasks(completed_at DESC) WHERE completed_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_projects_archived_at ON projects(archived_at DESC) WHERE archived_at IS NOT NULL;

-- ================================
-- 分析查询优化索引
-- ================================

-- 时间范围分析索引
CREATE INDEX IF NOT EXISTS idx_tasks_created_completed ON tasks(created_at, completed_at);
CREATE INDEX IF NOT EXISTS idx_projects_start_deadline ON projects(start_date, deadline);
CREATE INDEX IF NOT EXISTS idx_habit_records_date_value ON habit_records(completed_date, completion_value);

-- 用户活动分析索引
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_created ON tasks(assigned_to, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_created_by_date ON projects(created_by, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_reviews_created_by_date ON reviews(created_by, created_at DESC);

-- ================================
-- 清理和维护相关索引
-- ================================

-- 用于数据清理的索引
CREATE INDEX IF NOT EXISTS idx_inbox_items_processed_at ON inbox_items(processed_at) WHERE processed_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_recurring_task_executions_executed_at ON recurring_task_executions(executed_at) WHERE executed_at IS NOT NULL;

-- ================================
-- 索引使用情况说明
-- ================================

-- 以下注释说明了各个索引的主要用途：

-- 1. 复合索引设计原则：
--    - 将最常用的过滤条件放在前面
--    - 将排序字段放在最后
--    - 考虑查询的选择性

-- 2. 主要查询场景：
--    - 按状态筛选任务/项目
--    - 按优先级排序
--    - 按时间范围查询
--    - 按用户查询个人数据
--    - 层级关系查询（任务树）

-- 3. 性能监控：
--    - 定期检查索引使用情况
--    - 监控慢查询
--    - 根据实际使用情况调整索引

-- 4. 维护建议：
--    - 定期重建索引（REINDEX）
--    - 更新表统计信息（ANALYZE）
--    - 监控数据库大小增长
