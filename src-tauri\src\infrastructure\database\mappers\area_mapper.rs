// 领域实体与数据库模型之间的映射器
// 负责Area实体与AreaModel之间的转换

use crate::domain::entities::area::{Area, CreateAreaData, UpdateAreaData};
use crate::infrastructure::database::models::area_model::AreaModel;
use crate::shared::errors::AppResult;
use chrono::Utc;

/// 领域映射器
pub struct AreaMapper;

impl AreaMapper {
    /// 将AreaModel转换为Area实体
    pub fn model_to_entity(model: AreaModel) -> AppResult<Area> {
        Area::new(CreateAreaData {
            name: model.name,
            description: model.description,
            color_hex: model.color_hex,
            icon_name: model.icon_name,
        })
    }

    /// 将Area实体转换为AreaModel
    pub fn entity_to_model(area: &Area) -> AreaModel {
        AreaModel {
            id: area.id.clone(),
            name: area.name.clone(),
            description: area.description.clone(),
            color_hex: area.color_hex.clone(),
            icon_name: area.icon_name.clone(),
            is_active: area.is_active,
            created_by: area.created_by.clone(),
            created_at: area.created_at,
            updated_at: area.updated_at,
        }
    }

    /// 将CreateAreaData转换为AreaModel
    pub fn create_data_to_model(data: CreateAreaData, created_by: String) -> AreaModel {
        let now = Utc::now();
        AreaModel {
            id: crate::shared::types::EntityId::new(),
            name: data.name,
            description: data.description,
            color_hex: data.color_hex,
            icon_name: data.icon_name,
            is_active: true,
            created_by,
            created_at: now,
            updated_at: now,
        }
    }

    /// 应用UpdateAreaData到AreaModel
    pub fn apply_update_to_model(mut model: AreaModel, data: UpdateAreaData) -> AreaModel {
        let now = Utc::now();
        
        if let Some(name) = data.name {
            model.name = name;
        }
        
        if let Some(description) = data.description {
            model.description = description;
        }
        
        if let Some(color_hex) = data.color_hex {
            model.color_hex = color_hex;
        }
        
        if let Some(icon_name) = data.icon_name {
            model.icon_name = icon_name;
        }
        
        if let Some(is_active) = data.is_active {
            model.is_active = is_active;
        }
        
        model.updated_at = now;
        model
    }
}
