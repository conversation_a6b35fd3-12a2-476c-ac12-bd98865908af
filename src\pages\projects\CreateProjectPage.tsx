// 创建项目页面
// 提供创建新项目的表单界面

import { Component, createSignal, createEffect, Show, For } from 'solid-js';
import { useNavigate, useSearchParams } from '@solidjs/router';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface Area {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

export const CreateProjectPage: Component = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  const [areas, setAreas] = createSignal<Area[]>([]);
  const [loading, setLoading] = createSignal(false);
  const [submitting, setSubmitting] = createSignal(false);
  
  const [form, setForm] = createSignal({
    name: '',
    description: '',
    priority: 'medium',
    start_date: '',
    deadline: '',
    estimated_hours: 0,
    area_id: searchParams.area_id || '',
  });

  // 加载领域列表
  const loadAreas = async () => {
    try {
      setLoading(true);
      const areaData = await api.listAreas({ page: 1, page_size: 100 });
      
      if (Array.isArray(areaData)) {
        setAreas(areaData);
      }
    } catch (error) {
      console.error('Failed to load areas:', error);
      toast.error('加载领域列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  createEffect(() => {
    loadAreas();
  });

  // 表单验证
  const validateForm = () => {
    const formData = form();
    
    if (!formData.name.trim()) {
      toast.error('请输入项目名称');
      return false;
    }
    
    if (formData.deadline && formData.start_date) {
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.deadline);
      
      if (endDate <= startDate) {
        toast.error('截止日期必须晚于开始日期');
        return false;
      }
    }
    
    return true;
  };

  // 提交表单
  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    try {
      setSubmitting(true);
      const formData = form();
      
      const projectData = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        priority: formData.priority,
        start_date: formData.start_date || undefined,
        deadline: formData.deadline || undefined,
        estimated_hours: formData.estimated_hours || undefined,
        area_id: formData.area_id || undefined,
      };
      
      const newProject = await api.createProject(projectData);
      
      toast.success('项目创建成功');
      navigate(`/projects/${newProject.id}`);
    } catch (error) {
      console.error('Failed to create project:', error);
      toast.error('创建项目失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    setForm({
      name: '',
      description: '',
      priority: 'medium',
      start_date: '',
      deadline: '',
      estimated_hours: 0,
      area_id: searchParams.area_id || '',
    });
  };

  return (
    <MainLayout>
      <div class="max-w-4xl mx-auto p-6">
        {/* 页面头部 */}
        <div class="mb-6">
          <button
            type="button"
            class="text-blue-600 hover:text-blue-800 mb-2"
            onClick={() => navigate('/projects')}
          >
            ← 返回项目列表
          </button>
          <h1 class="text-3xl font-bold text-gray-900">创建新项目</h1>
          <p class="text-gray-600 mt-2">
            填写下面的信息来创建一个新的项目
          </p>
        </div>

        {/* 创建表单 */}
        <div class="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} class="space-y-6">
            {/* 基本信息 */}
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    项目名称 <span class="text-red-500">*</span>
                  </label>
                  <Input
                    type="text"
                    value={form().name}
                    onInput={(e) => setForm(prev => ({ ...prev, name: e.currentTarget.value }))}
                    placeholder="输入项目名称"
                    required
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    项目描述
                  </label>
                  <textarea
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    rows="4"
                    value={form().description}
                    onInput={(e) => setForm(prev => ({ ...prev, description: e.currentTarget.value }))}
                    placeholder="输入项目描述（可选）"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    所属领域
                  </label>
                  <Show when={loading()}>
                    <div class="text-sm text-gray-500">加载领域列表中...</div>
                  </Show>
                  <Show when={!loading()}>
                    <select
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      value={form().area_id}
                      onChange={(e) => setForm(prev => ({ ...prev, area_id: e.currentTarget.value }))}
                    >
                      <option value="">选择领域（可选）</option>
                      <For each={areas()}>
                        {(area) => (
                          <option value={area.id}>{area.name}</option>
                        )}
                      </For>
                    </select>
                  </Show>
                </div>
              </div>
            </div>

            {/* 项目设置 */}
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">项目设置</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    优先级
                  </label>
                  <select
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    value={form().priority}
                    onChange={(e) => setForm(prev => ({ ...prev, priority: e.currentTarget.value }))}
                  >
                    <option value="low">低优先级</option>
                    <option value="medium">中优先级</option>
                    <option value="high">高优先级</option>
                    <option value="urgent">紧急</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    预估工时（小时）
                  </label>
                  <Input
                    type="number"
                    min="0"
                    step="0.5"
                    value={form().estimated_hours}
                    onInput={(e) => setForm(prev => ({ ...prev, estimated_hours: parseFloat(e.currentTarget.value) || 0 }))}
                    placeholder="预估需要的工时"
                  />
                </div>
              </div>
            </div>

            {/* 时间设置 */}
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">时间设置</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    开始日期
                  </label>
                  <Input
                    type="date"
                    value={form().start_date}
                    onInput={(e) => setForm(prev => ({ ...prev, start_date: e.currentTarget.value }))}
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    截止日期
                  </label>
                  <Input
                    type="date"
                    value={form().deadline}
                    onInput={(e) => setForm(prev => ({ ...prev, deadline: e.currentTarget.value }))}
                  />
                </div>
              </div>
              
              <Show when={form().start_date && form().deadline}>
                <div class="mt-2 text-sm text-gray-600">
                  项目周期: {
                    Math.ceil(
                      (new Date(form().deadline).getTime() - new Date(form().start_date).getTime()) 
                      / (1000 * 60 * 60 * 24)
                    )
                  } 天
                </div>
              </Show>
            </div>

            {/* 表单操作 */}
            <div class="flex justify-end gap-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={submitting()}
              >
                重置
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/projects')}
                disabled={submitting()}
              >
                取消
              </Button>
              
              <Button
                type="submit"
                disabled={submitting() || !form().name.trim()}
              >
                <Show when={submitting()}>
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                </Show>
                {submitting() ? '创建中...' : '创建项目'}
              </Button>
            </div>
          </form>
        </div>

        {/* 提示信息 */}
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">
                创建项目提示
              </h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>项目名称是必填项，请确保名称简洁明了</li>
                  <li>选择合适的领域有助于更好地组织和管理项目</li>
                  <li>设置合理的时间计划和工时预估有助于项目进度管控</li>
                  <li>创建后可以随时编辑项目信息和添加任务</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};
