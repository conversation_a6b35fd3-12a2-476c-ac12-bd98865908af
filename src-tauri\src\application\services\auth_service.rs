// 认证和授权服务
// 实现用户认证、会话管理和权限控制

use crate::domain::entities::user::{User, CreateUserData};
use crate::domain::repositories::user_repository::UserRepository;
use crate::infrastructure::database::validators::{CreateUserValidator, UserBusinessRuleValidator, Validator};
use crate::shared::errors::{AppError, AppResult};
use crate::shared::types::EntityId;
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use serde::{Deserialize, Serialize};

/// 认证令牌
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthToken {
    pub token: String,
    pub user_id: String,
    pub expires_at: DateTime<Utc>,
    pub token_type: TokenType,
}

/// 令牌类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum TokenType {
    Access,
    Refresh,
}

/// 登录请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
    pub remember_me: bool,
}

/// 登录响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub user: User,
    pub access_token: AuthToken,
    pub refresh_token: Option<AuthToken>,
}

/// 注册请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegisterRequest {
    pub username: String,
    pub email: Option<String>,
    pub password: String,
    pub full_name: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

/// 密码重置请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordResetRequest {
    pub email: String,
}

/// 密码更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordUpdateRequest {
    pub current_password: String,
    pub new_password: String,
}

/// 认证和授权服务
pub struct AuthService {
    user_repository: Arc<dyn UserRepository>,
    jwt_secret: String,
    access_token_duration: Duration,
    refresh_token_duration: Duration,
}

impl AuthService {
    /// 创建新的认证服务
    pub fn new(
        user_repository: Arc<dyn UserRepository>,
        jwt_secret: String,
    ) -> Self {
        Self {
            user_repository,
            jwt_secret,
            access_token_duration: Duration::hours(1),      // 访问令牌1小时
            refresh_token_duration: Duration::days(30),     // 刷新令牌30天
        }
    }

    /// 用户注册
    pub async fn register(&self, request: RegisterRequest) -> AppResult<User> {
        // 转换为创建用户数据
        let create_data = CreateUserData {
            username: request.username.clone(),
            email: request.email.clone(),
            password: request.password.clone(),
            full_name: request.full_name,
            timezone: request.timezone,
            language: request.language,
            created_by: "system".to_string(),
        };

        // 验证数据
        let validator = CreateUserValidator;
        let create_data = validator.validate_and_sanitize(create_data)?;

        // 验证业务规则
        UserBusinessRuleValidator::validate_data_integrity(&create_data)?;

        // 检查用户名是否已存在
        UserBusinessRuleValidator::validate_username_uniqueness(
            &create_data.username,
            || async { self.user_repository.username_exists(&create_data.username).await }
        ).await?;

        // 检查邮箱是否已存在
        if let Some(email) = &create_data.email {
            UserBusinessRuleValidator::validate_email_uniqueness(
                email,
                || async { self.user_repository.email_exists(email).await }
            ).await?;
        }

        // 哈希密码
        let password_hash = self.hash_password(&create_data.password)?;
        
        // 创建用户数据（替换明文密码为哈希）
        let mut final_data = create_data;
        final_data.password = password_hash;

        // 生成用户ID
        let user_id = Uuid::new_v4().to_string();

        // 创建用户
        let user = self.user_repository.create_user(final_data, user_id).await?;

        tracing::info!(
            user_id = %user.id(),
            username = %user.username(),
            "User registered successfully"
        );

        Ok(user)
    }

    /// 用户登录
    pub async fn login(&self, request: LoginRequest) -> AppResult<LoginResponse> {
        // 查找用户
        let user = self.user_repository.find_by_username(&request.username).await?
            .ok_or_else(|| AppError::unauthorized("用户名或密码错误"))?;

        // 检查用户是否激活
        if !user.is_active {
            return Err(AppError::unauthorized("用户账户已被停用"));
        }

        // 验证密码
        if !self.verify_password(&request.password, &user.password_hash)? {
            return Err(AppError::unauthorized("用户名或密码错误"));
        }

        // 生成访问令牌
        let access_token = self.generate_access_token(&user.id)?;

        // 生成刷新令牌（如果记住我）
        let refresh_token = if request.remember_me {
            Some(self.generate_refresh_token(user.id())?)
        } else {
            None
        };

        // 更新最后登录时间
        self.user_repository.update_last_login(&user.id).await?;

        tracing::info!(
            user_id = %user.id,
            username = %user.username,
            remember_me = %request.remember_me,
            "User logged in successfully"
        );

        Ok(LoginResponse {
            user,
            access_token,
            refresh_token,
        })
    }

    /// 用户登出
    pub async fn logout(&self, token: &str) -> AppResult<()> {
        // 验证令牌
        let claims = self.verify_token(token)?;
        
        // 这里可以将令牌加入黑名单
        // 暂时只记录日志
        tracing::info!(
            user_id = %claims.user_id,
            "User logged out successfully"
        );

        Ok(())
    }

    /// 刷新访问令牌
    pub async fn refresh_token(&self, refresh_token: &str) -> AppResult<AuthToken> {
        // 验证刷新令牌
        let claims = self.verify_token(refresh_token)?;
        
        // 检查令牌类型
        if claims.token_type != "refresh" {
            return Err(AppError::unauthorized("无效的刷新令牌"));
        }

        // 检查用户是否仍然存在且激活
        let user = self.user_repository.find_by_id(&claims.user_id).await?
            .ok_or_else(|| AppError::unauthorized("用户不存在"))?;

        if !user.is_active {
            return Err(AppError::unauthorized("用户账户已被停用"));
        }

        // 生成新的访问令牌
        let new_access_token = self.generate_access_token(&claims.user_id)?;

        tracing::info!(
            user_id = %claims.user_id,
            "Access token refreshed successfully"
        );

        Ok(new_access_token)
    }

    /// 验证访问令牌
    pub async fn verify_access_token(&self, token: &str) -> AppResult<User> {
        // 验证令牌
        let claims = self.verify_token(token)?;
        
        // 检查令牌类型
        if claims.token_type != "access" {
            return Err(AppError::unauthorized("无效的访问令牌"));
        }

        // 获取用户信息
        let user = self.user_repository.find_by_id(&claims.user_id).await?
            .ok_or_else(|| AppError::unauthorized("用户不存在"))?;

        if !user.is_active {
            return Err(AppError::unauthorized("用户账户已被停用"));
        }

        Ok(user)
    }

    /// 更改密码
    pub async fn change_password(
        &self,
        user_id: &str,
        request: PasswordUpdateRequest,
    ) -> AppResult<()> {
        // 获取用户
        let user = self.user_repository.find_by_id(user_id).await?
            .ok_or_else(|| AppError::not_found("用户不存在"))?;

        // 验证当前密码
        if !self.verify_password(&request.current_password, &user.password_hash)? {
            return Err(AppError::unauthorized("当前密码错误"));
        }

        // 验证新密码强度
        self.validate_password_strength(&request.new_password)?;

        // 哈希新密码
        let new_password_hash = self.hash_password(&request.new_password)?;

        // 更新密码
        self.user_repository.update_password(user_id, &new_password_hash).await?;

        tracing::info!(
            user_id = %user_id,
            "Password changed successfully"
        );

        Ok(())
    }

    /// 生成访问令牌
    fn generate_access_token(&self, user_id: &str) -> AppResult<AuthToken> {
        let expires_at = Utc::now() + self.access_token_duration;
        let token = self.create_jwt_token(user_id, "access", expires_at)?;

        Ok(AuthToken {
            token,
            user_id: user_id.to_string(),
            expires_at,
            token_type: TokenType::Access,
        })
    }

    /// 生成刷新令牌
    fn generate_refresh_token(&self, user_id: &str) -> AppResult<AuthToken> {
        let expires_at = Utc::now() + self.refresh_token_duration;
        let token = self.create_jwt_token(user_id, "refresh", expires_at)?;

        Ok(AuthToken {
            token,
            user_id: user_id.to_string(),
            expires_at,
            token_type: TokenType::Refresh,
        })
    }

    /// 创建JWT令牌
    fn create_jwt_token(
        &self,
        user_id: &str,
        token_type: &str,
        expires_at: DateTime<Utc>,
    ) -> AppResult<String> {
        // 这里应该使用实际的JWT库，如jsonwebtoken
        // 暂时使用简单的格式
        let payload = format!("{}:{}:{}", user_id, token_type, expires_at.timestamp());
        let signature = self.sign_payload(&payload)?;
        Ok(format!("{}.{}", payload, signature))
    }

    /// 验证令牌
    fn verify_token(&self, token: &str) -> AppResult<TokenClaims> {
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() != 2 {
            return Err(AppError::unauthorized("无效的令牌格式"));
        }

        let payload = parts[0];
        let signature = parts[1];

        // 验证签名
        let expected_signature = self.sign_payload(payload)?;
        if signature != expected_signature {
            return Err(AppError::unauthorized("无效的令牌签名"));
        }

        // 解析载荷
        let payload_parts: Vec<&str> = payload.split(':').collect();
        if payload_parts.len() != 3 {
            return Err(AppError::unauthorized("无效的令牌载荷"));
        }

        let user_id = payload_parts[0].to_string();
        let token_type = payload_parts[1].to_string();
        let expires_timestamp: i64 = payload_parts[2].parse()
            .map_err(|_| AppError::unauthorized("无效的过期时间"))?;

        let expires_at = DateTime::from_timestamp(expires_timestamp, 0)
            .ok_or_else(|| AppError::unauthorized("无效的过期时间"))?;

        // 检查是否过期
        if Utc::now() > expires_at {
            return Err(AppError::unauthorized("令牌已过期"));
        }

        Ok(TokenClaims {
            user_id,
            token_type,
            expires_at,
        })
    }

    /// 签名载荷
    fn sign_payload(&self, payload: &str) -> AppResult<String> {
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(payload.as_bytes());
        hasher.update(self.jwt_secret.as_bytes());
        Ok(format!("{:x}", hasher.finalize()))
    }

    /// 哈希密码
    fn hash_password(&self, password: &str) -> AppResult<String> {
        bcrypt::hash(password, bcrypt::DEFAULT_COST)
            .map_err(|e| AppError::internal(&format!("Failed to hash password: {}", e)))
    }

    /// 验证密码
    fn verify_password(&self, password: &str, hash: &str) -> AppResult<bool> {
        bcrypt::verify(password, hash)
            .map_err(|e| AppError::internal(&format!("Failed to verify password: {}", e)))
    }

    /// 验证密码强度
    fn validate_password_strength(&self, password: &str) -> AppResult<()> {
        if password.len() < 8 {
            return Err(AppError::validation("密码至少需要8个字符"));
        }

        if password.len() > 128 {
            return Err(AppError::validation("密码不能超过128个字符"));
        }

        let has_upper = password.chars().any(|c| c.is_uppercase());
        let has_lower = password.chars().any(|c| c.is_lowercase());
        let has_digit = password.chars().any(|c| c.is_numeric());
        let has_special = password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

        let strength_count = [has_upper, has_lower, has_digit, has_special]
            .iter()
            .filter(|&&x| x)
            .count();

        if strength_count < 3 {
            return Err(AppError::validation(
                "密码必须包含大写字母、小写字母、数字和特殊字符中的至少3种"
            ));
        }

        Ok(())
    }
}

/// 令牌声明
#[derive(Debug, Clone)]
struct TokenClaims {
    pub user_id: String,
    pub token_type: String,
    pub expires_at: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_strength_validation() {
        let auth_service = AuthService::new(
            Arc::new(MockUserRepository::new()),
            "test_secret".to_string(),
        );

        // 有效密码
        assert!(auth_service.validate_password_strength("Password123!").is_ok());
        
        // 太短
        assert!(auth_service.validate_password_strength("Pass1!").is_err());
        
        // 缺少特殊字符
        assert!(auth_service.validate_password_strength("Password123").is_err());
        
        // 缺少数字
        assert!(auth_service.validate_password_strength("Password!").is_err());
    }

    #[test]
    fn test_token_generation_and_verification() {
        let auth_service = AuthService::new(
            Arc::new(MockUserRepository::new()),
            "test_secret".to_string(),
        );

        let user_id = "test_user_123";
        let token = auth_service.generate_access_token(user_id).unwrap();
        
        assert_eq!(token.user_id, user_id);
        assert_eq!(token.token_type, TokenType::Access);
        assert!(token.expires_at > Utc::now());
        
        // 验证令牌
        let claims = auth_service.verify_token(&token.token).unwrap();
        assert_eq!(claims.user_id, user_id);
        assert_eq!(claims.token_type, "access");
    }
}
