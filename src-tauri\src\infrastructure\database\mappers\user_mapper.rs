// 用户对象映射器
// 负责在领域实体和数据库模型之间进行转换

use crate::domain::entities::user::{User, CreateUserData, UpdateUserData};
use crate::infrastructure::database::models::{UserModel, CreateUserModel, UpdateUserModel};
use crate::shared::errors::{AppError, AppResult};

/// 用户映射器
pub struct UserMapper;

impl UserMapper {
    /// 将数据库模型转换为领域实体
    pub fn to_domain(model: UserModel) -> AppResult<User> {
        User::new(CreateUserData {
            username: model.username,
            email: model.email,
            password: String::new(), // 从数据库加载时不需要原始密码
            full_name: model.full_name,
            avatar_url: model.avatar_url,
            timezone: model.timezone,
            language: model.language,
            created_by: "system".to_string(),
        })
    }

    /// 将领域实体转换为数据库模型
    pub fn to_model(user: &User) -> UserModel {
        UserModel {
            id: user.id.clone(),
            username: user.username.clone(),
            email: user.email.clone(),
            password_hash: user.password_hash.clone(),
            full_name: user.full_name.clone(),
            avatar_url: user.avatar_url.clone(),
            timezone: user.timezone.clone(),
            language: user.language.clone(),
            is_active: user.is_active,
            created_at: user.created_at,
            updated_at: user.updated_at,
            last_login_at: user.last_login_at,
        }
    }

    /// 将创建数据转换为数据库创建模型
    pub fn create_data_to_model(data: CreateUserData, id: String) -> CreateUserModel {
        CreateUserModel::new(
            id,
            data.username,
            data.email,
            data.password,
            data.full_name,
            data.timezone,
            data.language,
        )
    }

    /// 将更新数据转换为数据库更新模型
    pub fn update_data_to_model(data: UpdateUserData) -> UpdateUserModel {
        let mut model = UpdateUserModel::new();

        if let Some(email) = data.email {
            model = model.with_email(Some(email));
        }

        // UpdateUserData 不包含 password_hash 字段，跳过此部分

        if let Some(full_name) = data.full_name {
            model = model.with_full_name(Some(full_name));
        }

        if let Some(avatar_url) = data.avatar_url {
            model = model.with_avatar_url(Some(avatar_url));
        }

        if let Some(timezone) = data.timezone {
            model = model.with_timezone(timezone);
        }

        if let Some(language) = data.language {
            model = model.with_language(language);
        }

        // UpdateUserData 不包含 is_active 和 last_login_at 字段，跳过这些部分

        model
    }

    /// 批量转换数据库模型为领域实体
    pub fn to_domain_list(models: Vec<UserModel>) -> AppResult<Vec<User>> {
        models
            .into_iter()
            .map(Self::to_domain)
            .collect::<AppResult<Vec<_>>>()
    }

    /// 批量转换领域实体为数据库模型
    pub fn to_model_list(users: &[User]) -> Vec<UserModel> {
        users.iter().map(Self::to_model).collect()
    }

    /// 验证用户数据完整性
    pub fn validate_user_data(model: &UserModel) -> AppResult<()> {
        // 验证用户名
        if model.username.trim().is_empty() {
            return Err(AppError::validation("用户名不能为空"));
        }

        if model.username.len() < 3 {
            return Err(AppError::validation("用户名至少需要3个字符"));
        }

        if model.username.len() > 50 {
            return Err(AppError::validation("用户名不能超过50个字符"));
        }

        // 验证邮箱格式
        if let Some(email) = &model.email {
            if !email.trim().is_empty() && !Self::is_valid_email(email) {
                return Err(AppError::validation("邮箱格式不正确"));
            }
        }

        // 验证密码哈希
        if model.password_hash.trim().is_empty() {
            return Err(AppError::validation("密码哈希不能为空"));
        }

        // 验证时区
        if model.timezone.trim().is_empty() {
            return Err(AppError::validation("时区不能为空"));
        }

        // 验证语言
        if model.language.trim().is_empty() {
            return Err(AppError::validation("语言不能为空"));
        }

        // 验证全名长度
        if let Some(full_name) = &model.full_name {
            if full_name.len() > 100 {
                return Err(AppError::validation("全名不能超过100个字符"));
            }
        }

        Ok(())
    }

    /// 验证创建用户数据
    pub fn validate_create_data(data: &CreateUserData) -> AppResult<()> {
        // 验证用户名
        if data.username.trim().is_empty() {
            return Err(AppError::validation("用户名不能为空"));
        }

        if data.username.len() < 3 {
            return Err(AppError::validation("用户名至少需要3个字符"));
        }

        if data.username.len() > 50 {
            return Err(AppError::validation("用户名不能超过50个字符"));
        }

        // 验证用户名格式（只允许字母、数字、下划线）
        if !data.username.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(AppError::validation("用户名只能包含字母、数字和下划线"));
        }

        // 验证邮箱
        if let Some(email) = &data.email {
            if !email.trim().is_empty() && !Self::is_valid_email(email) {
                return Err(AppError::validation("邮箱格式不正确"));
            }
        }

        // 验证密码
        if data.password.len() < 6 {
            return Err(AppError::validation("密码至少需要6个字符"));
        }

        if data.password.len() > 128 {
            return Err(AppError::validation("密码不能超过128个字符"));
        }

        // 验证全名
        if let Some(full_name) = &data.full_name {
            if full_name.len() > 100 {
                return Err(AppError::validation("全名不能超过100个字符"));
            }
        }

        Ok(())
    }

    /// 验证更新用户数据
    pub fn validate_update_data(data: &UpdateUserData) -> AppResult<()> {
        // 验证邮箱
        if let Some(email) = &data.email {
            if !email.trim().is_empty() && !Self::is_valid_email(email) {
                return Err(AppError::validation("邮箱格式不正确"));
            }
        }

        // 验证密码哈希
        // UpdateUserData 不包含 password_hash 字段，跳过此验证

        // 验证全名
        if let Some(full_name) = &data.full_name {
            if full_name.len() > 100 {
                return Err(AppError::validation("全名不能超过100个字符"));
            }
        }

        // 验证时区
        if let Some(timezone) = &data.timezone {
            if timezone.trim().is_empty() {
                return Err(AppError::validation("时区不能为空"));
            }
        }

        // 验证语言
        if let Some(language) = &data.language {
            if language.trim().is_empty() {
                return Err(AppError::validation("语言不能为空"));
            }
        }

        Ok(())
    }

    /// 简单的邮箱格式验证
    fn is_valid_email(email: &str) -> bool {
        email.contains('@') && email.contains('.') && email.len() > 5
    }

    /// 清理用户数据（去除多余空格等）
    pub fn sanitize_create_data(mut data: CreateUserData) -> CreateUserData {
        data.username = data.username.trim().to_string();
        
        if let Some(email) = data.email {
            data.email = Some(email.trim().to_lowercase());
        }

        if let Some(full_name) = data.full_name {
            data.full_name = Some(full_name.trim().to_string());
        }

        data.timezone = Some(data.timezone.unwrap_or_else(|| "UTC".to_string()));
        data.language = Some(data.language.unwrap_or_else(|| "en".to_string()));

        data
    }

    /// 清理更新数据
    pub fn sanitize_update_data(mut data: UpdateUserData) -> UpdateUserData {
        if let Some(email) = data.email {
            data.email = Some(email.trim().to_lowercase());
        }

        if let Some(full_name) = data.full_name {
            data.full_name = Some(full_name.trim().to_string());
        }

        if let Some(timezone) = data.timezone {
            data.timezone = Some(timezone.trim().to_string());
        }

        if let Some(language) = data.language {
            data.language = Some(language.trim().to_string());
        }

        data
    }

    /// 检查用户数据是否发生变化
    pub fn has_changes(original: &User, update_data: &UpdateUserData) -> bool {
        if let Some(email) = &update_data.email {
            if original.email.as_ref() != Some(email) {
                return true;
            }
        }

        // UpdateUserData 不包含 password_hash 字段，跳过此验证

        if let Some(full_name) = &update_data.full_name {
            if original.full_name.as_ref() != Some(full_name) {
                return true;
            }
        }

        if let Some(avatar_url) = &update_data.avatar_url {
            if original.avatar_url.as_ref() != Some(avatar_url) {
                return true;
            }
        }

        if let Some(timezone) = &update_data.timezone {
            if original.timezone != *timezone {
                return true;
            }
        }

        if let Some(language) = &update_data.language {
            if original.language != *language {
                return true;
            }
        }

        // UpdateUserData 不包含 is_active 字段，跳过此验证

        false
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_user_mapping() {
        let model = UserModel {
            id: "user123".to_string(),
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hash123".to_string(),
            full_name: Some("Test User".to_string()),
            avatar_url: None,
            timezone: "UTC".to_string(),
            language: "en".to_string(),
            is_active: true,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_login_at: None,
        };

        // 测试模型到领域实体的转换
        let user = UserMapper::to_domain(model.clone()).unwrap();
        assert_eq!(user.id(), &model.id);
        assert_eq!(user.username(), &model.username);

        // 测试领域实体到模型的转换
        let converted_model = UserMapper::to_model(&user);
        assert_eq!(converted_model.id, model.id);
        assert_eq!(converted_model.username, model.username);
    }

    #[test]
    fn test_create_data_validation() {
        let valid_data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "password123".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: Some("UTC".to_string()),
            language: Some("en".to_string()),
            created_by: "system".to_string(),
        };

        assert!(UserMapper::validate_create_data(&valid_data).is_ok());

        // 测试无效用户名
        let invalid_data = CreateUserData {
            username: "ab".to_string(), // 太短
            ..valid_data.clone()
        };
        assert!(UserMapper::validate_create_data(&invalid_data).is_err());

        // 测试无效邮箱
        let invalid_data = CreateUserData {
            email: Some("invalid-email".to_string()),
            ..valid_data.clone()
        };
        assert!(UserMapper::validate_create_data(&invalid_data).is_err());
    }

    #[test]
    fn test_email_validation() {
        assert!(UserMapper::is_valid_email("<EMAIL>"));
        assert!(UserMapper::is_valid_email("<EMAIL>"));
        assert!(!UserMapper::is_valid_email("invalid-email"));
        assert!(!UserMapper::is_valid_email("@domain.com"));
        assert!(!UserMapper::is_valid_email("user@"));
    }

    #[test]
    fn test_data_sanitization() {
        let data = CreateUserData {
            username: "  testuser  ".to_string(),
            email: Some("  <EMAIL>  ".to_string()),
            password: "password123".to_string(),
            full_name: Some("  Test User  ".to_string()),
            timezone: None,
            language: None,
            created_by: "system".to_string(),
        };

        let sanitized = UserMapper::sanitize_create_data(data);
        assert_eq!(sanitized.username, "testuser");
        assert_eq!(sanitized.email, Some("<EMAIL>".to_string()));
        assert_eq!(sanitized.full_name, Some("Test User".to_string()));
        assert_eq!(sanitized.timezone, Some("UTC".to_string()));
        assert_eq!(sanitized.language, Some("en".to_string()));
    }
}
