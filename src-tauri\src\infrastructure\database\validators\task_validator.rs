// 任务数据验证器
// 实现任务相关的数据验证和业务规则检查

use crate::domain::entities::task::{CreateTaskData, UpdateTaskData};
// use crate::domain::value_objects::{Priority, TaskStatus}; // 暂时注释掉，因为 value_objects 模块不存在
use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
use crate::shared::errors::{AppError, AppResult};
use chrono::NaiveDate;

/// 任务创建数据验证器
pub struct CreateTaskValidator;

impl Validator<CreateTaskData> for CreateTaskValidator {
    fn validate(&self, data: &CreateTaskData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证任务标题
        ctx.validate(
            || ValidationUtils::validate_required(&data.title, "任务标题"),
            "title_required"
        );
        
        ctx.validate(
            || ValidationUtils::validate_length(&data.title, "任务标题", 1, 200),
            "title_length"
        );

        // 验证描述
        if let Some(description) = &data.description {
            ctx.validate(
                || ValidationUtils::validate_length(description, "任务描述", 0, 2000),
                "description_length"
            );
        }

        // 验证预估时间
        if let Some(estimated_minutes) = data.estimated_minutes {
            ctx.validate(
                || ValidationUtils::validate_range(
                    estimated_minutes,
                    "预估时间",
                    1,
                    10080 // 一周的分钟数
                ),
                "estimated_minutes_range"
            );
        }

        // 验证截止日期不能是过去
        if let Some(due_date) = data.due_date {
            let today = chrono::Utc::now().date_naive();
            if due_date < today {
                ctx.validate(
                    || Err(AppError::validation("截止日期不能是过去时间")),
                    "due_date_past"
                );
            }
        }

        ctx.finish()
    }

    fn sanitize(&self, mut data: CreateTaskData) -> CreateTaskData {
        // 清理任务标题
        data.title = ValidationUtils::sanitize_string(data.title);
        
        // 清理描述
        data.description = ValidationUtils::sanitize_optional_string(data.description);

        data
    }
}

/// 任务更新数据验证器
pub struct UpdateTaskValidator;

impl Validator<UpdateTaskData> for UpdateTaskValidator {
    fn validate(&self, data: &UpdateTaskData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证任务标题
        if let Some(title) = &data.title {
            ctx.validate(
                || ValidationUtils::validate_required(title, "任务标题"),
                "title_required"
            );
            
            ctx.validate(
                || ValidationUtils::validate_length(title, "任务标题", 1, 200),
                "title_length"
            );
        }

        // 验证描述
        if let Some(description) = &data.description {
            ctx.validate(
                || ValidationUtils::validate_length(description, "任务描述", 0, 2000),
                "description_length"
            );
        }

        // 验证完成度
        if let Some(completion_percentage) = data.completion_percentage {
            ctx.validate(
                || ValidationUtils::validate_range(
                    completion_percentage,
                    "完成度",
                    0,
                    100
                ),
                "completion_percentage_range"
            );
        }

        // 验证预估时间
        if let Some(estimated_minutes) = data.estimated_minutes {
            ctx.validate(
                || ValidationUtils::validate_range(
                    estimated_minutes,
                    "预估时间",
                    1,
                    10080
                ),
                "estimated_minutes_range"
            );
        }

        // 验证实际时间
        // UpdateTaskData 不包含 actual_minutes 字段，跳过此验证

        ctx.finish()
    }

    fn sanitize(&self, mut data: UpdateTaskData) -> UpdateTaskData {
        // 清理任务标题
        if let Some(title) = data.title {
            data.title = Some(ValidationUtils::sanitize_string(title));
        }
        
        // 清理描述
        if let Some(description) = data.description {
            data.description = ValidationUtils::sanitize_optional_string(Some(description));
        }

        data
    }
}

/// 任务业务规则验证器
pub struct TaskBusinessRuleValidator;

impl TaskBusinessRuleValidator {
    /// 验证任务状态转换
    pub fn validate_status_transition(
        current_status: &TaskStatus,
        new_status: &TaskStatus,
    ) -> AppResult<()> {
        use crate::shared::types::TaskStatus::*;

        let valid_transitions = match current_status {
            Todo => vec![InProgress, Waiting, Cancelled],
            InProgress => vec![Todo, Waiting, Completed, Cancelled],
            Waiting => vec![Todo, InProgress, Cancelled],
            Completed => vec![Todo, InProgress], // 允许重新打开已完成的任务
            Cancelled => vec![Todo], // 取消的任务可以重新激活
        };

        if current_status == new_status {
            return Ok(()); // 相同状态总是允许的
        }

        if !valid_transitions.contains(new_status) {
            return Err(AppError::validation(&format!(
                "不能从状态 '{:?}' 转换到 '{:?}'",
                current_status, new_status
            )));
        }

        Ok(())
    }

    /// 验证任务完成条件
    pub fn validate_completion_requirements(
        status: &TaskStatus,
        completion_percentage: i32,
        has_subtasks: bool,
        subtasks_completed: bool,
    ) -> AppResult<()> {
        if *status == TaskStatus::Completed {
            // 完成的任务完成度必须是100%
            if completion_percentage < 100 {
                return Err(AppError::validation(
                    "任务标记为完成时，完成度必须达到100%"
                ));
            }

            // 如果有子任务，所有子任务必须完成
            if has_subtasks && !subtasks_completed {
                return Err(AppError::validation(
                    "任务还有未完成的子任务，无法标记为完成"
                ));
            }
        }

        Ok(())
    }

    /// 验证父子任务关系
    pub fn validate_parent_child_relationship(
        task_id: &str,
        parent_task_id: Option<&str>,
        project_id: Option<&str>,
        parent_project_id: Option<&str>,
    ) -> AppResult<()> {
        // 任务不能以自己为父任务
        if let Some(parent_id) = parent_task_id {
            if task_id == parent_id {
                return Err(AppError::validation("任务不能以自己为父任务"));
            }
        }

        // 子任务必须与父任务在同一项目中
        if let (Some(parent_id), Some(task_project), Some(parent_project)) = 
            (parent_task_id, project_id, parent_project_id) {
            if task_project != parent_project {
                return Err(AppError::validation(
                    "子任务必须与父任务在同一项目中"
                ));
            }
        }

        Ok(())
    }

    /// 验证任务分配权限
    pub fn validate_assignment_permission(
        assignee_id: Option<&str>,
        current_user: &str,
        is_admin: bool,
        is_project_member: bool,
    ) -> AppResult<()> {
        if let Some(assignee) = assignee_id {
            // 只有管理员、项目成员或任务创建者可以分配任务
            if assignee != current_user && !is_admin && !is_project_member {
                return Err(AppError::validation(
                    "没有权限分配任务给其他用户"
                ));
            }
        }

        Ok(())
    }

    /// 验证任务删除权限
    pub fn validate_deletion_permission(
        task_creator: &str,
        current_user: &str,
        is_admin: bool,
        status: &TaskStatus,
        has_subtasks: bool,
    ) -> AppResult<()> {
        // 只有任务创建者或管理员可以删除任务
        if task_creator != current_user && !is_admin {
            return Err(AppError::validation("只有任务创建者或管理员可以删除任务"));
        }

        // 有子任务的任务不能删除
        if has_subtasks {
            return Err(AppError::validation("有子任务的任务不能删除"));
        }

        // 已完成的任务建议不要删除
        if *status == TaskStatus::Completed {
            return Err(AppError::validation(
                "已完成的任务建议不要删除，可以归档到项目中"
            ));
        }

        Ok(())
    }

    /// 验证任务截止日期
    pub fn validate_due_date_constraints(
        due_date: Option<NaiveDate>,
        project_deadline: Option<NaiveDate>,
        parent_due_date: Option<NaiveDate>,
    ) -> AppResult<()> {
        if let Some(due_date) = due_date {
            // 任务截止日期不能晚于项目截止日期
            if let Some(project_deadline) = project_deadline {
                if due_date > project_deadline {
                    return Err(AppError::validation(
                        "任务截止日期不能晚于项目截止日期"
                    ));
                }
            }

            // 子任务截止日期不能晚于父任务截止日期
            if let Some(parent_due) = parent_due_date {
                if due_date > parent_due {
                    return Err(AppError::validation(
                        "子任务截止日期不能晚于父任务截止日期"
                    ));
                }
            }

            // 截止日期不能超过1年
            let today = chrono::Utc::now().date_naive();
            let max_due_date = today + chrono::Duration::days(365);
            if due_date > max_due_date {
                return Err(AppError::validation(
                    "任务截止日期不能超过1年"
                ));
            }
        }

        Ok(())
    }

    /// 验证任务时间逻辑
    pub fn validate_time_logic(
        estimated_minutes: Option<i32>,
        actual_minutes: i32,
        status: &TaskStatus,
    ) -> AppResult<()> {
        // 实际时间不能为负数
        if actual_minutes < 0 {
            return Err(AppError::validation("实际时间不能为负数"));
        }

        // 如果任务已完成，实际时间应该有记录
        if *status == TaskStatus::Completed && actual_minutes == 0 {
            return Err(AppError::validation(
                "已完成的任务应该记录实际时间"
            ));
        }

        // 如果有预估时间，检查实际时间是否合理
        if let Some(estimated) = estimated_minutes {
            if estimated > 0 && actual_minutes > estimated * 5 {
                return Err(AppError::validation(
                    "实际时间超出预估时间过多，请检查记录是否正确"
                ));
            }
        }

        Ok(())
    }

    /// 验证项目引用
    pub async fn validate_project_reference<F, Fut>(
        project_id: Option<&str>,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        if let Some(project_id) = project_id {
            ValidationUtils::validate_foreign_key(check_fn, "项目", project_id).await?;
        }
        Ok(())
    }

    /// 验证任务层级深度
    pub fn validate_task_hierarchy_depth(depth: i32) -> AppResult<()> {
        const MAX_DEPTH: i32 = 5; // 最大5层嵌套
        
        if depth > MAX_DEPTH {
            return Err(AppError::validation(&format!(
                "任务嵌套层级不能超过{}层",
                MAX_DEPTH
            )));
        }

        Ok(())
    }

    /// 验证任务数据完整性
    pub fn validate_data_integrity(data: &CreateTaskData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 高优先级任务应该有截止日期
        if data.priority == Priority::High || data.priority == Priority::Urgent {
            if data.due_date.is_none() {
                ctx.validate(
                    || Err(AppError::validation("高优先级任务应该设置截止日期")),
                    "high_priority_due_date"
                );
            }
        }

        // 有截止日期的任务应该有预估时间
        if data.due_date.is_some() && data.estimated_minutes.is_none() {
            ctx.validate(
                || Err(AppError::validation("有截止日期的任务建议设置预估时间")),
                "due_date_estimated_time"
            );
        }

        // 任务标题不能包含特殊字符
        let invalid_chars = ['<', '>', ':', '"', '|', '?', '*'];
        for ch in invalid_chars {
            if data.title.contains(ch) {
                ctx.validate(
                    || Err(AppError::validation(&format!(
                        "任务标题不能包含特殊字符: {}",
                        ch
                    ))),
                    "title_special_chars"
                );
                break;
            }
        }

        ctx.finish()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_task_validation() {
        let validator = CreateTaskValidator;
        
        // 有效数据
        let valid_data = CreateTaskData {
            title: "Test Task".to_string(),
            description: Some("A test task".to_string()),
            priority: Priority::Medium,
            parent_task_id: None,
            project_id: Some("proj123".to_string()),
            area_id: Some("area123".to_string()),
            assigned_to: Some("user123".to_string()),
            due_date: None,
            estimated_minutes: Some(60),
        };
        
        assert!(validator.validate(&valid_data).is_ok());

        // 无效标题
        let invalid_data = CreateTaskData {
            title: "".to_string(),
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());
    }

    #[test]
    fn test_status_transition_validation() {
        use TaskStatus::*;

        // 有效转换
        assert!(TaskBusinessRuleValidator::validate_status_transition(
            &Todo, &InProgress
        ).is_ok());
        
        assert!(TaskBusinessRuleValidator::validate_status_transition(
            &InProgress, &Completed
        ).is_ok());

        // 允许重新打开已完成的任务
        assert!(TaskBusinessRuleValidator::validate_status_transition(
            &Completed, &InProgress
        ).is_ok());
    }

    #[test]
    fn test_completion_requirements() {
        // 有效完成
        assert!(TaskBusinessRuleValidator::validate_completion_requirements(
            &TaskStatus::Completed, 100, false, true
        ).is_ok());

        // 完成度不足
        assert!(TaskBusinessRuleValidator::validate_completion_requirements(
            &TaskStatus::Completed, 80, false, true
        ).is_err());

        // 有未完成子任务
        assert!(TaskBusinessRuleValidator::validate_completion_requirements(
            &TaskStatus::Completed, 100, true, false
        ).is_err());
    }

    #[test]
    fn test_parent_child_relationship() {
        // 有效关系
        assert!(TaskBusinessRuleValidator::validate_parent_child_relationship(
            "task1", Some("task2"), Some("proj1"), Some("proj1")
        ).is_ok());

        // 自己为父任务
        assert!(TaskBusinessRuleValidator::validate_parent_child_relationship(
            "task1", Some("task1"), None, None
        ).is_err());

        // 不同项目
        assert!(TaskBusinessRuleValidator::validate_parent_child_relationship(
            "task1", Some("task2"), Some("proj1"), Some("proj2")
        ).is_err());
    }
}
