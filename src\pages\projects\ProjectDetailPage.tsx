// 项目详情页面
// 显示项目的详细信息，支持编辑和管理

import { Component, createSignal, createEffect, Show, For } from 'solid-js';
import { useParams, useNavigate } from '@solidjs/router';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  progress: number;
  priority: string;
  start_date?: string;
  deadline?: string;
  estimated_hours?: number;
  actual_hours: number;
  area_id?: string;
  area_name?: string;
  created_at: string;
  updated_at: string;
  task_count?: number;
  completed_task_count?: number;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  due_date?: string;
  completion_percentage: number;
  created_at: string;
}

export const ProjectDetailPage: Component = () => {
  const params = useParams();
  const navigate = useNavigate();
  
  const [project, setProject] = createSignal<Project | null>(null);
  const [tasks, setTasks] = createSignal<Task[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [editing, setEditing] = createSignal(false);
  const [editForm, setEditForm] = createSignal({
    name: '',
    description: '',
    priority: 'medium',
    start_date: '',
    deadline: '',
    estimated_hours: 0,
  });

  // 加载项目详情
  const loadProject = async () => {
    try {
      setLoading(true);
      const projectData = await api.getProject(params.id);
      setProject(projectData);
      
      // 设置编辑表单的初始值
      setEditForm({
        name: projectData.name,
        description: projectData.description || '',
        priority: projectData.priority,
        start_date: projectData.start_date || '',
        deadline: projectData.deadline || '',
        estimated_hours: projectData.estimated_hours || 0,
      });
    } catch (error) {
      console.error('Failed to load project:', error);
      toast.error('加载项目详情失败');
      navigate('/projects');
    } finally {
      setLoading(false);
    }
  };

  // 加载项目任务
  const loadTasks = async () => {
    try {
      const taskData = await api.listTasks({
        page: 1,
        page_size: 50,
      });
      
      // 过滤出属于当前项目的任务
      if (Array.isArray(taskData)) {
        const projectTasks = taskData.filter(task => task.project_id === params.id);
        setTasks(projectTasks);
      }
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  };

  // 初始加载
  createEffect(() => {
    if (params.id) {
      loadProject();
      loadTasks();
    }
  });

  // 保存项目编辑
  const handleSave = async () => {
    try {
      const form = editForm();
      await api.updateProject(params.id, {
        name: form.name,
        description: form.description,
        priority: form.priority,
        start_date: form.start_date || undefined,
        deadline: form.deadline || undefined,
        estimated_hours: form.estimated_hours || undefined,
      });
      
      toast.success('项目更新成功');
      setEditing(false);
      loadProject();
    } catch (error) {
      console.error('Failed to update project:', error);
      toast.error('更新项目失败');
    }
  };

  // 更新项目状态
  const handleStatusChange = async (newStatus: string) => {
    try {
      await api.updateProjectStatus(params.id, newStatus);
      toast.success('项目状态更新成功');
      loadProject();
    } catch (error) {
      console.error('Failed to update project status:', error);
      toast.error('更新项目状态失败');
    }
  };

  // 归档项目
  const handleArchive = async () => {
    if (!confirm('确定要归档这个项目吗？')) return;
    
    try {
      await api.archiveProject(params.id);
      toast.success('项目已归档');
      navigate('/projects');
    } catch (error) {
      console.error('Failed to archive project:', error);
      toast.error('归档项目失败');
    }
  };

  // 删除项目
  const handleDelete = async () => {
    if (!confirm('确定要删除这个项目吗？此操作不可恢复！')) return;
    
    try {
      await api.deleteProject(params.id);
      toast.success('项目已删除');
      navigate('/projects');
    } catch (error) {
      console.error('Failed to delete project:', error);
      toast.error('删除项目失败');
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'at_risk': return 'bg-yellow-100 text-yellow-800';
      case 'paused': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'archived': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <MainLayout>
      <div class="max-w-6xl mx-auto p-6">
        <Show when={loading()}>
          <div class="flex justify-center items-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </Show>

        <Show when={!loading() && project()}>
          <div class="space-y-6">
            {/* 页面头部 */}
            <div class="flex justify-between items-start">
              <div>
                <button
                  type="button"
                  class="text-blue-600 hover:text-blue-800 mb-2"
                  onClick={() => navigate('/projects')}
                >
                  ← 返回项目列表
                </button>
                <h1 class="text-3xl font-bold text-gray-900">
                  {project()?.name}
                </h1>
                <Show when={project()?.area_name}>
                  <p class="text-sm text-blue-600 mt-1">
                    领域: {project()?.area_name}
                  </p>
                </Show>
              </div>
              
              <div class="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setEditing(!editing())}
                >
                  {editing() ? '取消编辑' : '编辑项目'}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleArchive}
                >
                  归档
                </Button>
                <Button
                  variant="danger"
                  onClick={handleDelete}
                >
                  删除
                </Button>
              </div>
            </div>

            {/* 项目信息卡片 */}
            <div class="bg-white rounded-lg shadow p-6">
              <Show when={!editing()}>
                {/* 查看模式 */}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 class="text-lg font-semibold mb-4">基本信息</h3>
                    <div class="space-y-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700">描述</label>
                        <p class="mt-1 text-sm text-gray-900">
                          {project()?.description || '暂无描述'}
                        </p>
                      </div>
                      
                      <div class="flex gap-4">
                        <div>
                          <label class="block text-sm font-medium text-gray-700">状态</label>
                          <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project()?.status || '')}`}>
                            {project()?.status}
                          </span>
                        </div>
                        
                        <div>
                          <label class="block text-sm font-medium text-gray-700">优先级</label>
                          <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(project()?.priority || '')}`}>
                            {project()?.priority}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700">进度</label>
                        <div class="mt-1">
                          <div class="bg-gray-200 rounded-full h-2">
                            <div 
                              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={`width: ${project()?.progress || 0}%`}
                            ></div>
                          </div>
                          <span class="text-sm text-gray-600 mt-1">
                            {project()?.progress || 0}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 class="text-lg font-semibold mb-4">时间信息</h3>
                    <div class="space-y-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700">开始日期</label>
                        <p class="mt-1 text-sm text-gray-900">
                          {project()?.start_date || '未设置'}
                        </p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700">截止日期</label>
                        <p class="mt-1 text-sm text-gray-900">
                          {project()?.deadline || '未设置'}
                        </p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700">预估工时</label>
                        <p class="mt-1 text-sm text-gray-900">
                          {project()?.estimated_hours || 0} 小时
                        </p>
                      </div>
                      
                      <div>
                        <label class="block text-sm font-medium text-gray-700">实际工时</label>
                        <p class="mt-1 text-sm text-gray-900">
                          {project()?.actual_hours || 0} 小时
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </Show>

              <Show when={editing()}>
                {/* 编辑模式 */}
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">项目名称</label>
                    <Input
                      type="text"
                      value={editForm().name}
                      onInput={(e) => setEditForm(prev => ({ ...prev, name: e.currentTarget.value }))}
                      placeholder="输入项目名称"
                    />
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">描述</label>
                    <textarea
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      rows="3"
                      value={editForm().description}
                      onInput={(e) => setEditForm(prev => ({ ...prev, description: e.currentTarget.value }))}
                      placeholder="输入项目描述"
                    />
                  </div>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700">优先级</label>
                      <select
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        value={editForm().priority}
                        onChange={(e) => setEditForm(prev => ({ ...prev, priority: e.currentTarget.value }))}
                      >
                        <option value="low">低</option>
                        <option value="medium">中</option>
                        <option value="high">高</option>
                        <option value="urgent">紧急</option>
                      </select>
                    </div>
                    
                    <div>
                      <label class="block text-sm font-medium text-gray-700">预估工时</label>
                      <Input
                        type="number"
                        value={editForm().estimated_hours}
                        onInput={(e) => setEditForm(prev => ({ ...prev, estimated_hours: parseInt(e.currentTarget.value) || 0 }))}
                        placeholder="预估工时（小时）"
                      />
                    </div>
                  </div>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700">开始日期</label>
                      <Input
                        type="date"
                        value={editForm().start_date}
                        onInput={(e) => setEditForm(prev => ({ ...prev, start_date: e.currentTarget.value }))}
                      />
                    </div>
                    
                    <div>
                      <label class="block text-sm font-medium text-gray-700">截止日期</label>
                      <Input
                        type="date"
                        value={editForm().deadline}
                        onInput={(e) => setEditForm(prev => ({ ...prev, deadline: e.currentTarget.value }))}
                      />
                    </div>
                  </div>
                  
                  <div class="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setEditing(false)}
                    >
                      取消
                    </Button>
                    <Button onClick={handleSave}>
                      保存
                    </Button>
                  </div>
                </div>
              </Show>
            </div>

            {/* 状态管理 */}
            <Show when={!editing()}>
              <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-4">状态管理</h3>
                <div class="flex flex-wrap gap-2">
                  <Button
                    variant={project()?.status === 'not_started' ? 'primary' : 'outline'}
                    onClick={() => handleStatusChange('not_started')}
                  >
                    未开始
                  </Button>
                  <Button
                    variant={project()?.status === 'in_progress' ? 'primary' : 'outline'}
                    onClick={() => handleStatusChange('in_progress')}
                  >
                    进行中
                  </Button>
                  <Button
                    variant={project()?.status === 'at_risk' ? 'primary' : 'outline'}
                    onClick={() => handleStatusChange('at_risk')}
                  >
                    有风险
                  </Button>
                  <Button
                    variant={project()?.status === 'paused' ? 'primary' : 'outline'}
                    onClick={() => handleStatusChange('paused')}
                  >
                    暂停
                  </Button>
                  <Button
                    variant={project()?.status === 'completed' ? 'primary' : 'outline'}
                    onClick={() => handleStatusChange('completed')}
                  >
                    已完成
                  </Button>
                </div>
              </div>
            </Show>

            {/* 项目任务 */}
            <div class="bg-white rounded-lg shadow p-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">项目任务</h3>
                <Button
                  onClick={() => navigate(`/tasks/new?project_id=${params.id}`)}
                >
                  添加任务
                </Button>
              </div>
              
              <Show when={tasks().length === 0}>
                <div class="text-center py-8 text-gray-500">
                  暂无任务，点击上方按钮添加第一个任务
                </div>
              </Show>
              
              <Show when={tasks().length > 0}>
                <div class="space-y-3">
                  <For each={tasks()}>
                    {(task) => (
                      <div class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                           onClick={() => navigate(`/tasks/${task.id}`)}>
                        <div class="flex justify-between items-start">
                          <div class="flex-1">
                            <h4 class="font-medium text-gray-900">{task.title}</h4>
                            <Show when={task.description}>
                              <p class="text-sm text-gray-600 mt-1">{task.description}</p>
                            </Show>
                            <div class="flex items-center gap-4 mt-2">
                              <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(task.status)}`}>
                                {task.status}
                              </span>
                              <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}`}>
                                {task.priority}
                              </span>
                              <Show when={task.due_date}>
                                <span class="text-xs text-gray-500">
                                  截止: {task.due_date}
                                </span>
                              </Show>
                            </div>
                          </div>
                          <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">
                              {task.completion_percentage}%
                            </div>
                            <div class="w-16 bg-gray-200 rounded-full h-2 mt-1">
                              <div 
                                class="bg-blue-600 h-2 rounded-full"
                                style={`width: ${task.completion_percentage}%`}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </For>
                </div>
              </Show>
            </div>
          </div>
        </Show>
      </div>
    </MainLayout>
  );
};
