// 用户仓储实现
// 实现用户数据访问的具体逻辑

use crate::domain::entities::user::{User, CreateUserData, UpdateUserData, UserQuery};
use crate::domain::repositories::user_repository::UserRepository;
use crate::infrastructure::database::models::UserModel;
use crate::infrastructure::database::mappers::{UserMapper, Mapper};
use crate::infrastructure::database::DatabaseUtils;
use crate::shared::types::{EntityId, QueryParams};
use crate::shared::errors::{AppError, AppResult};
use async_trait::async_trait;
use sqlx::{SqlitePool, Row};
use chrono::Utc;

/// 用户仓储实现
pub struct UserRepositoryImpl {
    pool: SqlitePool,
}

impl UserRepositoryImpl {
    /// 创建新的用户仓储实现
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserRepository for UserRepositoryImpl {
    async fn create(&self, data: CreateUserData) -> AppResult<User> {
        // 检查用户名是否已存在
        if self.username_exists(&data.username).await? {
            return Err(AppError::validation("用户名已存在"));
        }

        // 检查邮箱是否已存在
        if let Some(ref email) = data.email {
            if self.email_exists(email).await? {
                return Err(AppError::validation("邮箱已存在"));
            }
        }

        // 创建用户实体
        let user = User::new(data)?;
        let model = UserMapper::to_model(&user);

        // 插入数据库
        sqlx::query!(
            r#"
            INSERT INTO users (
                id, username, email, password_hash, full_name, avatar_url,
                timezone, language, is_active, created_at, updated_at, last_login_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            model.id,
            model.username,
            model.email,
            model.password_hash,
            model.full_name,
            model.avatar_url,
            model.timezone,
            model.language,
            model.is_active,
            model.created_at,
            model.updated_at,
            model.last_login_at
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create user: {}", e)))?;

        Ok(user)
    }

    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<User>> {
        let model = sqlx::query_as!(
            UserModel,
            "SELECT * FROM users WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find user by id: {}", e)))?;

        match model {
            Some(model) => Ok(Some(UserMapper::to_domain(model)?)),
            None => Ok(None),
        }
    }

    async fn find_by_username(&self, username: &str) -> AppResult<Option<User>> {
        let model = sqlx::query_as!(
            UserModel,
            "SELECT * FROM users WHERE username = ?",
            username
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find user by username: {}", e)))?;

        match model {
            Some(model) => Ok(Some(UserMapper::to_domain(model)?)),
            None => Ok(None),
        }
    }

    async fn find_by_email(&self, email: &str) -> AppResult<Option<User>> {
        let model = sqlx::query_as!(
            UserModel,
            "SELECT * FROM users WHERE email = ?",
            email
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find user by email: {}", e)))?;

        match model {
            Some(model) => Ok(Some(UserMapper::to_domain(model)?)),
            None => Ok(None),
        }
    }

    async fn update(&self, id: &EntityId, data: UpdateUserData) -> AppResult<User> {
        // 先获取现有用户
        let mut user = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("用户"))?;

        // 检查邮箱是否已被其他用户使用
        if let Some(ref email) = data.email {
            if let Some(existing_user) = self.find_by_email(email).await? {
                if existing_user.id != *id {
                    return Err(AppError::validation("邮箱已被其他用户使用"));
                }
            }
        }

        // 更新用户实体
        user.update(data)?;
        let model = UserMapper::to_model(&user);

        // 更新数据库
        sqlx::query!(
            r#"
            UPDATE users SET
                email = ?, full_name = ?, avatar_url = ?, timezone = ?,
                language = ?, updated_at = ?
            WHERE id = ?
            "#,
            model.email,
            model.full_name,
            model.avatar_url,
            model.timezone,
            model.language,
            model.updated_at,
            model.id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update user: {}", e)))?;

        Ok(user)
    }

    async fn delete(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "DELETE FROM users WHERE id = ?",
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to delete user: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("用户"));
        }

        Ok(())
    }

    async fn find_all(&self, query: UserQuery, params: QueryParams) -> AppResult<Vec<User>> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件
        if let Some(username) = &query.username {
            conditions.push("username LIKE ?".to_string());
            query_params.push(format!("%{}%", username));
        }

        if let Some(email) = &query.email {
            conditions.push("email LIKE ?".to_string());
            query_params.push(format!("%{}%", email));
        }

        if let Some(is_active) = query.is_active {
            conditions.push("is_active = ?".to_string());
            query_params.push(is_active.to_string());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
            query_params.push(created_after.to_rfc3339());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
            query_params.push(created_before.to_rfc3339());
        }

        // 构建完整查询
        let base_query = "SELECT * FROM users";
        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!(" WHERE {}", conditions.join(" AND "))
        };

        let order_clause = DatabaseUtils::build_order_clause(&params.sort.unwrap_or_default());
        let limit_clause = DatabaseUtils::build_limit_clause(&params.pagination);

        let final_query = format!("{}{}{}{}", base_query, where_clause, order_clause, limit_clause);

        let models = sqlx::query_as::<_, UserModel>(&final_query)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to find users: {}", e)))?;

        let users = models.into_iter()
            .map(|model| UserMapper::to_domain(model))
            .collect::<AppResult<Vec<_>>>()?;

        Ok(users)
    }

    async fn count(&self, query: UserQuery) -> AppResult<u64> {
        let mut conditions = Vec::new();

        // 构建查询条件（与find_all相同的逻辑）
        if let Some(username) = &query.username {
            conditions.push("username LIKE ?".to_string());
        }

        if let Some(email) = &query.email {
            conditions.push("email LIKE ?".to_string());
        }

        if let Some(is_active) = query.is_active {
            conditions.push("is_active = ?".to_string());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
        }

        let base_query = "SELECT COUNT(*) FROM users";
        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!(" WHERE {}", conditions.join(" AND "))
        };

        let final_query = format!("{}{}", base_query, where_clause);

        let count = sqlx::query_scalar::<_, i64>(&final_query)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count users: {}", e)))?;

        Ok(count as u64)
    }

    async fn exists(&self, id: &EntityId) -> AppResult<bool> {
        let count = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM users WHERE id = ?",
            id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check user existence: {}", e)))?;

        Ok(count > 0)
    }

    async fn username_exists(&self, username: &str) -> AppResult<bool> {
        let count = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM users WHERE username = ?",
            username
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check username existence: {}", e)))?;

        Ok(count > 0)
    }

    async fn email_exists(&self, email: &str) -> AppResult<bool> {
        let count = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM users WHERE email = ?",
            email
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check email existence: {}", e)))?;

        Ok(count > 0)
    }

    async fn update_last_login(&self, id: &EntityId) -> AppResult<()> {
        let now = Utc::now();

        sqlx::query!(
            "UPDATE users SET last_login_at = ?, updated_at = ? WHERE id = ?",
            now,
            now,
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update last login: {}", e)))?;

        Ok(())
    }

    async fn activate_user(&self, id: &EntityId) -> AppResult<()> {
        let now = Utc::now();

        sqlx::query!(
            "UPDATE users SET is_active = ?, updated_at = ? WHERE id = ?",
            true,
            now,
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to activate user: {}", e)))?;

        Ok(())
    }

    async fn deactivate_user(&self, id: &EntityId) -> AppResult<()> {
        let now = Utc::now();

        sqlx::query!(
            "UPDATE users SET is_active = ?, updated_at = ? WHERE id = ?",
            false,
            now,
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to deactivate user: {}", e)))?;

        Ok(())
    }
    async fn count(&self, query: UserQuery) -> AppResult<u64> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件（与find_all相同的逻辑）
        if let Some(username) = &query.username {
            conditions.push("username LIKE ?".to_string());
            query_params.push(format!("%{}%", username));
        }

        if let Some(email) = &query.email {
            conditions.push("email LIKE ?".to_string());
            query_params.push(format!("%{}%", email));
        }

        if let Some(is_active) = query.is_active {
            conditions.push("is_active = ?".to_string());
            query_params.push(is_active.to_string());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
            query_params.push(created_after.to_rfc3339());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
            query_params.push(created_before.to_rfc3339());
        }

        let base_query = "SELECT COUNT(*) FROM users";
        let sql = format!("{}{}", base_query, DatabaseUtils::build_where_clause(conditions));

        let mut query_builder = sqlx::query(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let row = query_builder
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count users: {}", e)))?;

        let count: i64 = row.get(0);
        Ok(count as u64)
    }

    async fn username_exists(&self, username: &str) -> AppResult<bool> {
        let row = sqlx::query!(
            "SELECT COUNT(*) as count FROM users WHERE username = ?",
            username
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check username existence: {}", e)))?;

        Ok(row.count > 0)
    }

    async fn email_exists(&self, email: &str) -> AppResult<bool> {
        let row = sqlx::query!(
            "SELECT COUNT(*) as count FROM users WHERE email = ?",
            email
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check email existence: {}", e)))?;

        Ok(row.count > 0)
    }

    async fn activate(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE users SET is_active = true, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to activate user: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("用户"));
        }

        Ok(())
    }

    async fn deactivate(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE users SET is_active = false, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to deactivate user: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("用户"));
        }

        Ok(())
    }

    async fn update_last_login(&self, id: &EntityId) -> AppResult<()> {
        let now = Utc::now();
        let result = sqlx::query!(
            "UPDATE users SET last_login_at = ?, updated_at = ? WHERE id = ?",
            now,
            now,
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update last login: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("用户"));
        }

        Ok(())
    }

    async fn update_password(&self, id: &EntityId, password_hash: &str) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?",
            password_hash,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update password: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("用户"));
        }

        Ok(())
    }

    async fn find_active_users(&self, params: QueryParams) -> AppResult<Vec<User>> {
        let query = UserQuery {
            username: None,
            email: None,
            is_active: Some(true),
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_recent_users(&self, days: i32, params: QueryParams) -> AppResult<Vec<User>> {
        let created_after = Utc::now() - chrono::Duration::days(days as i64);
        let query = UserQuery {
            username: None,
            email: None,
            is_active: None,
            created_after: Some(created_after),
            created_before: None,
        };

        self.find_all(query, params).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;

    async fn setup_test_db() -> SqlitePool {
        let pool = SqlitePool::connect(":memory:").await.unwrap();
        
        // 运行迁移
        sqlx::migrate!("./migrations")
            .run(&pool)
            .await
            .unwrap();

        pool
    }

    #[tokio::test]
    async fn test_user_repository_create() {
        let pool = setup_test_db().await;
        let repo = UserRepositoryImpl::new(pool);

        let data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "password123".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: None,
            language: None,
        };

        let user = repo.create(data).await.unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
    }

    #[tokio::test]
    async fn test_user_repository_find_by_id() {
        let pool = setup_test_db().await;
        let repo = UserRepositoryImpl::new(pool);

        let data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "password123".to_string(),
            full_name: None,
            timezone: None,
            language: None,
        };

        let created_user = repo.create(data).await.unwrap();
        let found_user = repo.find_by_id(&created_user.id).await.unwrap();

        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().id, created_user.id);
    }
}
