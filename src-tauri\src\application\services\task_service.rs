// 任务应用服务
// 实现任务相关的业务用例

use crate::application::services::{Service, ServiceContext, ServiceResult, PagedResult, ServiceUtils};
use crate::domain::entities::task::{Task, CreateTaskData, UpdateTaskData, TaskQuery};
use crate::domain::repositories::task_repository::{TaskRepository, TaskHierarchy, ProjectTaskStats, UserTaskStats};
use crate::shared::types::{EntityId, QueryParams, Pagination, TaskStatus, Priority};
use crate::shared::errors::{AppError, AppResult};
use std::sync::Arc;

/// 任务应用服务
pub struct TaskService {
    task_repository: Arc<dyn TaskRepository>,
}

impl TaskService {
    /// 创建新的任务服务
    pub fn new(task_repository: Arc<dyn TaskRepository>) -> Self {
        Self { task_repository }
    }

    /// 创建任务
    pub async fn create_task(
        &self,
        context: &ServiceContext,
        data: CreateTaskData,
    ) -> AppResult<ServiceResult<Task>> {
        let user_id = context.user_id()?;

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            task_title = %data.title,
            "Creating new task"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.create(data).await
        }).await;

        match result {
            Ok(task) => {
                ServiceUtils::log_audit_event(
                    context,
                    "create_task",
                    "task",
                    &task.id,
                    Some(&format!("Created task: {}", task.title)),
                );

                Ok(ServiceResult::new(task)
                    .with_execution_time(execution_time)
                    .with_affected_entity(task.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to create task"
                );
                Err(e)
            }
        }
    }

    /// 获取任务
    pub async fn get_task(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<Option<Task>>> {
        tracing::debug!(
            request_id = %context.request_id,
            task_id = %id,
            "Getting task"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.find_by_id(id).await
        }).await;

        match result {
            Ok(task_opt) => {
                if let Some(ref task) = task_opt {
                    // 检查权限：只能查看分配给自己的任务或自己创建的项目中的任务
                    let user_id = context.user_id()?;
                    if let Some(ref assigned_to) = task.assigned_to {
                        if assigned_to != user_id {
                            return Err(AppError::forbidden("无权访问此任务"));
                        }
                    }

                    ServiceUtils::log_audit_event(
                        context,
                        "get_task",
                        "task",
                        id,
                        Some("Task retrieved successfully"),
                    );
                }

                Ok(ServiceResult::new(task_opt).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    task_id = %id,
                    "Failed to get task"
                );
                Err(e)
            }
        }
    }

    /// 更新任务
    pub async fn update_task(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        data: UpdateTaskData,
    ) -> AppResult<ServiceResult<Task>> {
        // 先获取任务检查权限
        let existing_task = self.task_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        let user_id = context.user_id()?;
        if let Some(ref assigned_to) = existing_task.assigned_to {
            if assigned_to != user_id {
                return Err(AppError::forbidden("无权修改此任务"));
            }
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            task_id = %id,
            "Updating task"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.update(id, data).await
        }).await;

        match result {
            Ok(task) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_task",
                    "task",
                    &task.id,
                    Some(&format!("Updated task: {}", task.title)),
                );

                Ok(ServiceResult::new(task)
                    .with_execution_time(execution_time)
                    .with_affected_entity(task.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    task_id = %id,
                    "Failed to update task"
                );
                Err(e)
            }
        }
    }

    /// 删除任务
    pub async fn delete_task(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 先获取任务检查权限
        let existing_task = self.task_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        let user_id = context.user_id()?;
        if let Some(ref assigned_to) = existing_task.assigned_to {
            if assigned_to != user_id {
                return Err(AppError::forbidden("无权删除此任务"));
            }
        }

        tracing::warn!(
            request_id = %context.request_id,
            user_id = %user_id,
            task_id = %id,
            "Deleting task"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.delete(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "delete_task",
                    "task",
                    id,
                    Some("Task deleted successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    task_id = %id,
                    "Failed to delete task"
                );
                Err(e)
            }
        }
    }

    /// 完成任务
    pub async fn complete_task(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<Task>> {
        // 先获取任务检查权限
        let existing_task = self.task_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        let user_id = context.user_id()?;
        if existing_task.created_by != *user_id && existing_task.assigned_to.as_ref() != Some(user_id) {
            return Err(AppError::forbidden("无权完成此任务"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            task_id = %id,
            "Completing task"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.update_status(id, crate::shared::types::TaskStatus::Completed).await?;
            self.task_repository.update_completion(id, 100).await?;
            self.task_repository.find_by_id(id).await
        }).await;

        match result {
            Ok(Some(task)) => {
                ServiceUtils::log_audit_event(
                    context,
                    "complete_task",
                    "task",
                    id,
                    Some("Task completed successfully"),
                );

                Ok(ServiceResult::new(task)
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Ok(None) => Err(AppError::not_found("任务")),
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    task_id = %id,
                    "Failed to complete task"
                );
                Err(e)
            }
        }
    }

    /// 列出用户的任务
    pub async fn list_user_tasks(
        &self,
        context: &ServiceContext,
        page: u32,
        page_size: u32,
        status_filter: Option<TaskStatus>,
    ) -> AppResult<ServiceResult<PagedResult<Task>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            page = %page,
            page_size = %page_size,
            "Listing user tasks"
        );

        let query = TaskQuery {
            title: None,
            status: status_filter,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: Some(user_id.clone()),
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: None,
            sort: None,
            filters: None,
        };

        let (tasks_result, execution_time) = ServiceUtils::measure_time(async {
            let tasks = self.task_repository.find_all(query.clone(), params).await?;
            let total_count = self.task_repository.count(query).await?;
            Ok::<(Vec<Task>, u64), AppError>((tasks, total_count))
        }).await;

        match tasks_result {
            Ok((tasks, total_count)) => {
                let paged_result = PagedResult::new(tasks, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "list_user_tasks",
                    "task",
                    "multiple",
                    Some(&format!("Retrieved {} tasks", paged_result.len())),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to list user tasks"
                );
                Err(e)
            }
        }
    }

    /// 更新任务状态
    pub async fn update_task_status(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        status: TaskStatus,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限
        let existing_task = self.task_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        let user_id = context.user_id()?;
        if let Some(ref assigned_to) = existing_task.assigned_to {
            if assigned_to != user_id {
                return Err(AppError::forbidden("无权修改此任务状态"));
            }
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            task_id = %id,
            new_status = ?status,
            "Updating task status"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.update_status(id, status).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_task_status",
                    "task",
                    id,
                    Some(&format!("Updated task status to: {:?}", status)),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    task_id = %id,
                    "Failed to update task status"
                );
                Err(e)
            }
        }
    }

    /// 更新任务完成百分比
    pub async fn update_task_completion(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        percentage: u8,
    ) -> AppResult<ServiceResult<()>> {
        if percentage > 100 {
            return Err(AppError::validation("完成百分比不能超过100%"));
        }

        // 检查权限
        let existing_task = self.task_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("任务"))?;

        let user_id = context.user_id()?;
        if let Some(ref assigned_to) = existing_task.assigned_to {
            if assigned_to != user_id {
                return Err(AppError::forbidden("无权修改此任务完成度"));
            }
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            task_id = %id,
            percentage = %percentage,
            "Updating task completion"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.update_completion_percentage(id, percentage).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_task_completion",
                    "task",
                    id,
                    Some(&format!("Updated task completion to: {}%", percentage)),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    task_id = %id,
                    "Failed to update task completion"
                );
                Err(e)
            }
        }
    }

    /// 获取逾期任务
    pub async fn get_overdue_tasks(
        &self,
        context: &ServiceContext,
        page: u32,
        page_size: u32,
    ) -> AppResult<ServiceResult<PagedResult<Task>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            "Getting overdue tasks"
        );

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: None,
            sort: None,
            filters: None,
        };

        let (result, execution_time) = ServiceUtils::measure_time(async {
            let mut tasks = self.task_repository.find_overdue(params).await?;
            // 只返回分配给当前用户的任务
            tasks.retain(|t| t.assigned_to.as_ref() == Some(user_id));
            let total_count = tasks.len() as u64;
            Ok::<(Vec<Task>, u64), AppError>((tasks, total_count))
        }).await;

        match result {
            Ok((tasks, total_count)) => {
                let paged_result = PagedResult::new(tasks, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "get_overdue_tasks",
                    "task",
                    "multiple",
                    Some(&format!("Retrieved {} overdue tasks", paged_result.len())),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to get overdue tasks"
                );
                Err(e)
            }
        }
    }

    /// 获取任务层级结构
    pub async fn get_task_hierarchy(
        &self,
        context: &ServiceContext,
        root_task_id: Option<&EntityId>,
    ) -> AppResult<ServiceResult<Vec<TaskHierarchy>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            root_task_id = ?root_task_id,
            "Getting task hierarchy"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            let mut hierarchy = self.task_repository.get_task_hierarchy(root_task_id).await?;
            // 只返回分配给当前用户的任务
            hierarchy.retain(|h| h.task.assigned_to.as_ref() == Some(user_id));
            Ok::<Vec<TaskHierarchy>, AppError>(hierarchy)
        }).await;

        match result {
            Ok(hierarchy) => {
                ServiceUtils::log_audit_event(
                    context,
                    "get_task_hierarchy",
                    "task",
                    "multiple",
                    Some(&format!("Retrieved task hierarchy with {} items", hierarchy.len())),
                );

                Ok(ServiceResult::new(hierarchy).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to get task hierarchy"
                );
                Err(e)
            }
        }
    }

    /// 获取用户任务统计
    pub async fn get_user_task_stats(
        &self,
        context: &ServiceContext,
    ) -> AppResult<ServiceResult<UserTaskStats>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            "Getting user task stats"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.task_repository.get_user_task_stats(user_id).await
        }).await;

        match result {
            Ok(stats) => {
                ServiceUtils::log_audit_event(
                    context,
                    "get_user_task_stats",
                    "task",
                    user_id,
                    Some("Retrieved user task statistics"),
                );

                Ok(ServiceResult::new(stats).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %user_id,
                    "Failed to get user task stats"
                );
                Err(e)
            }
        }
    }

    /// 搜索任务
    pub async fn search_tasks(
        &self,
        context: &ServiceContext,
        keyword: &str,
        page: u32,
        page_size: u32,
    ) -> AppResult<ServiceResult<PagedResult<Task>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            keyword = %keyword,
            "Searching tasks"
        );

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: Some(keyword.to_string()),
            sort: None,
            filters: None,
        };

        let (result, execution_time) = ServiceUtils::measure_time(async {
            let mut tasks = self.task_repository.search(keyword, params).await?;
            // 只返回分配给当前用户的任务
            tasks.retain(|t| t.assigned_to.as_ref() == Some(user_id));
            let total_count = tasks.len() as u64;
            Ok::<(Vec<Task>, u64), AppError>((tasks, total_count))
        }).await;

        match result {
            Ok((tasks, total_count)) => {
                let paged_result = PagedResult::new(tasks, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "search_tasks",
                    "task",
                    "multiple",
                    Some(&format!("Found {} tasks for keyword: {}", paged_result.len(), keyword)),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    keyword = %keyword,
                    "Failed to search tasks"
                );
                Err(e)
            }
        }
    }
}

impl Service for TaskService {
    fn name(&self) -> &'static str {
        "TaskService"
    }

    async fn health_check(&self) -> AppResult<()> {
        let query = TaskQuery {
            title: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_before: None,
            due_after: None,
            created_after: None,
            created_before: None,
        };
        
        let params = QueryParams {
            pagination: Some(Pagination::new(1, 1)),
            search: None,
            sort: None,
            filters: None,
        };

        self.task_repository.find_all(query, params).await?;
        Ok(())
    }
}
