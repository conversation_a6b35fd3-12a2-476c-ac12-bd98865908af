-- PaoLife 定期任务系统表结构
-- 创建时间: 2025-01-29
-- 版本: 1.1.0

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 定期任务表
CREATE TABLE recurring_tasks (
    id TEXT PRIMARY KEY,
    area_id TEXT NOT NULL REFERENCES areas(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    recurrence_pattern TEXT NOT NULL
        CHECK (recurrence_pattern IN ('daily', 'weekly', 'monthly', 'yearly')),
    recurrence_interval INTEGER NOT NULL DEFAULT 1, -- 每N个周期
    recurrence_days TEXT, -- JSON数组，存储星期几或月份中的日期
    start_date DATE NOT NULL,
    end_date DATE,
    next_due_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    auto_create_tasks BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 定期任务执行记录表
CREATE TABLE recurring_task_executions (
    id TEXT PRIMARY KEY,
    recurring_task_id TEXT NOT NULL REFERENCES recurring_tasks(id) ON DELETE CASCADE,
    scheduled_date DATE NOT NULL,
    created_task_id TEXT REFERENCES tasks(id) ON DELETE SET NULL,
    execution_status TEXT NOT NULL DEFAULT 'pending'
        CHECK (execution_status IN ('pending', 'created', 'skipped', 'failed')),
    execution_note TEXT,
    executed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 索引定义
-- ================================

-- 定期任务表索引
CREATE INDEX idx_recurring_tasks_area ON recurring_tasks(area_id);
CREATE INDEX idx_recurring_tasks_active ON recurring_tasks(is_active);
CREATE INDEX idx_recurring_tasks_next_due ON recurring_tasks(next_due_date, is_active);
CREATE INDEX idx_recurring_tasks_pattern ON recurring_tasks(recurrence_pattern);
CREATE INDEX idx_recurring_tasks_created_at ON recurring_tasks(created_at);

-- 定期任务执行记录表索引
CREATE INDEX idx_recurring_task_executions_task ON recurring_task_executions(recurring_task_id);
CREATE INDEX idx_recurring_task_executions_date ON recurring_task_executions(scheduled_date);
CREATE INDEX idx_recurring_task_executions_status ON recurring_task_executions(execution_status);
CREATE INDEX idx_recurring_task_executions_created_task ON recurring_task_executions(created_task_id);

-- ================================
-- 触发器定义
-- ================================

-- 自动更新 updated_at 字段的触发器
CREATE TRIGGER trigger_recurring_tasks_updated_at
    AFTER UPDATE ON recurring_tasks
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE recurring_tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 定期任务执行时自动设置 executed_at
CREATE TRIGGER trigger_recurring_task_executions_executed_at
    AFTER UPDATE ON recurring_task_executions
    FOR EACH ROW
    WHEN NEW.execution_status IN ('created', 'skipped', 'failed') AND OLD.execution_status = 'pending'
BEGIN
    UPDATE recurring_task_executions SET executed_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- ================================
-- 视图定义
-- ================================

-- 定期任务统计视图
CREATE VIEW recurring_task_stats AS
SELECT
    rt.id,
    rt.title,
    rt.area_id,
    rt.recurrence_pattern,
    rt.is_active,
    COUNT(rte.id) as total_executions,
    COUNT(CASE WHEN rte.execution_status = 'created' THEN 1 END) as successful_executions,
    COUNT(CASE WHEN rte.execution_status = 'skipped' THEN 1 END) as skipped_executions,
    COUNT(CASE WHEN rte.execution_status = 'failed' THEN 1 END) as failed_executions,
    CASE
        WHEN COUNT(rte.id) > 0 THEN
            ROUND(COUNT(CASE WHEN rte.execution_status = 'created' THEN 1 END) * 100.0 / COUNT(rte.id), 2)
        ELSE 0
    END as success_rate,
    rt.next_due_date,
    rt.created_at
FROM recurring_tasks rt
LEFT JOIN recurring_task_executions rte ON rt.id = rte.recurring_task_id
GROUP BY rt.id, rt.title, rt.area_id, rt.recurrence_pattern, rt.is_active, rt.next_due_date, rt.created_at;

-- ================================
-- 初始数据插入
-- ================================

-- 插入示例定期任务（需要先有领域数据）
-- 这些数据将在有实际领域创建后通过应用程序添加

-- 插入应用配置
INSERT INTO app_settings (setting_key, setting_value, setting_type, description, is_user_configurable) VALUES
('recurring_task_check_interval_minutes', '60', 'number', '定期任务检查间隔（分钟）', TRUE),
('recurring_task_advance_days', '1', 'number', '提前创建任务的天数', TRUE),
('recurring_task_auto_cleanup_days', '90', 'number', '自动清理执行记录的天数', TRUE),
('recurring_task_max_missed_executions', '5', 'number', '最大错过执行次数', TRUE);

-- ================================
-- 函数定义（SQLite 不支持存储过程，这些逻辑需要在应用层实现）
-- ================================

-- 注释：以下逻辑需要在 Rust 应用层实现

-- 1. 计算下次执行日期的函数
-- calculate_next_due_date(recurring_task_id, current_date) -> next_date

-- 2. 创建定期任务实例的函数
-- create_task_from_recurring(recurring_task_id, scheduled_date) -> task_id

-- 3. 检查并创建到期任务的函数
-- process_due_recurring_tasks(current_date) -> created_count

-- 4. 清理过期执行记录的函数
-- cleanup_old_executions(days_to_keep) -> deleted_count

-- ================================
-- 示例定期任务配置（JSON格式说明）
-- ================================

-- recurrence_days 字段的 JSON 格式示例：

-- 每周任务（周一、周三、周五）:
-- '{"weekdays": [1, 3, 5]}'

-- 每月任务（每月1号和15号）:
-- '{"monthdays": [1, 15]}'

-- 每年任务（每年1月1日和7月1日）:
-- '{"yeardays": [{"month": 1, "day": 1}, {"month": 7, "day": 1}]}'

-- 工作日任务:
-- '{"weekdays": [1, 2, 3, 4, 5]}'

-- 周末任务:
-- '{"weekdays": [6, 7]}'
