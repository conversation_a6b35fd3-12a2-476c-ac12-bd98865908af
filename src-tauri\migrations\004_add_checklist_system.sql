-- PaoLife 清单系统表结构
-- 创建时间: 2025-01-29
-- 版本: 1.1.0

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 清单模板表
CREATE TABLE checklist_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT CHECK (category IN ('project', 'area', 'general')),
    is_public BOOLEAN DEFAULT FALSE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 清单项目表
CREATE TABLE checklist_items (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES checklist_templates(id) ON DELETE CASCADE,
    item_text TEXT NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_required BOOLEAN DEFAULT FALSE,
    estimated_minutes INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 清单实例表
CREATE TABLE checklist_instances (
    id TEXT PRIMARY KEY,
    template_id TEXT NOT NULL REFERENCES checklist_templates(id),
    title TEXT NOT NULL,
    project_id TEXT REFERENCES projects(id) ON DELETE SET NULL,
    area_id TEXT REFERENCES areas(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'active'
        CHECK (status IN ('active', 'completed', 'cancelled')),
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- 清单实例项目表
CREATE TABLE checklist_instance_items (
    id TEXT PRIMARY KEY,
    instance_id TEXT NOT NULL REFERENCES checklist_instances(id) ON DELETE CASCADE,
    template_item_id TEXT NOT NULL REFERENCES checklist_items(id),
    is_completed BOOLEAN DEFAULT FALSE,
    completion_note TEXT,
    completed_by TEXT REFERENCES users(id),
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 索引定义
-- ================================

-- 清单模板表索引
CREATE INDEX idx_checklist_templates_category ON checklist_templates(category);
CREATE INDEX idx_checklist_templates_created_by ON checklist_templates(created_by);
CREATE INDEX idx_checklist_templates_is_public ON checklist_templates(is_public);
CREATE INDEX idx_checklist_templates_created_at ON checklist_templates(created_at);

-- 清单项目表索引
CREATE INDEX idx_checklist_items_template ON checklist_items(template_id);
CREATE INDEX idx_checklist_items_sort ON checklist_items(template_id, sort_order);
CREATE INDEX idx_checklist_items_required ON checklist_items(is_required);

-- 清单实例表索引
CREATE INDEX idx_checklist_instances_template ON checklist_instances(template_id);
CREATE INDEX idx_checklist_instances_project ON checklist_instances(project_id);
CREATE INDEX idx_checklist_instances_area ON checklist_instances(area_id);
CREATE INDEX idx_checklist_instances_status ON checklist_instances(status);
CREATE INDEX idx_checklist_instances_created_by ON checklist_instances(created_by);
CREATE INDEX idx_checklist_instances_created_at ON checklist_instances(created_at);

-- 清单实例项目表索引
CREATE INDEX idx_checklist_instance_items_instance ON checklist_instance_items(instance_id);
CREATE INDEX idx_checklist_instance_items_template_item ON checklist_instance_items(template_item_id);
CREATE INDEX idx_checklist_instance_items_completed ON checklist_instance_items(is_completed);
CREATE INDEX idx_checklist_instance_items_completed_by ON checklist_instance_items(completed_by);

-- ================================
-- 触发器定义
-- ================================

-- 自动更新 updated_at 字段的触发器
CREATE TRIGGER trigger_checklist_templates_updated_at
    AFTER UPDATE ON checklist_templates
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE checklist_templates SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 清单项目完成时自动设置 completed_at
CREATE TRIGGER trigger_checklist_instance_items_completed_at
    AFTER UPDATE ON checklist_instance_items
    FOR EACH ROW
    WHEN NEW.is_completed = TRUE AND OLD.is_completed = FALSE
BEGIN
    UPDATE checklist_instance_items SET completed_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 清单项目取消完成时清除 completed_at
CREATE TRIGGER trigger_checklist_instance_items_uncompleted_at
    AFTER UPDATE ON checklist_instance_items
    FOR EACH ROW
    WHEN NEW.is_completed = FALSE AND OLD.is_completed = TRUE
BEGIN
    UPDATE checklist_instance_items SET completed_at = NULL WHERE id = NEW.id;
END;

-- 清单实例完成时自动设置 completed_at
CREATE TRIGGER trigger_checklist_instances_completed_at
    AFTER UPDATE ON checklist_instances
    FOR EACH ROW
    WHEN NEW.status = 'completed' AND OLD.status != 'completed'
BEGIN
    UPDATE checklist_instances SET completed_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 清单实例状态变更时清除 completed_at
CREATE TRIGGER trigger_checklist_instances_uncompleted_at
    AFTER UPDATE ON checklist_instances
    FOR EACH ROW
    WHEN NEW.status != 'completed' AND OLD.status = 'completed'
BEGIN
    UPDATE checklist_instances SET completed_at = NULL WHERE id = NEW.id;
END;

-- 自动更新清单实例完成百分比
CREATE TRIGGER trigger_update_checklist_completion_percentage
    AFTER UPDATE ON checklist_instance_items
    FOR EACH ROW
    WHEN NEW.is_completed != OLD.is_completed
BEGIN
    UPDATE checklist_instances 
    SET completion_percentage = (
        SELECT ROUND(
            COUNT(CASE WHEN cii.is_completed = TRUE THEN 1 END) * 100.0 / COUNT(*), 0
        )
        FROM checklist_instance_items cii
        WHERE cii.instance_id = NEW.instance_id
    )
    WHERE id = NEW.instance_id;
END;

-- ================================
-- 视图定义
-- ================================

-- 清单模板统计视图
CREATE VIEW checklist_template_stats AS
SELECT
    ct.id,
    ct.name,
    ct.category,
    ct.is_public,
    COUNT(ci.id) as total_items,
    COUNT(CASE WHEN ci.is_required = TRUE THEN 1 END) as required_items,
    COUNT(cin.id) as usage_count,
    AVG(cin.completion_percentage) as avg_completion_rate,
    ct.created_at
FROM checklist_templates ct
LEFT JOIN checklist_items ci ON ct.id = ci.template_id
LEFT JOIN checklist_instances cin ON ct.id = cin.template_id
GROUP BY ct.id, ct.name, ct.category, ct.is_public, ct.created_at;

-- 清单实例详情视图
CREATE VIEW checklist_instance_details AS
SELECT
    ci.id,
    ci.title,
    ci.status,
    ci.completion_percentage,
    ct.name as template_name,
    ct.category as template_category,
    p.name as project_name,
    a.name as area_name,
    COUNT(cii.id) as total_items,
    COUNT(CASE WHEN cii.is_completed = TRUE THEN 1 END) as completed_items,
    COUNT(CASE WHEN cli.is_required = TRUE AND cii.is_completed = FALSE THEN 1 END) as pending_required_items,
    ci.created_at,
    ci.completed_at
FROM checklist_instances ci
JOIN checklist_templates ct ON ci.template_id = ct.id
LEFT JOIN projects p ON ci.project_id = p.id
LEFT JOIN areas a ON ci.area_id = a.id
LEFT JOIN checklist_instance_items cii ON ci.id = cii.instance_id
LEFT JOIN checklist_items cli ON cii.template_item_id = cli.id
GROUP BY ci.id, ci.title, ci.status, ci.completion_percentage, ct.name, ct.category, 
         p.name, a.name, ci.created_at, ci.completed_at;

-- ================================
-- 初始数据插入
-- ================================

-- 插入默认清单模板
INSERT INTO checklist_templates (id, name, description, category, is_public, created_by) VALUES
('template_project_start', '项目启动清单', '新项目启动时的标准检查清单', 'project', TRUE, 'system'),
('template_project_end', '项目结束清单', '项目结束时的标准检查清单', 'project', TRUE, 'system'),
('template_area_review', '领域维护清单', '定期领域维护的标准检查清单', 'area', TRUE, 'system'),
('template_daily_routine', '日常例行清单', '每日例行事务的检查清单', 'general', TRUE, 'system'),
('template_weekly_review', '周度复盘清单', '每周复盘的标准检查清单', 'general', TRUE, 'system');

-- 插入项目启动清单项目
INSERT INTO checklist_items (id, template_id, item_text, description, sort_order, is_required, estimated_minutes) VALUES
('item_ps_1', 'template_project_start', '明确项目目标和成功标准', '确保项目目标清晰、可衡量', 1, TRUE, 30),
('item_ps_2', 'template_project_start', '识别关键利益相关者', '列出所有相关人员和他们的期望', 2, TRUE, 20),
('item_ps_3', 'template_project_start', '制定项目计划和时间线', '创建详细的项目执行计划', 3, TRUE, 60),
('item_ps_4', 'template_project_start', '确定所需资源和预算', '评估人力、物力、财力需求', 4, TRUE, 30),
('item_ps_5', 'template_project_start', '建立沟通机制', '确定汇报频率和沟通渠道', 5, TRUE, 15),
('item_ps_6', 'template_project_start', '识别潜在风险', '列出可能的风险和应对策略', 6, FALSE, 45),
('item_ps_7', 'template_project_start', '设置项目文档结构', '创建项目文件夹和文档模板', 7, FALSE, 20);

-- 插入项目结束清单项目
INSERT INTO checklist_items (id, template_id, item_text, description, sort_order, is_required, estimated_minutes) VALUES
('item_pe_1', 'template_project_end', '验证项目目标达成情况', '检查是否达到预期目标', 1, TRUE, 30),
('item_pe_2', 'template_project_end', '整理项目文档和资料', '归档所有项目相关文档', 2, TRUE, 45),
('item_pe_3', 'template_project_end', '进行项目复盘', '总结经验教训和改进建议', 3, TRUE, 60),
('item_pe_4', 'template_project_end', '感谢团队成员', '向参与人员表达感谢', 4, FALSE, 15),
('item_pe_5', 'template_project_end', '更新个人技能清单', '记录在项目中获得的新技能', 5, FALSE, 20),
('item_pe_6', 'template_project_end', '分享项目成果', '向相关人员展示项目成果', 6, FALSE, 30);

-- 插入领域维护清单项目
INSERT INTO checklist_items (id, template_id, item_text, description, sort_order, is_required, estimated_minutes) VALUES
('item_ar_1', 'template_area_review', '检查领域标准执行情况', '评估是否按照既定标准执行', 1, TRUE, 20),
('item_ar_2', 'template_area_review', '更新领域相关资源', '整理和更新相关文档、工具', 2, TRUE, 30),
('item_ar_3', 'template_area_review', '评估领域指标表现', '查看关键指标的变化趋势', 3, TRUE, 25),
('item_ar_4', 'template_area_review', '清理过期内容', '删除或归档不再需要的内容', 4, FALSE, 20),
('item_ar_5', 'template_area_review', '规划下期重点', '确定下一阶段的重点工作', 5, TRUE, 30);

-- 插入日常例行清单项目
INSERT INTO checklist_items (id, template_id, item_text, description, sort_order, is_required, estimated_minutes) VALUES
('item_dr_1', 'template_daily_routine', '查看今日任务清单', '确认当天需要完成的任务', 1, TRUE, 5),
('item_dr_2', 'template_daily_routine', '检查邮件和消息', '处理重要的邮件和消息', 2, TRUE, 15),
('item_dr_3', 'template_daily_routine', '更新任务进度', '记录任务完成情况', 3, TRUE, 10),
('item_dr_4', 'template_daily_routine', '整理工作环境', '保持工作空间整洁', 4, FALSE, 10),
('item_dr_5', 'template_daily_routine', '规划明日重点', '确定明天的重要任务', 5, TRUE, 10);

-- 插入周度复盘清单项目
INSERT INTO checklist_items (id, template_id, item_text, description, sort_order, is_required, estimated_minutes) VALUES
('item_wr_1', 'template_weekly_review', '回顾本周目标完成情况', '检查周目标的达成度', 1, TRUE, 20),
('item_wr_2', 'template_weekly_review', '分析本周时间分配', '评估时间使用的合理性', 2, TRUE, 15),
('item_wr_3', 'template_weekly_review', '总结本周学习收获', '记录新的知识和技能', 3, FALSE, 15),
('item_wr_4', 'template_weekly_review', '识别需要改进的地方', '找出可以优化的环节', 4, TRUE, 20),
('item_wr_5', 'template_weekly_review', '制定下周计划', '设定下周的重点目标', 5, TRUE, 25),
('item_wr_6', 'template_weekly_review', '整理本周资料', '归档重要文档和资料', 6, FALSE, 15);
