// 项目相关的Tauri命令
// 实现项目管理的前后端通信

use crate::api::common::{ApiState, CommandUtils, PaginationParams, SearchParams};
use crate::api::dto::{CreateProjectRequestDto, UpdateProjectRequestDto, ProjectResponseDto};
use crate::domain::entities::project::CreateProjectData;
use crate::execute_simple_command;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

/// 创建项目命令
#[command]
pub async fn project_create(
    request: CreateProjectRequestDto,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_create",
        Some(user_id),
        async {
            let create_data = CreateProjectData {
                name: request.name,
                description: request.description,
                priority: request.priority,
                start_date: request.start_date,
                deadline: request.deadline,
                estimated_hours: request.estimated_hours,
                area_id: request.area_id,
                created_by: user_id,
            };

            let project_result = state.project_service.create_project(&context, create_data).await?;
            match project_result.data {
                Some(project) => Ok(ProjectResponseDto::from(project)),
                None => Err(crate::shared::errors::AppError::internal_error("项目创建失败"))
            }
        }
    )
}

/// 获取项目命令
#[command]
pub async fn project_get(
    project_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_get",
        Some(user_id),
        async {
            let project_result = state.project_service.get_project(&context, &project_id).await?;
            match project_result.data {
                Some(project) => Ok(ProjectResponseDto::from(project)),
                None => Err(crate::shared::errors::AppError::not_found("项目不存在"))
            }
        }
    )
}

/// 更新项目命令
#[command]
pub async fn project_update(
    project_id: String,
    request: UpdateProjectRequestDto,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_update",
        Some(user_id),
        async {
            let project_result = state.project_service.update_project(&context, &project_id, request).await?;
            match project_result.data {
                Some(project) => Ok(ProjectResponseDto::from(project)),
                None => Err(crate::shared::errors::AppError::not_found("项目不存在"))
            }
        }
    )
}

/// 删除项目命令
#[command]
pub async fn project_delete(
    project_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<(), String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_delete",
        Some(user_id),
        async {
            let result = state.project_service.delete_project(&context, &project_id).await?;
            Ok(())
        }
    )
}

/// 项目列表命令
#[command]
pub async fn project_list(
    pagination: Option<PaginationParams>,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<ProjectResponseDto>, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));
    let pagination = pagination.unwrap_or_default();

    execute_simple_command!(
        "project_list",
        Some(user_id),
        async {
            let projects_result = state.project_service.list_user_projects(&context, pagination.page, pagination.page_size, None).await?;
            match projects_result.data {
                Some(projects) => Ok(projects.into_iter().map(ProjectResponseDto::from).collect()),
                None => Ok(Vec::new())
            }
        }
    )
}

/// 归档项目命令
#[command]
pub async fn project_archive(
    project_id: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_archive",
        Some(user_id),
        async {
            let result = state.project_service.archive_project(&context, &project_id).await?;
            // 归档成功后，重新获取项目信息返回
            let project_result = state.project_service.get_project(&context, &project_id).await?;
            match project_result.data {
                Some(project) => Ok(ProjectResponseDto::from(project)),
                None => Err(crate::shared::errors::AppError::not_found("项目不存在"))
            }
        }
    )
}

/// 搜索项目命令
#[command]
pub async fn project_search(
    search_params: SearchParams,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<Vec<ProjectResponseDto>, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_search",
        Some(user_id),
        async {
            let projects_result = state.project_service.search_projects(&context, &search_params.keyword.unwrap_or_default(), search_params.pagination.page, search_params.pagination.page_size).await?;
            match projects_result.data {
                Some(projects) => Ok(projects.into_iter().map(ProjectResponseDto::from).collect()),
                None => Ok(Vec::new())
            }
        }
    )
}

/// 更新项目状态命令
#[command]
pub async fn project_update_status(
    project_id: String,
    status: String,
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<ProjectResponseDto, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_update_status",
        Some(user_id),
        async {
            // 首先需要将字符串状态转换为ProjectStatus枚举
            let project_status = match status.as_str() {
                "not_started" => crate::shared::types::ProjectStatus::NotStarted,
                "in_progress" => crate::shared::types::ProjectStatus::InProgress,
                "at_risk" => crate::shared::types::ProjectStatus::AtRisk,
                "paused" => crate::shared::types::ProjectStatus::Paused,
                "completed" => crate::shared::types::ProjectStatus::Completed,
                "archived" => crate::shared::types::ProjectStatus::Archived,
                _ => return Err(crate::shared::errors::AppError::validation("无效的项目状态")),
            };

            let result = state.project_service.update_project_status(&context, &project_id, project_status).await?;
            // 状态更新成功后，重新获取项目信息返回
            let project_result = state.project_service.get_project(&context, &project_id).await?;
            match project_result.data {
                Some(project) => Ok(ProjectResponseDto::from(project)),
                None => Err(crate::shared::errors::AppError::not_found("项目不存在"))
            }
        }
    )
}

/// 获取项目统计命令
#[command]
pub async fn project_get_stats(
    current_user_id: Option<String>,
    state: State<'_, ApiState>,
) -> Result<serde_json::Value, String> {
    let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    let context = CommandUtils::create_service_context(Some(user_id.clone()));

    execute_simple_command!(
        "project_get_stats",
        Some(user_id),
        async {
            let stats_result = state.project_service.get_user_project_stats(&context).await?;
            match stats_result.data {
                Some(stats) => Ok(serde_json::to_value(stats).unwrap_or_default()),
                None => Ok(serde_json::Value::Null)
            }
        }
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_project_commands_compilation() {
        // 这个测试确保所有命令都能正确编译
        // 实际的功能测试应该在集成测试中进行
        assert!(true);
    }
}
