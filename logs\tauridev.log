Compiling paolife v0.1.0 (D:\0DevelopeRepository\RustProgram\PaoLife\src-tauri)
error[E0583]: file not found for module `task_mapper`
 --> src\infrastructure\database\mappers\mod.rs:9:1
  |
9 | pub mod task_mapper;
  | ^^^^^^^^^^^^^^^^^^^^
  |
  = help: to create the module `task_mapper`, create file "src\infrastructure\database\mappers\task_mapper.rs" or "src\infrastructure\database\mappers\task_mapper\mod.rs"
  = note: if there is a `mod task_mapper` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `area_mapper`
  --> src\infrastructure\database\mappers\mod.rs:10:1
   |
10 | pub mod area_mapper;
   | ^^^^^^^^^^^^^^^^^^^^
   |
   = help: to create the module `area_mapper`, create file "src\infrastructure\database\mappers\area_mapper.rs" or "src\infrastructure\database\mappers\area_mapper\mod.rs"
   = note: if there is a `mod area_mapper` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `file_system`
 --> src\infrastructure\mod.rs:5:1
  |
5 | pub mod file_system;
  | ^^^^^^^^^^^^^^^^^^^^
  |
  = help: to create the module `file_system`, create file "src\infrastructure\file_system.rs" or "src\infrastructure\file_system\mod.rs"
  = note: if there is a `mod file_system` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `cache`
 --> src\infrastructure\mod.rs:6:1
  |
6 | pub mod cache;
  | ^^^^^^^^^^^^^^
  |
  = help: to create the module `cache`, create file "src\infrastructure\cache.rs" or "src\infrastructure\cache\mod.rs"
  = note: if there is a `mod cache` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `search`
 --> src\infrastructure\mod.rs:7:1
  |
7 | pub mod search;
  | ^^^^^^^^^^^^^^^
  |
  = help: to create the module `search`, create file "src\infrastructure\search.rs" or "src\infrastructure\search\mod.rs"
  = note: if there is a `mod search` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `use_cases`
 --> src\application\mod.rs:4:1
  |
4 | pub mod use_cases;
  | ^^^^^^^^^^^^^^^^^^
  |
  = help: to create the module `use_cases`, create file "src\application\use_cases.rs" or "src\application\use_cases\mod.rs"
  = note: if there is a `mod use_cases` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `dto`
 --> src\application\mod.rs:5:1
  |
5 | pub mod dto;
  | ^^^^^^^^^^^^
  |
  = help: to create the module `dto`, create file "src\application\dto.rs" or "src\application\dto\mod.rs"
  = note: if there is a `mod dto` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `commands`
 --> src\application\mod.rs:7:1
  |
7 | pub mod commands;
  | ^^^^^^^^^^^^^^^^^
  |
  = help: to create the module `commands`, create file "src\application\commands.rs" or "src\application\commands\mod.rs"
  = note: if there is a `mod commands` elsewhere in the crate already, import it with `use crate::...` instead

error[E0583]: file not found for module `queries`
 --> src\application\mod.rs:8:1
  |
8 | pub mod queries;
  | ^^^^^^^^^^^^^^^^
  |
  = help: to create the module `queries`, create file "src\application\queries.rs" or "src\application\queries\mod.rs"
  = note: if there is a `mod queries` elsewhere in the crate already, import it with `use crate::...` instead

error[E0428]: the name `execute_command` is defined multiple times
   --> src\api\common.rs:68:1
    |
68  | macro_rules! execute_command {
    | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ `execute_command` redefined here
    |
   ::: src\api\commands\mod.rs:152:1
    |
152 | macro_rules! execute_command {
    | ---------------------------- previous definition of the macro `execute_command` here
    |
    = note: `execute_command` must be defined only once in the macro namespace of this module

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\user_repository_impl.rs:47:9
   |
47 | /         sqlx::query!(
48 | |             r#"
49 | |             INSERT INTO users (
50 | |                 id, username, email, password_hash, full_name, avatar_url,
...  |
65 | |             model.last_login_at
66 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\user_repository_impl.rs:75:21
   |
75 |           let model = sqlx::query_as!(
   |  _____________________^
76 | |             UserModel,
77 | |             "SELECT * FROM users WHERE id = ?",
78 | |             id
79 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\user_repository_impl.rs:91:21
   |
91 |           let model = sqlx::query_as!(
   |  _____________________^
92 | |             UserModel,
93 | |             "SELECT * FROM users WHERE username = ?",
94 | |             username
95 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:107:21
    |
107 |           let model = sqlx::query_as!(
    |  _____________________^
108 | |             UserModel,
109 | |             "SELECT * FROM users WHERE email = ?",
110 | |             email
111 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:141:9
    |
141 | /         sqlx::query!(
142 | |             r#"
143 | |             UPDATE users SET
144 | |                 email = ?, full_name = ?, avatar_url = ?, timezone = ?,
...   |
154 | |             model.id
155 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:164:22
    |
164 |           let result = sqlx::query!(
    |  ______________________^
165 | |             "DELETE FROM users WHERE id = ?",
166 | |             id
167 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:276:21
    |
276 |           let count = sqlx::query_scalar!(
    |  _____________________^
277 | |             "SELECT COUNT(*) FROM users WHERE id = ?",
278 | |             id
279 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:288:21
    |
288 |           let count = sqlx::query_scalar!(
    |  _____________________^
289 | |             "SELECT COUNT(*) FROM users WHERE username = ?",
290 | |             username
291 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:300:21
    |
300 |           let count = sqlx::query_scalar!(
    |  _____________________^
301 | |             "SELECT COUNT(*) FROM users WHERE email = ?",
302 | |             email
303 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:314:9
    |
314 | /         sqlx::query!(
315 | |             "UPDATE users SET last_login_at = ?, updated_at = ? WHERE id = ?",
316 | |             now,
317 | |             now,
318 | |             id
319 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:330:9
    |
330 | /         sqlx::query!(
331 | |             "UPDATE users SET is_active = ?, updated_at = ? WHERE id = ?",
332 | |             true,
333 | |             now,
334 | |             id
335 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:346:9
    |
346 | /         sqlx::query!(
347 | |             "UPDATE users SET is_active = ?, updated_at = ? WHERE id = ?",
348 | |             false,
349 | |             now,
350 | |             id
351 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:406:19
    |
406 |           let row = sqlx::query!(
    |  ___________________^
407 | |             "SELECT COUNT(*) as count FROM users WHERE username = ?",
408 | |             username
409 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:418:19
    |
418 |           let row = sqlx::query!(
    |  ___________________^
419 | |             "SELECT COUNT(*) as count FROM users WHERE email = ?",
420 | |             email
421 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:430:22
    |
430 |           let result = sqlx::query!(
    |  ______________________^
431 | |             "UPDATE users SET is_active = true, updated_at = ? WHERE id = ?",
432 | |             Utc::now(),
433 | |             id
434 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:447:22
    |
447 |           let result = sqlx::query!(
    |  ______________________^
448 | |             "UPDATE users SET is_active = false, updated_at = ? WHERE id = ?",
449 | |             Utc::now(),
450 | |             id
451 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:465:22
    |
465 |           let result = sqlx::query!(
    |  ______________________^
466 | |             "UPDATE users SET last_login_at = ?, updated_at = ? WHERE id = ?",
467 | |             now,
468 | |             now,
469 | |             id
470 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\user_repository_impl.rs:483:22
    |
483 |           let result = sqlx::query!(
    |  ______________________^
484 | |             "UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?",
485 | |             password_hash,
486 | |             Utc::now(),
487 | |             id
488 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\project_repository_impl.rs:35:9
   |
35 | /         sqlx::query!(
36 | |             r#"
37 | |             INSERT INTO projects (
38 | |                 id, name, description, status, progress, priority,
...  |
57 | |             model.archived_at
58 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\project_repository_impl.rs:67:21
   |
67 |           let model = sqlx::query_as!(
   |  _____________________^
68 | |             ProjectModel,
69 | |             "SELECT * FROM projects WHERE id = ?",
70 | |             id
71 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:92:9
    |
92  | /         sqlx::query!(
93  | |             r#"
94  | |             UPDATE projects SET
95  | |                 name = ?, description = ?, status = ?, progress = ?, priority = ?,
...   |
109 | |             model.id
110 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:119:22
    |
119 |           let result = sqlx::query!(
    |  ______________________^
120 | |             "DELETE FROM projects WHERE id = ?",
121 | |             id
122 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:365:22
    |
365 |           let models = sqlx::query_as!(
    |  ______________________^
366 | |             ProjectModel,
367 | |             r#"
368 | |             SELECT * FROM projects
...   |
374 | |             params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
375 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:384:22
    |
384 |           let models = sqlx::query_as!(
    |  ______________________^
385 | |             ProjectModel,
386 | |             r#"
387 | |             SELECT * FROM projects
...   |
393 | |             params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
394 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:403:22
    |
403 |           let result = sqlx::query!(
    |  ______________________^
404 | |             "UPDATE projects SET status = ?, updated_at = ? WHERE id = ?",
405 | |             status.to_string(),
406 | |             Utc::now(),
407 | |             id
408 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:421:22
    |
421 |           let result = sqlx::query!(
    |  ______________________^
422 | |             "UPDATE projects SET progress = ?, updated_at = ? WHERE id = ?",
423 | |             progress as i32,
424 | |             Utc::now(),
425 | |             id
426 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:439:22
    |
439 |           let result = sqlx::query!(
    |  ______________________^
440 | |             "UPDATE projects SET actual_hours = actual_hours + ?, updated_at = ? WHERE id = ?",
441 | |             hours as i32,
442 | |             Utc::now(),
443 | |             id
444 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:458:22
    |
458 |           let result = sqlx::query!(
    |  ______________________^
459 | |             "UPDATE projects SET status = 'archived', archived_at = ?, updated_at = ? WHERE id = ?",
460 | |             now,
461 | |             now,
462 | |             id
463 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:476:22
    |
476 |           let result = sqlx::query!(
    |  ______________________^
477 | |             "UPDATE projects SET status = 'in_progress', archived_at = NULL, updated_at = ? WHERE id = ?",
478 | |             Utc::now(),
479 | |             id
480 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:500:19
    |
500 |           let row = sqlx::query!(
    |  ___________________^
501 | |             r#"
502 | |             SELECT
503 | |                 COUNT(*) as total_projects,
...   |
511 | |             user_id
512 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\project_repository_impl.rs:532:19
    |
532 |           let row = sqlx::query!(
    |  ___________________^
533 | |             r#"
534 | |             SELECT
535 | |                 COUNT(*) as total_projects,
...   |
543 | |             area_id
544 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\task_repository_impl.rs:45:9
   |
45 | /         sqlx::query!(
46 | |             r#"
47 | |             INSERT INTO tasks (
48 | |                 id, title, description, status, priority, parent_task_id,
...  |
70 | |             model.completed_at
71 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\task_repository_impl.rs:80:21
   |
80 |           let model = sqlx::query_as!(
   |  _____________________^
81 | |             TaskModel,
82 | |             "SELECT * FROM tasks WHERE id = ?",
83 | |             id
84 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:115:9
    |
115 | /         sqlx::query!(
116 | |             r#"
117 | |             UPDATE tasks SET
118 | |                 title = ?, description = ?, status = ?, priority = ?, parent_task_id = ?,
...   |
137 | |             model.id
138 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:153:22
    |
153 |           let result = sqlx::query!(
    |  ______________________^
154 | |             "DELETE FROM tasks WHERE id = ?",
155 | |             id
156 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:421:22
    |
421 |           let models = sqlx::query_as!(
    |  ______________________^
422 | |             TaskModel,
423 | |             r#"
424 | |             SELECT * FROM tasks
...   |
430 | |             params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
431 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:486:22
    |
486 |           let models = sqlx::query_as!(
    |  ______________________^
487 | |             TaskModel,
488 | |             r#"
489 | |             SELECT * FROM tasks
...   |
495 | |             params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
496 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:505:22
    |
505 |           let models = sqlx::query_as!(
    |  ______________________^
506 | |             TaskModel,
507 | |             r#"
508 | |             SELECT * FROM tasks
...   |
514 | |             params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
515 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:609:22
    |
609 |           let result = sqlx::query!(
    |  ______________________^
610 | |             "UPDATE tasks SET status = ?, completed_at = ?, updated_at = ? WHERE id = ?",
611 | |             status.to_string(),
612 | |             completed_at,
613 | |             Utc::now(),
614 | |             id
615 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:628:22
    |
628 |           let result = sqlx::query!(
    |  ______________________^
629 | |             "UPDATE tasks SET completion_percentage = ?, updated_at = ? WHERE id = ?",
630 | |             percentage as i32,
631 | |             Utc::now(),
632 | |             id
633 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:646:22
    |
646 |           let result = sqlx::query!(
    |  ______________________^
647 | |             "UPDATE tasks SET sort_order = ?, updated_at = ? WHERE id = ?",
648 | |             sort_order,
649 | |             Utc::now(),
650 | |             id
651 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:672:13
    |
672 | /             sqlx::query!(
673 | |                 "UPDATE tasks SET sort_order = ?, updated_at = ? WHERE id = ?",
674 | |                 sort_order,
675 | |                 Utc::now(),
676 | |                 id
677 | |             )
    | |_____________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:690:22
    |
690 |           let result = sqlx::query!(
    |  ______________________^
691 | |             "UPDATE tasks SET actual_minutes = actual_minutes + ?, updated_at = ? WHERE id = ?",
692 | |             minutes as i32,
693 | |             Utc::now(),
694 | |             id
695 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:718:22
    |
718 |           let result = sqlx::query!(
    |  ______________________^
719 | |             "UPDATE tasks SET parent_task_id = ?, updated_at = ? WHERE id = ?",
720 | |             new_parent_id,
721 | |             Utc::now(),
722 | |             id
723 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:764:19
    |
764 |           let row = sqlx::query!(
    |  ___________________^
765 | |             r#"
766 | |             SELECT
767 | |                 COUNT(*) as total_tasks,
...   |
775 | |             project_id
776 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:796:19
    |
796 |           let row = sqlx::query!(
    |  ___________________^
797 | |             r#"
798 | |             SELECT
799 | |                 COUNT(*) as total_tasks,
...   |
807 | |             area_id
808 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\task_repository_impl.rs:828:19
    |
828 |           let row = sqlx::query!(
    |  ___________________^
829 | |             r#"
830 | |             SELECT
831 | |                 COUNT(*) as total_assigned_tasks,
...   |
840 | |             user_id
841 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\area_repository_impl.rs:40:9
   |
40 | /         sqlx::query!(
41 | |             r#"
42 | |             INSERT INTO areas (
43 | |                 id, name, description, standard, icon_name, color_hex,
...  |
58 | |             model.archived_at
59 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\area_repository_impl.rs:68:21
   |
68 |           let model = sqlx::query_as!(
   |  _____________________^
69 | |             AreaModel,
70 | |             "SELECT * FROM areas WHERE id = ?",
71 | |             id
72 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
  --> src\infrastructure\database\repositories\area_repository_impl.rs:84:21
   |
84 |           let model = sqlx::query_as!(
   |  _____________________^
85 | |             AreaModel,
86 | |             "SELECT * FROM areas WHERE name = ?",
87 | |             name
88 | |         )
   | |_________^
   |
   = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:118:9
    |
118 | /         sqlx::query!(
119 | |             r#"
120 | |             UPDATE areas SET
121 | |                 name = ?, description = ?, standard = ?, icon_name = ?, color_hex = ?,
...   |
133 | |             model.id
134 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:144:29
    |
144 |           let project_count = sqlx::query!(
    |  _____________________________^
145 | |             "SELECT COUNT(*) as count FROM projects WHERE area_id = ?",
146 | |             id
147 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:156:26
    |
156 |           let task_count = sqlx::query!(
    |  __________________________^
157 | |             "SELECT COUNT(*) as count FROM tasks WHERE area_id = ?",
158 | |             id
159 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:168:22
    |
168 |           let result = sqlx::query!(
    |  ______________________^
169 | |             "DELETE FROM areas WHERE id = ?",
170 | |             id
171 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:312:22
    |
312 |           let models = sqlx::query_as!(
    |  ______________________^
313 | |             AreaModel,
314 | |             r#"
315 | |             SELECT * FROM areas
...   |
321 | |             params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
322 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:331:22
    |
331 |           let result = sqlx::query!(
    |  ______________________^
332 | |             "UPDATE areas SET is_active = true, updated_at = ? WHERE id = ?",
333 | |             Utc::now(),
334 | |             id
335 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:348:22
    |
348 |           let result = sqlx::query!(
    |  ______________________^
349 | |             "UPDATE areas SET is_active = false, updated_at = ? WHERE id = ?",
350 | |             Utc::now(),
351 | |             id
352 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:366:22
    |
366 |           let result = sqlx::query!(
    |  ______________________^
367 | |             "UPDATE areas SET is_active = false, archived_at = ?, updated_at = ? WHERE id = ?",
368 | |             now,
369 | |             now,
370 | |             id
371 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:384:22
    |
384 |           let result = sqlx::query!(
    |  ______________________^
385 | |             "UPDATE areas SET is_active = true, archived_at = NULL, updated_at = ? WHERE id = ?",
386 | |             Utc::now(),
387 | |             id
388 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:401:22
    |
401 |           let result = sqlx::query!(
    |  ______________________^
402 | |             "UPDATE areas SET sort_order = ?, updated_at = ? WHERE id = ?",
403 | |             sort_order,
404 | |             Utc::now(),
405 | |             id
406 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:427:13
    |
427 | /             sqlx::query!(
428 | |                 "UPDATE areas SET sort_order = ?, updated_at = ? WHERE id = ?",
429 | |                 sort_order,
430 | |                 Utc::now(),
431 | |                 id
432 | |             )
    | |_____________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:445:19
    |
445 |           let row = sqlx::query!(
    |  ___________________^
446 | |             "SELECT COUNT(*) as count FROM areas WHERE name = ?",
447 | |             name
448 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:468:29
    |
468 |           let project_stats = sqlx::query!(
    |  _____________________________^
469 | |             r#"
470 | |             SELECT
471 | |                 COUNT(*) as total_projects,
...   |
477 | |             id
478 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:484:26
    |
484 |           let task_stats = sqlx::query!(
    |  __________________________^
485 | |             r#"
486 | |             SELECT
487 | |                 COUNT(*) as total_tasks,
...   |
493 | |             id
494 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:500:27
    |
500 |           let habit_stats = sqlx::query!(
    |  ___________________________^
501 | |             r#"
502 | |             SELECT
503 | |                 COUNT(*) as total_habits,
...   |
508 | |             id
509 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:548:24
    |
548 |           let existing = sqlx::query!(
    |  ________________________^
549 | |             "SELECT id FROM habit_records WHERE area_id = ? AND habit_name = ? AND record_date = ?",
550 | |             area_id,
551 | |             habit_name,
552 | |             today
553 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:564:9
    |
564 | /         sqlx::query!(
565 | |             r#"
566 | |             INSERT INTO habit_records (id, area_id, habit_name, record_date, created_at)
567 | |             VALUES (?, ?, ?, ?, ?)
...   |
573 | |             chrono::Utc::now()
574 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error: error returned from database: (code: 14) unable to open database file
   --> src\infrastructure\database\repositories\area_repository_impl.rs:586:23
    |
586 |           let records = sqlx::query!(
    |  _______________________^
587 | |             r#"
588 | |             SELECT record_date FROM habit_records
589 | |             WHERE area_id = ? AND habit_name = ?
...   |
593 | |             habit_name
594 | |         )
    | |_________^
    |
    = note: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)

error[E0432]: unresolved import `crate::api::dto::UserUpdateRequestDto`
 --> src\api\commands\auth_commands.rs:5:77
  |
5 | use crate::api::dto::{LoginRequestDto, RegisterRequestDto, UserResponseDto, UserUpdateRequestDto};
  |                                                                             ^^^^^^^^^^^^^^^^^^^^ no `UserUpdateRequestDto` in `api::dto`

error[E0432]: unresolved import `ProjectStatus`
   --> src\infrastructure\database\validators\project_validator.rs:210:13
    |
210 |         use ProjectStatus::*;
    |             ^^^^^^^^^^^^^ help: a similar path exists: `crate::ProjectStatus`
    |
    = note: `use` statements changed in Rust 2018; read more at <https://doc.rust-lang.org/edition-guide/rust-2018/module-system/path-clarity.html>

error[E0432]: unresolved import `TaskStatus`
   --> src\infrastructure\database\validators\task_validator.rs:168:13
    |
168 |         use TaskStatus::*;
    |             ^^^^^^^^^^ help: a similar path exists: `crate::TaskStatus`
    |
    = note: `use` statements changed in Rust 2018; read more at <https://doc.rust-lang.org/edition-guide/rust-2018/module-system/path-clarity.html>

error[E0106]: missing lifetime specifier
   --> src\api\mod.rs:185:92
    |
185 |     pub fn validate_required_string(value: &Option<String>, field_name: &str) -> AppResult<&String> {
    |                                            ---------------              ----               ^ expected named lifetime parameter
    |
    = help: this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `value` or `field_name`
help: consider introducing a named lifetime parameter
    |
185 |     pub fn validate_required_string<'a>(value: &'a Option<String>, field_name: &'a str) -> AppResult<&'a String> {
    |                                    ++++         ++                              ++                    ++

error[E0106]: missing lifetime specifier
  --> src\infrastructure\database\mappers\mod.rs:71:89
   |
71 |     pub fn validate_required_field<T>(field: &Option<T>, field_name: &str) -> AppResult<&T> {
   |                                              ----------              ----               ^ expected named lifetime parameter
   |
   = help: this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `field` or `field_name`
help: consider introducing a named lifetime parameter
   |
71 |     pub fn validate_required_field<'a, T>(field: &'a Option<T>, field_name: &'a str) -> AppResult<&'a T> {
   |                                    +++            ++                         ++                    ++

error[E0659]: `CommandUtils` is ambiguous
  --> src\api\commands\user_commands.rs:4:60
   |
4  | use crate::api::{ApiResponse, ApiState, PaginationRequest, CommandUtils};
   |                                                            ^^^^^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
  --> src\api\mod.rs:10:9
   |
10 | pub use commands::*;
   |         ^^^^^^^^^^^
   = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
  --> src\api\mod.rs:13:9
   |
13 | pub use common::*;
   |         ^^^^^^^^^
   = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
  --> src\api\commands\user_commands.rs:68:19
   |
68 |     let context = CommandUtils::create_service_context(None);
   |                   ^^^^^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
  --> src\api\mod.rs:10:9
   |
10 | pub use commands::*;
   |         ^^^^^^^^^^^
   = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
  --> src\api\mod.rs:13:9
   |
13 | pub use common::*;
   |         ^^^^^^^^^
   = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\mod.rs:160:17
    |
160 |                 CommandUtils::log_command_execution($command_name, $user_id.as_deref(), true, duration);
    |                 ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\mod.rs:164:17
    |
164 |                 CommandUtils::log_command_execution($command_name, $user_id.as_deref(), false, duration);
    |                 ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
  --> src\api\commands\user_commands.rs:95:19
   |
95 |     let context = CommandUtils::create_service_context(current_user_id.clone());
   |                   ^^^^^^^^^^^^ ambiguous name
   |
   = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
  --> src\api\mod.rs:10:9
   |
10 | pub use commands::*;
   |         ^^^^^^^^^^^
   = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
  --> src\api\mod.rs:13:9
   |
13 | pub use common::*;
   |         ^^^^^^^^^
   = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\user_commands.rs:114:19
    |
114 |     let context = CommandUtils::create_service_context(current_user_id.clone());
    |                   ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\user_commands.rs:139:19
    |
139 |     let context = CommandUtils::create_service_context(current_user_id.clone());
    |                   ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\user_commands.rs:160:19
    |
160 |     let context = CommandUtils::create_service_context(current_user_id.clone());
    |                   ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\user_commands.rs:187:19
    |
187 |     let context = CommandUtils::create_service_context(current_user_id.clone());
    |                   ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\user_commands.rs:205:19
    |
205 |     let context = CommandUtils::create_service_context(current_user_id.clone());
    |                   ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

error[E0659]: `CommandUtils` is ambiguous
   --> src\api\commands\user_commands.rs:223:19
    |
223 |     let context = CommandUtils::create_service_context(current_user_id.clone());
    |                   ^^^^^^^^^^^^ ambiguous name
    |
    = note: ambiguous because of multiple glob imports of a name in the same module
note: `CommandUtils` could refer to the struct imported here
   --> src\api\mod.rs:10:9
    |
10  | pub use commands::*;
    |         ^^^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate
note: `CommandUtils` could also refer to the struct imported here
   --> src\api\mod.rs:13:9
    |
13  | pub use common::*;
    |         ^^^^^^^^^
    = help: consider adding an explicit import of `CommandUtils` to disambiguate

warning: unused import: `ApiResponse`
 --> src\api\commands\user_commands.rs:4:18
  |
4 | use crate::api::{ApiResponse, ApiState, PaginationRequest, CommandUtils};
  |                  ^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `Deserialize` and `Serialize`
 --> src\api\commands\project_commands.rs:9:13
  |
9 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\api\commands\task_commands.rs:9:13
  |
9 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused imports: `Deserialize` and `Serialize`
 --> src\api\commands\area_commands.rs:9:13
  |
9 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^  ^^^^^^^^^

warning: unused import: `Deserialize`
 --> src\api\commands\auth_commands.rs:9:13
  |
9 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^

warning: unused import: `ApiState`
  --> src\api\commands\mod.rs:17:31
   |
17 | use crate::api::{ApiResponse, ApiState};
   |                               ^^^^^^^^

warning: unused import: `tauri::State`
  --> src\api\commands\mod.rs:20:5
   |
20 | use tauri::State;
   |     ^^^^^^^^^^^^

warning: unused import: `Deserialize`
 --> src\api\handlers\middleware.rs:6:13
  |
6 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^

warning: unused import: `Serialize`
 --> src\api\dto\request.rs:5:26
  |
5 | use serde::{Deserialize, Serialize};
  |                          ^^^^^^^^^

warning: unused import: `Deserialize`
 --> src\api\dto\response.rs:6:13
  |
6 | use serde::{Deserialize, Serialize};
  |             ^^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\api\dto\validation.rs:5:5
  |
5 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: ambiguous glob re-exports
  --> src\api\mod.rs:10:9
   |
10 | pub use commands::*;
   |         ^^^^^^^^^^^ the name `CommandUtils` in the type namespace is first re-exported here
...
13 | pub use common::*;
   |         --------- but the name `CommandUtils` in the type namespace is also re-exported here
   |
   = note: `#[warn(ambiguous_glob_reexports)]` on by default

warning: ambiguous glob re-exports
  --> src\api\mod.rs:10:9
   |
10 | pub use commands::*;
   |         ^^^^^^^^^^^ the name `CommandUtils` in the value namespace is first re-exported here
...
13 | pub use common::*;
   |         --------- but the name `CommandUtils` in the value namespace is also re-exported here

warning: ambiguous glob re-exports
  --> src\api\mod.rs:12:9
   |
12 | pub use dto::*;
   |         ^^^^^^ the name `SortOrder` in the type namespace is first re-exported here
13 | pub use common::*;
   |         --------- but the name `SortOrder` in the type namespace is also re-exported here

warning: unused import: `tauri::State`
  --> src\api\mod.rs:18:5
   |
18 | use tauri::State;
   |     ^^^^^^^^^^^^

warning: unused import: `DateTime`
 --> src\domain\entities\project.rs:6:14
  |
6 | use chrono::{DateTime, Utc, NaiveDate};
  |              ^^^^^^^^

warning: unused import: `DateTime`
 --> src\domain\entities\area.rs:6:14
  |
6 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `DateTime`
 --> src\domain\entities\task.rs:6:14
  |
6 | use chrono::{DateTime, Utc, NaiveDate};
  |              ^^^^^^^^

warning: unused import: `DateTime`
 --> src\domain\entities\user.rs:6:14
  |
6 | use chrono::{DateTime, Utc};
  |              ^^^^^^^^

warning: unused import: `crate::shared::errors::AppResult`
 --> src\domain\repositories\mod.rs:4:5
  |
4 | use crate::shared::errors::AppResult;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `async_trait::async_trait`
 --> src\domain\repositories\mod.rs:5:5
  |
5 | use async_trait::async_trait;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `chrono::NaiveDate`
 --> src\domain\repositories\project_repository.rs:8:5
  |
8 | use chrono::NaiveDate;
  |     ^^^^^^^^^^^^^^^^^

warning: unused import: `chrono::NaiveDate`
 --> src\domain\repositories\task_repository.rs:8:5
  |
8 | use chrono::NaiveDate;
  |     ^^^^^^^^^^^^^^^^^

warning: unused import: `Pagination`
 --> src\domain\repositories\user_repository.rs:5:51
  |
5 | use crate::shared::types::{EntityId, QueryParams, Pagination};
  |                                                   ^^^^^^^^^^

warning: unused import: `SqlitePool`
 --> src\infrastructure\database\mod.rs:5:12
  |
5 | use sqlx::{SqlitePool, Row};
  |            ^^^^^^^^^^

warning: unused import: `std::collections::HashMap`
 --> src\infrastructure\database\validators\mod.rs:6:5
  |
6 | use std::collections::HashMap;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `ValidationContext`
 --> src\infrastructure\database\validators\user_validator.rs:5:79
  |
5 | use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
  |                                                                               ^^^^^^^^^^^^^^^^^

warning: unused import: `ValidationContext`
 --> src\infrastructure\database\validators\project_validator.rs:6:79
  |
6 | use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
  |                                                                               ^^^^^^^^^^^^^^^^^

warning: unused import: `ValidationContext`
 --> src\infrastructure\database\validators\task_validator.rs:6:79
  |
6 | use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
  |                                                                               ^^^^^^^^^^^^^^^^^

warning: unused import: `ValidationContext`
 --> src\infrastructure\database\validators\area_validator.rs:5:79
  |
5 | use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
  |                                                                               ^^^^^^^^^^^^^^^^^

warning: unused import: `NaiveDate`
  --> src\infrastructure\database\repositories\project_repository_impl.rs:13:19
   |
13 | use chrono::{Utc, NaiveDate};
   |                   ^^^^^^^^^

warning: unused import: `DependencyType`
 --> src\infrastructure\database\repositories\task_repository_impl.rs:7:21
  |
7 |     TaskDependency, DependencyType
  |                     ^^^^^^^^^^^^^^

warning: unused import: `Mapper`
  --> src\infrastructure\database\repositories\task_repository_impl.rs:10:75
   |
10 | use crate::infrastructure::database::mappers::{task_mapper as TaskMapper, Mapper};
   |                                                                           ^^^^^^

warning: unused import: `NaiveDate`
  --> src\infrastructure\database\repositories\task_repository_impl.rs:16:19
   |
16 | use chrono::{Utc, NaiveDate};
   |                   ^^^^^^^^^

warning: unused import: `Mapper`
 --> src\infrastructure\database\repositories\area_repository_impl.rs:7:75
  |
7 | use crate::infrastructure::database::mappers::{area_mapper as AreaMapper, Mapper};
  |                                                                           ^^^^^^

warning: unused import: `task_mapper::*`
  --> src\infrastructure\database\mappers\mod.rs:15:9
   |
15 | pub use task_mapper::*;
   |         ^^^^^^^^^^^^^^

warning: unused import: `area_mapper::*`
  --> src\infrastructure\database\mappers\mod.rs:16:9
   |
16 | pub use area_mapper::*;
   |         ^^^^^^^^^^^^^^

warning: unused imports: `Pool` and `Sqlite`
 --> src\infrastructure\config\database.rs:8:5
  |
8 |     Pool, Sqlite, SqlitePool,
  |     ^^^^  ^^^^^^

warning: unused import: `std::path::PathBuf`
 --> src\infrastructure\config\logging.rs:6:5
  |
6 | use std::path::PathBuf;
  |     ^^^^^^^^^^^^^^^^^^

warning: unused import: `Level`
 --> src\infrastructure\config\logging.rs:7:15
  |
7 | use tracing::{Level, Subscriber};
  |               ^^^^^

warning: unused import: `file_system::*`
  --> src\infrastructure\mod.rs:12:9
   |
12 | pub use file_system::*;
   |         ^^^^^^^^^^^^^^

warning: unused import: `cache::*`
  --> src\infrastructure\mod.rs:13:9
   |
13 | pub use cache::*;
   |         ^^^^^^^^

warning: unused import: `search::*`
  --> src\infrastructure\mod.rs:14:9
   |
14 | pub use search::*;
   |         ^^^^^^^^^

warning: unused import: `AreaProjectStats`
 --> src\application\services\project_service.rs:6:92
  |
6 | use crate::domain::repositories::project_repository::{ProjectRepository, UserProjectStats, AreaProjectStats};
  |                                                                                            ^^^^^^^^^^^^^^^^

warning: unused import: `Priority`
 --> src\application\services\project_service.rs:7:78
  |
7 | use crate::shared::types::{EntityId, QueryParams, Pagination, ProjectStatus, Priority};
  |                                                                              ^^^^^^^^

warning: unused import: `ProjectTaskStats`
 --> src\application\services\task_service.rs:6:83
  |
6 | use crate::domain::repositories::task_repository::{TaskRepository, TaskHierarchy, ProjectTaskStats, UserTaskStats};
  |                                                                                   ^^^^^^^^^^^^^^^^

warning: unused import: `Priority`
 --> src\application\services\task_service.rs:7:75
  |
7 | use crate::shared::types::{EntityId, QueryParams, Pagination, TaskStatus, Priority};
  |                                                                           ^^^^^^^^

warning: unused import: `crate::shared::types::EntityId`
 --> src\application\services\auth_service.rs:8:5
  |
8 | use crate::shared::types::EntityId;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `async_trait::async_trait`
 --> src\application\services\auth_service.rs:9:5
  |
9 | use async_trait::async_trait;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused imports: `EntityId`, `ProjectStatus`, and `TaskStatus`
  --> src\application\services\analytics_service.rs:11:28
   |
11 | use crate::shared::types::{EntityId, ProjectStatus, TaskStatus};
   |                            ^^^^^^^^  ^^^^^^^^^^^^^  ^^^^^^^^^^

warning: unused import: `async_trait::async_trait`
  --> src\application\services\analytics_service.rs:12:5
   |
12 | use async_trait::async_trait;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
  --> src\application\services\mod.rs:22:5
   |
22 | use std::sync::Arc;
   |     ^^^^^^^^^^^^^^

warning: unused import: `use_cases::*`
  --> src\application\mod.rs:11:9
   |
11 | pub use use_cases::*;
   |         ^^^^^^^^^^^^

warning: unused import: `dto::*`
  --> src\application\mod.rs:12:9
   |
12 | pub use dto::*;
   |         ^^^^^^

warning: unused import: `commands::*`
  --> src\application\mod.rs:14:9
   |
14 | pub use commands::*;
   |         ^^^^^^^^^^^

warning: unused import: `queries::*`
  --> src\application\mod.rs:15:9
   |
15 | pub use queries::*;
   |         ^^^^^^^^^^

warning: unused import: `std::fmt`
 --> src\shared\errors.rs:5:5
  |
5 | use std::fmt;
  |     ^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> src\shared\events.rs:7:5
  |
7 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `commands::*`
  --> src\lib.rs:12:9
   |
12 | pub use commands::*;
   |         ^^^^^^^^^^^

error[E0053]: method `get_area_summary` has an incompatible type for trait
  --> src\infrastructure\database\repositories\area_repository_impl.rs:27:1
   |
27 | #[async_trait]
   | ^^^^^^^^^^^^^^ expected `area_repository::AreaSummary`, found `area_repository_impl::AreaSummary`
   |
note: type in trait
  --> src\domain\repositories\area_repository.rs:10:1
   |
10 | #[async_trait]
   | ^^^^^^^^^^^^^^
   = note: expected signature `fn(&'life0 area_repository_impl::AreaRepositoryImpl, &'life1 std::string::String) -> Pin<Box<(dyn std::future::Future<Output = Result<area_repository::AreaSummary, AppError>> + Send + 'async_trait)>>`
              found signature `fn(&'life0 area_repository_impl::AreaRepositoryImpl, &'life1 std::string::String) -> Pin<Box<(dyn std::future::Future<Output = Result<area_repository_impl::AreaSummary, AppError>> + Send + 'async_trait)>>`
   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)
help: change the output type to match the trait
   |
27 - #[async_trait]
27 + Pin<Box<(dyn std::future::Future<Output = Result<area_repository::AreaSummary, AppError>> + Send + 'async_trait)>>
   |

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:85:8
    |
85  |     Ok(response)
    |     -- ^^^^^^^^ expected `UserResponse`, found `Result<User, String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected struct `UserResponse`
                 found enum `Result<user::User, std::string::String>`
help: the type constructed contains `Result<user::User, std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:85:5
    |
85  |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:103:8
    |
103 |     Ok(response)
    |     -- ^^^^^^^^ expected `Option<UserResponse>`, found `Result<Option<User>, String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected enum `std::option::Option<UserResponse>`
               found enum `Result<std::option::Option<user::User>, std::string::String>`
help: the type constructed contains `Result<std::option::Option<user::User>, std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:103:5
    |
103 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^

error[E0063]: missing field `avatar_url` in initializer of `UpdateUserData`
   --> src\api\commands\user_commands.rs:116:23
    |
116 |     let update_data = UpdateUserData {
    |                       ^^^^^^^^^^^^^^ missing `avatar_url`

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:129:8
    |
129 |     Ok(response)
    |     -- ^^^^^^^^ expected `UserResponse`, found `Result<User, String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected struct `UserResponse`
                 found enum `Result<user::User, std::string::String>`
help: the type constructed contains `Result<user::User, std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:129:5
    |
129 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:147:8
    |
147 |     Ok(response)
    |     -- ^^^^^^^^ expected `()`, found `Result<(), String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected unit type `()`
                    found enum `Result<(), std::string::String>`
help: the type constructed contains `Result<(), std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:147:5
    |
147 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^
help: use the `?` operator to extract the `Result<(), std::string::String>` value, propagating a `Result::Err` value to the caller
    |
147 |     Ok(response?)
    |                +

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:176:8
    |
176 |     Ok(response)
    |     -- ^^^^^^^^ expected `PagedResult<UserResponse>`, found `Result<PagedResult<User>, String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected struct `PagedResult<UserResponse>`
                 found enum `Result<PagedResult<user::User>, std::string::String>`
help: the type constructed contains `Result<PagedResult<user::User>, std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:176:5
    |
176 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:195:8
    |
195 |     Ok(response)
    |     -- ^^^^^^^^ expected `()`, found `Result<(), String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected unit type `()`
                    found enum `Result<(), std::string::String>`
help: the type constructed contains `Result<(), std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:195:5
    |
195 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^
help: use the `?` operator to extract the `Result<(), std::string::String>` value, propagating a `Result::Err` value to the caller
    |
195 |     Ok(response?)
    |                +

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:213:8
    |
213 |     Ok(response)
    |     -- ^^^^^^^^ expected `()`, found `Result<(), String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected unit type `()`
                    found enum `Result<(), std::string::String>`
help: the type constructed contains `Result<(), std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:213:5
    |
213 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^
help: use the `?` operator to extract the `Result<(), std::string::String>` value, propagating a `Result::Err` value to the caller
    |
213 |     Ok(response?)
    |                +

error[E0308]: mismatched types
   --> src\api\commands\user_commands.rs:231:8
    |
231 |     Ok(response)
    |     -- ^^^^^^^^ expected `()`, found `Result<(), String>`
    |     |
    |     arguments to this enum variant are incorrect
    |
    = note: expected unit type `()`
                    found enum `Result<(), std::string::String>`
help: the type constructed contains `Result<(), std::string::String>` due to the type of the argument passed
   --> src\api\commands\user_commands.rs:231:5
    |
231 |     Ok(response)
    |     ^^^--------^
    |        |
    |        this argument influences the type of `Ok`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\result.rs:552:5
    |
552 |     Ok(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^
help: use the `?` operator to extract the `Result<(), std::string::String>` value, propagating a `Result::Err` value to the caller
    |
231 |     Ok(response?)
    |                +

error[E0308]: mismatched types
  --> src\api\commands\project_commands.rs:28:27
   |
28 |                 priority: request.priority,
   |                           ^^^^^^^^^^^^^^^^ expected `Option<Priority>`, found `Option<String>`
   |
   = note: expected enum `std::option::Option<Priority>`
              found enum `std::option::Option<std::string::String>`

error[E0308]: mismatched types
  --> src\api\commands\project_commands.rs:38:17
   |
37 |             match project_result.data {
   |                   ------------------- this expression has type `Project`
38 |                 Some(project) => Ok(ProjectResponseDto::from(project)),
   |                 ^^^^^^^^^^^^^ expected `Project`, found `Option<_>`
   |
   = note: expected struct `Project`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
37 |             match project_result.data.description {
   |                                      ++++++++++++

error[E0308]: mismatched types
  --> src\api\commands\project_commands.rs:39:17
   |
37 |             match project_result.data {
   |                   ------------------- this expression has type `Project`
38 |                 Some(project) => Ok(ProjectResponseDto::from(project)),
39 |                 None => Err(crate::shared::errors::AppError::internal_error("项目创建失败"))
   |                 ^^^^ expected `Project`, found `Option<_>`
   |
   = note: expected struct `Project`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
37 |             match project_result.data.description {
   |                                      ++++++++++++

error[E0599]: no variant or associated item named `internal_error` found for enum `AppError` in the current scope
  --> src\api\commands\project_commands.rs:39:62
   |
39 |                 None => Err(crate::shared::errors::AppError::internal_error("项目创建失败"))
   |                                                              ^^^^^^^^^^^^^^ variant or associated item not found in `AppError`
   |
  ::: src\shared\errors.rs:10:1
   |
10 | pub enum AppError {
   | ----------------- variant or associated item `internal_error` not found for this enum
   |
note: if you're trying to build a new `AppError` consider using one of the following associated functions:
      AppError::database
      AppError::validation
      AppError::business_logic
      AppError::not_found
      and 4 others
  --> src\shared\errors.rs:50:5
   |
50 |     pub fn database<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57 |     pub fn validation<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
64 |     pub fn business_logic<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
71 |     pub fn not_found<S: Into<String>>(resource: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: there is an associated function `internal` with a similar name
   |
39 -                 None => Err(crate::shared::errors::AppError::internal_error("项目创建失败"))
39 +                 None => Err(crate::shared::errors::AppError::internal("项目创建失败"))
   |

error[E0382]: use of moved value: `user_id`
  --> src\api\commands\project_commands.rs:57:14
   |
52 |     let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
   |         ------- move occurs because `user_id` has type `std::string::String`, which does not implement the `Copy` trait
...
57 |         Some(user_id),
   |              ^^^^^^^ value used here after move
   |
help: consider cloning the value if the performance cost is acceptable
   |
57 |         Some(user_id.clone()),
   |                     ++++++++

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:83:94
    |
83  |             let project_result = state.project_service.update_project(&context, &project_id, request).await?;
    |                                                        --------------                        ^^^^^^^ expected `UpdateProjectData`, found `UpdateProjectRequestDto`
    |                                                        |
    |                                                        arguments to this method are incorrect
    |
note: method defined here
   --> src\application\services\project_service.rs:121:18
    |
121 |     pub async fn update_project(
    |                  ^^^^^^^^^^^^^^
...
125 |         data: UpdateProjectData,
    |         -----------------------

error[E0308]: mismatched types
  --> src\api\commands\project_commands.rs:85:17
   |
84 |             match project_result.data {
   |                   ------------------- this expression has type `Project`
85 |                 Some(project) => Ok(ProjectResponseDto::from(project)),
   |                 ^^^^^^^^^^^^^ expected `Project`, found `Option<_>`
   |
   = note: expected struct `Project`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
84 |             match project_result.data.description {
   |                                      ++++++++++++

error[E0308]: mismatched types
  --> src\api\commands\project_commands.rs:86:17
   |
84 |             match project_result.data {
   |                   ------------------- this expression has type `Project`
85 |                 Some(project) => Ok(ProjectResponseDto::from(project)),
86 |                 None => Err(crate::shared::errors::AppError::not_found("项目不存在"))
   |                 ^^^^ expected `Project`, found `Option<_>`
   |
   = note: expected struct `Project`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
84 |             match project_result.data.description {
   |                                      ++++++++++++

error[E0282]: type annotations needed
   --> src\api\commands\project_commands.rs:107:13
    |
107 |             Ok(())
    |             ^^ cannot infer type of the type parameter `E` declared on the enum `Result`
    |
   ::: src\api\common.rs:142:21
    |
142 |                 Err(e.to_string())
    |                     - type must be known at this point
    |
help: consider specifying the generic arguments
    |
107 |             Ok::<(), E>(())
    |               +++++++++

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:129:17
    |
128 |             match projects_result.data {
    |                   -------------------- this expression has type `PagedResult<Project>`
129 |                 Some(projects) => Ok(projects.into_iter().map(ProjectResponseDto::from).collect()),
    |                 ^^^^^^^^^^^^^^ expected `PagedResult<Project>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Project>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:130:17
    |
128 |             match projects_result.data {
    |                   -------------------- this expression has type `PagedResult<Project>`
129 |                 Some(projects) => Ok(projects.into_iter().map(ProjectResponseDto::from).collect()),
130 |                 None => Ok(Vec::new())
    |                 ^^^^ expected `PagedResult<Project>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Project>`
                 found enum `std::option::Option<_>`

warning: unused variable: `result`
   --> src\api\commands\project_commands.rs:150:17
    |
150 |             let result = state.project_service.archive_project(&context, &project_id).await?;
    |                 ^^^^^^ help: if this is intentional, prefix it with an underscore: `_result`
    |
    = note: `#[warn(unused_variables)]` on by default

error[E0382]: use of moved value: `user_id`
   --> src\api\commands\project_commands.rs:148:14
    |
143 |     let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    |         ------- move occurs because `user_id` has type `std::string::String`, which does not implement the `Copy` trait
...
148 |         Some(user_id),
    |              ^^^^^^^ value used here after move
    |
help: consider cloning the value if the performance cost is acceptable
    |
148 |         Some(user_id.clone()),
    |                     ++++++++

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:177:17
    |
176 |             match projects_result.data {
    |                   -------------------- this expression has type `PagedResult<Project>`
177 |                 Some(projects) => Ok(projects.into_iter().map(ProjectResponseDto::from).collect()),
    |                 ^^^^^^^^^^^^^^ expected `PagedResult<Project>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Project>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:178:17
    |
176 |             match projects_result.data {
    |                   -------------------- this expression has type `PagedResult<Project>`
177 |                 Some(projects) => Ok(projects.into_iter().map(ProjectResponseDto::from).collect()),
178 |                 None => Ok(Vec::new())
    |                 ^^^^ expected `PagedResult<Project>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Project>`
                 found enum `std::option::Option<_>`

warning: unused variable: `result`
   --> src\api\commands\project_commands.rs:210:17
    |
210 |             let result = state.project_service.update_project_status(&context, &project_id, project_status).await?;
    |                 ^^^^^^ help: if this is intentional, prefix it with an underscore: `_result`

error[E0382]: use of moved value: `user_id`
   --> src\api\commands\project_commands.rs:197:14
    |
192 |     let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    |         ------- move occurs because `user_id` has type `std::string::String`, which does not implement the `Copy` trait
...
197 |         Some(user_id),
    |              ^^^^^^^ value used here after move
    |
help: consider cloning the value if the performance cost is acceptable
    |
197 |         Some(user_id.clone()),
    |                     ++++++++

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:236:17
    |
235 |             match stats_result.data {
    |                   ----------------- this expression has type `UserProjectStats`
236 |                 Some(stats) => Ok(serde_json::to_value(stats).unwrap_or_default()),
    |                 ^^^^^^^^^^^ expected `UserProjectStats`, found `Option<_>`
    |
    = note: expected struct `UserProjectStats`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\project_commands.rs:237:17
    |
235 |             match stats_result.data {
    |                   ----------------- this expression has type `UserProjectStats`
236 |                 Some(stats) => Ok(serde_json::to_value(stats).unwrap_or_default()),
237 |                 None => Ok(serde_json::Value::Null)
    |                 ^^^^ expected `UserProjectStats`, found `Option<_>`
    |
    = note: expected struct `UserProjectStats`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
  --> src\api\commands\task_commands.rs:28:27
   |
28 |                 priority: request.priority,
   |                           ^^^^^^^^^^^^^^^^ expected `Option<Priority>`, found `Option<String>`
   |
   = note: expected enum `std::option::Option<Priority>`
              found enum `std::option::Option<std::string::String>`

error[E0560]: struct `CreateTaskData` has no field named `created_by`
  --> src\api\commands\task_commands.rs:35:17
   |
35 |                 created_by: user_id,
   |                 ^^^^^^^^^^ `CreateTaskData` does not have this field
   |
   = note: all struct fields are already assigned

error[E0308]: mismatched types
  --> src\api\commands\task_commands.rs:40:17
   |
39 |             match task_result.data {
   |                   ---------------- this expression has type `Task`
40 |                 Some(task) => Ok(TaskResponseDto::from(task)),
   |                 ^^^^^^^^^^ expected `Task`, found `Option<_>`
   |
   = note: expected struct `Task`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
39 |             match task_result.data.description {
   |                                   ++++++++++++

error[E0308]: mismatched types
  --> src\api\commands\task_commands.rs:41:17
   |
39 |             match task_result.data {
   |                   ---------------- this expression has type `Task`
40 |                 Some(task) => Ok(TaskResponseDto::from(task)),
41 |                 None => Err(crate::shared::errors::AppError::internal_error("任务创建失败"))
   |                 ^^^^ expected `Task`, found `Option<_>`
   |
   = note: expected struct `Task`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
39 |             match task_result.data.description {
   |                                   ++++++++++++

error[E0599]: no variant or associated item named `internal_error` found for enum `AppError` in the current scope
  --> src\api\commands\task_commands.rs:41:62
   |
41 |                 None => Err(crate::shared::errors::AppError::internal_error("任务创建失败"))
   |                                                              ^^^^^^^^^^^^^^ variant or associated item not found in `AppError`
   |
  ::: src\shared\errors.rs:10:1
   |
10 | pub enum AppError {
   | ----------------- variant or associated item `internal_error` not found for this enum
   |
note: if you're trying to build a new `AppError` consider using one of the following associated functions:
      AppError::database
      AppError::validation
      AppError::business_logic
      AppError::not_found
      and 4 others
  --> src\shared\errors.rs:50:5
   |
50 |     pub fn database<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57 |     pub fn validation<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
64 |     pub fn business_logic<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
71 |     pub fn not_found<S: Into<String>>(resource: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: there is an associated function `internal` with a similar name
   |
41 -                 None => Err(crate::shared::errors::AppError::internal_error("任务创建失败"))
41 +                 None => Err(crate::shared::errors::AppError::internal("任务创建失败"))
   |

error[E0382]: use of moved value: `user_id`
  --> src\api\commands\task_commands.rs:59:14
   |
54 |     let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
   |         ------- move occurs because `user_id` has type `std::string::String`, which does not implement the `Copy` trait
...
59 |         Some(user_id),
   |              ^^^^^^^ value used here after move
   |
help: consider cloning the value if the performance cost is acceptable
   |
59 |         Some(user_id.clone()),
   |                     ++++++++

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:85:82
    |
85  |             let task_result = state.task_service.update_task(&context, &task_id, request).await?;
    |                                                  -----------                     ^^^^^^^ expected `UpdateTaskData`, found `UpdateTaskRequestDto`
    |                                                  |
    |                                                  arguments to this method are incorrect
    |
note: method defined here
   --> src\application\services\task_service.rs:117:18
    |
117 |     pub async fn update_task(
    |                  ^^^^^^^^^^^
...
121 |         data: UpdateTaskData,
    |         --------------------

error[E0308]: mismatched types
  --> src\api\commands\task_commands.rs:87:17
   |
86 |             match task_result.data {
   |                   ---------------- this expression has type `Task`
87 |                 Some(task) => Ok(TaskResponseDto::from(task)),
   |                 ^^^^^^^^^^ expected `Task`, found `Option<_>`
   |
   = note: expected struct `Task`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
86 |             match task_result.data.description {
   |                                   ++++++++++++

error[E0308]: mismatched types
  --> src\api\commands\task_commands.rs:88:17
   |
86 |             match task_result.data {
   |                   ---------------- this expression has type `Task`
87 |                 Some(task) => Ok(TaskResponseDto::from(task)),
88 |                 None => Err(crate::shared::errors::AppError::not_found("任务不存在"))
   |                 ^^^^ expected `Task`, found `Option<_>`
   |
   = note: expected struct `Task`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
86 |             match task_result.data.description {
   |                                   ++++++++++++

error[E0282]: type annotations needed
   --> src\api\commands\task_commands.rs:109:13
    |
109 |             Ok(())
    |             ^^ cannot infer type of the type parameter `E` declared on the enum `Result`
    |
   ::: src\api\common.rs:142:21
    |
142 |                 Err(e.to_string())
    |                     - type must be known at this point
    |
help: consider specifying the generic arguments
    |
109 |             Ok::<(), E>(())
    |               +++++++++

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:131:17
    |
130 |             match tasks_result.data {
    |                   ----------------- this expression has type `PagedResult<Task>`
131 |                 Some(tasks) => Ok(tasks.into_iter().map(TaskResponseDto::from).collect()),
    |                 ^^^^^^^^^^^ expected `PagedResult<Task>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Task>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:132:17
    |
130 |             match tasks_result.data {
    |                   ----------------- this expression has type `PagedResult<Task>`
131 |                 Some(tasks) => Ok(tasks.into_iter().map(TaskResponseDto::from).collect()),
132 |                 None => Ok(Vec::new())
    |                 ^^^^ expected `PagedResult<Task>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Task>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:154:17
    |
153 |             match task_result.data {
    |                   ---------------- this expression has type `Task`
154 |                 Some(task) => Ok(TaskResponseDto::from(task)),
    |                 ^^^^^^^^^^ expected `Task`, found `Option<_>`
    |
    = note: expected struct `Task`
                 found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
    |
153 |             match task_result.data.description {
    |                                   ++++++++++++

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:155:17
    |
153 |             match task_result.data {
    |                   ---------------- this expression has type `Task`
154 |                 Some(task) => Ok(TaskResponseDto::from(task)),
155 |                 None => Err(crate::shared::errors::AppError::not_found("任务不存在"))
    |                 ^^^^ expected `Task`, found `Option<_>`
    |
    = note: expected struct `Task`
                 found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
    |
153 |             match task_result.data.description {
    |                                   ++++++++++++

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:177:17
    |
176 |             match tasks_result.data {
    |                   ----------------- this expression has type `PagedResult<Task>`
177 |                 Some(tasks) => Ok(tasks.into_iter().map(TaskResponseDto::from).collect()),
    |                 ^^^^^^^^^^^ expected `PagedResult<Task>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Task>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:178:17
    |
176 |             match tasks_result.data {
    |                   ----------------- this expression has type `PagedResult<Task>`
177 |                 Some(tasks) => Ok(tasks.into_iter().map(TaskResponseDto::from).collect()),
178 |                 None => Ok(Vec::new())
    |                 ^^^^ expected `PagedResult<Task>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Task>`
                 found enum `std::option::Option<_>`

warning: unused variable: `result`
   --> src\api\commands\task_commands.rs:209:17
    |
209 |             let result = state.task_service.update_task_status(&context, &task_id, task_status).await?;
    |                 ^^^^^^ help: if this is intentional, prefix it with an underscore: `_result`

error[E0382]: use of moved value: `user_id`
   --> src\api\commands\task_commands.rs:197:14
    |
192 |     let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
    |         ------- move occurs because `user_id` has type `std::string::String`, which does not implement the `Copy` trait
...
197 |         Some(user_id),
    |              ^^^^^^^ value used here after move
    |
help: consider cloning the value if the performance cost is acceptable
    |
197 |         Some(user_id.clone()),
    |                     ++++++++

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:235:17
    |
234 |             match stats_result.data {
    |                   ----------------- this expression has type `UserTaskStats`
235 |                 Some(stats) => Ok(serde_json::to_value(stats).unwrap_or_default()),
    |                 ^^^^^^^^^^^ expected `UserTaskStats`, found `Option<_>`
    |
    = note: expected struct `UserTaskStats`
                 found enum `std::option::Option<_>`
help: you might have meant to use field `average_completion_time_days` whose type is `std::option::Option<f64>`
    |
234 |             match stats_result.data.average_completion_time_days {
    |                                    +++++++++++++++++++++++++++++

error[E0308]: mismatched types
   --> src\api\commands\task_commands.rs:236:17
    |
234 |             match stats_result.data {
    |                   ----------------- this expression has type `UserTaskStats`
235 |                 Some(stats) => Ok(serde_json::to_value(stats).unwrap_or_default()),
236 |                 None => Ok(serde_json::Value::Null)
    |                 ^^^^ expected `UserTaskStats`, found `Option<_>`
    |
    = note: expected struct `UserTaskStats`
                 found enum `std::option::Option<_>`
help: you might have meant to use field `average_completion_time_days` whose type is `std::option::Option<f64>`
    |
234 |             match stats_result.data.average_completion_time_days {
    |                                    +++++++++++++++++++++++++++++

error[E0609]: no field `standard` on type `api::dto::request::CreateAreaRequestDto`
  --> src\api\commands\area_commands.rs:28:35
   |
28 |                 standard: request.standard,
   |                                   ^^^^^^^^ unknown field
   |
   = note: available fields are: `name`, `description`, `color`, `icon`

error[E0609]: no field `icon_name` on type `api::dto::request::CreateAreaRequestDto`
  --> src\api\commands\area_commands.rs:29:36
   |
29 |                 icon_name: request.icon_name,
   |                                    ^^^^^^^^^ unknown field
   |
   = note: available fields are: `name`, `description`, `color`, `icon`

error[E0609]: no field `color_hex` on type `api::dto::request::CreateAreaRequestDto`
  --> src\api\commands\area_commands.rs:30:36
   |
30 |                 color_hex: request.color_hex,
   |                                    ^^^^^^^^^ unknown field
   |
   = note: available fields are: `name`, `description`, `color`, `icon`

error[E0560]: struct `CreateAreaData` has no field named `sort_order`
  --> src\api\commands\area_commands.rs:31:17
   |
31 |                 sort_order: request.sort_order,
   |                 ^^^^^^^^^^ `CreateAreaData` does not have this field
   |
   = note: all struct fields are already assigned

error[E0609]: no field `sort_order` on type `api::dto::request::CreateAreaRequestDto`
  --> src\api\commands\area_commands.rs:31:37
   |
31 |                 sort_order: request.sort_order,
   |                                     ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `name`, `description`, `color`, `icon`

error[E0308]: mismatched types
  --> src\api\commands\area_commands.rs:37:17
   |
36 |             match area_result.data {
   |                   ---------------- this expression has type `Area`
37 |                 Some(area) => Ok(AreaResponseDto::from(area)),
   |                 ^^^^^^^^^^ expected `Area`, found `Option<_>`
   |
   = note: expected struct `Area`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
36 |             match area_result.data.description {
   |                                   ++++++++++++

error[E0308]: mismatched types
  --> src\api\commands\area_commands.rs:38:17
   |
36 |             match area_result.data {
   |                   ---------------- this expression has type `Area`
37 |                 Some(area) => Ok(AreaResponseDto::from(area)),
38 |                 None => Err(crate::shared::errors::AppError::internal_error("领域创建失败"))
   |                 ^^^^ expected `Area`, found `Option<_>`
   |
   = note: expected struct `Area`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
36 |             match area_result.data.description {
   |                                   ++++++++++++

error[E0599]: no variant or associated item named `internal_error` found for enum `AppError` in the current scope
  --> src\api\commands\area_commands.rs:38:62
   |
38 |                 None => Err(crate::shared::errors::AppError::internal_error("领域创建失败"))
   |                                                              ^^^^^^^^^^^^^^ variant or associated item not found in `AppError`
   |
  ::: src\shared\errors.rs:10:1
   |
10 | pub enum AppError {
   | ----------------- variant or associated item `internal_error` not found for this enum
   |
note: if you're trying to build a new `AppError` consider using one of the following associated functions:
      AppError::database
      AppError::validation
      AppError::business_logic
      AppError::not_found
      and 4 others
  --> src\shared\errors.rs:50:5
   |
50 |     pub fn database<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57 |     pub fn validation<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
64 |     pub fn business_logic<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
71 |     pub fn not_found<S: Into<String>>(resource: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: there is an associated function `internal` with a similar name
   |
38 -                 None => Err(crate::shared::errors::AppError::internal_error("领域创建失败"))
38 +                 None => Err(crate::shared::errors::AppError::internal("领域创建失败"))
   |

error[E0382]: use of moved value: `user_id`
  --> src\api\commands\area_commands.rs:56:14
   |
51 |     let user_id = current_user_id.ok_or_else(|| "用户未认证".to_string())?;
   |         ------- move occurs because `user_id` has type `std::string::String`, which does not implement the `Copy` trait
...
56 |         Some(user_id),
   |              ^^^^^^^ value used here after move
   |
help: consider cloning the value if the performance cost is acceptable
   |
56 |         Some(user_id.clone()),
   |                     ++++++++

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:82:82
    |
82  |             let area_result = state.area_service.update_area(&context, &area_id, request).await?;
    |                                                  -----------                     ^^^^^^^ expected `UpdateAreaData`, found `UpdateAreaRequestDto`
    |                                                  |
    |                                                  arguments to this method are incorrect
    |
note: method defined here
   --> src\application\services\area_service.rs:120:18
    |
120 |     pub async fn update_area(
    |                  ^^^^^^^^^^^
...
124 |         data: UpdateAreaData,
    |         --------------------

error[E0308]: mismatched types
  --> src\api\commands\area_commands.rs:84:17
   |
83 |             match area_result.data {
   |                   ---------------- this expression has type `Area`
84 |                 Some(area) => Ok(AreaResponseDto::from(area)),
   |                 ^^^^^^^^^^ expected `Area`, found `Option<_>`
   |
   = note: expected struct `Area`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
83 |             match area_result.data.description {
   |                                   ++++++++++++

error[E0308]: mismatched types
  --> src\api\commands\area_commands.rs:85:17
   |
83 |             match area_result.data {
   |                   ---------------- this expression has type `Area`
84 |                 Some(area) => Ok(AreaResponseDto::from(area)),
85 |                 None => Err(crate::shared::errors::AppError::not_found("领域不存在"))
   |                 ^^^^ expected `Area`, found `Option<_>`
   |
   = note: expected struct `Area`
                found enum `std::option::Option<_>`
help: you might have meant to use field `description` whose type is `std::option::Option<std::string::String>`
   |
83 |             match area_result.data.description {
   |                                   ++++++++++++

error[E0282]: type annotations needed
   --> src\api\commands\area_commands.rs:106:13
    |
106 |             Ok(())
    |             ^^ cannot infer type of the type parameter `E` declared on the enum `Result`
    |
   ::: src\api\common.rs:142:21
    |
142 |                 Err(e.to_string())
    |                     - type must be known at this point
    |
help: consider specifying the generic arguments
    |
106 |             Ok::<(), E>(())
    |               +++++++++

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:128:17
    |
127 |             match areas_result.data {
    |                   ----------------- this expression has type `PagedResult<Area>`
128 |                 Some(areas) => Ok(areas.into_iter().map(AreaResponseDto::from).collect()),
    |                 ^^^^^^^^^^^ expected `PagedResult<Area>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Area>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:129:17
    |
127 |             match areas_result.data {
    |                   ----------------- this expression has type `PagedResult<Area>`
128 |                 Some(areas) => Ok(areas.into_iter().map(AreaResponseDto::from).collect()),
129 |                 None => Ok(Vec::new())
    |                 ^^^^ expected `PagedResult<Area>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Area>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:151:17
    |
150 |             match areas_result.data {
    |                   ----------------- this expression has type `PagedResult<Area>`
151 |                 Some(areas) => Ok(areas.into_iter().map(AreaResponseDto::from).collect()),
    |                 ^^^^^^^^^^^ expected `PagedResult<Area>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Area>`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:152:17
    |
150 |             match areas_result.data {
    |                   ----------------- this expression has type `PagedResult<Area>`
151 |                 Some(areas) => Ok(areas.into_iter().map(AreaResponseDto::from).collect()),
152 |                 None => Ok(Vec::new())
    |                 ^^^^ expected `PagedResult<Area>`, found `Option<_>`
    |
    = note: expected struct `PagedResult<Area>`
                 found enum `std::option::Option<_>`

error[E0282]: type annotations needed
   --> src\api\commands\area_commands.rs:174:13
    |
174 |             Ok(())
    |             ^^ cannot infer type of the type parameter `E` declared on the enum `Result`
    |
   ::: src\api\common.rs:142:21
    |
142 |                 Err(e.to_string())
    |                     - type must be known at this point
    |
help: consider specifying the generic arguments
    |
174 |             Ok::<(), E>(())
    |               +++++++++

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:196:17
    |
195 |             match streak_result.data {
    |                   ------------------ this expression has type `u32`
196 |                 Some(streak) => Ok(streak),
    |                 ^^^^^^^^^^^^ expected `u32`, found `Option<_>`
    |
    = note: expected type `u32`
               found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\api\commands\area_commands.rs:197:17
    |
195 |             match streak_result.data {
    |                   ------------------ this expression has type `u32`
196 |                 Some(streak) => Ok(streak),
197 |                 None => Ok(0)
    |                 ^^^^ expected `u32`, found `Option<_>`
    |
    = note: expected type `u32`
               found enum `std::option::Option<_>`

error[E0308]: mismatched types
  --> src\api\commands\auth_commands.rs:80:17
   |
79 |             match user_result.data {
   |                   ---------------- this expression has type `user::User`
80 |                 Some(user) => Ok(UserResponseDto::from(user)),
   |                 ^^^^^^^^^^ expected `User`, found `Option<_>`
   |
   = note: expected struct `user::User`
                found enum `std::option::Option<_>`
help: you might have meant to use field `email` whose type is `std::option::Option<std::string::String>`
   |
79 |             match user_result.data.email {
   |                                   ++++++

error[E0308]: mismatched types
  --> src\api\commands\auth_commands.rs:81:17
   |
79 |             match user_result.data {
   |                   ---------------- this expression has type `user::User`
80 |                 Some(user) => Ok(UserResponseDto::from(user)),
81 |                 None => Err(crate::shared::errors::AppError::internal_error("注册失败"))
   |                 ^^^^ expected `User`, found `Option<_>`
   |
   = note: expected struct `user::User`
                found enum `std::option::Option<_>`
help: you might have meant to use field `email` whose type is `std::option::Option<std::string::String>`
   |
79 |             match user_result.data.email {
   |                                   ++++++

error[E0599]: no variant or associated item named `internal_error` found for enum `AppError` in the current scope
  --> src\api\commands\auth_commands.rs:81:62
   |
81 |                 None => Err(crate::shared::errors::AppError::internal_error("注册失败"))
   |                                                              ^^^^^^^^^^^^^^ variant or associated item not found in `AppError`
   |
  ::: src\shared\errors.rs:10:1
   |
10 | pub enum AppError {
   | ----------------- variant or associated item `internal_error` not found for this enum
   |
note: if you're trying to build a new `AppError` consider using one of the following associated functions:
      AppError::database
      AppError::validation
      AppError::business_logic
      AppError::not_found
      and 4 others
  --> src\shared\errors.rs:50:5
   |
50 |     pub fn database<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57 |     pub fn validation<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
64 |     pub fn business_logic<S: Into<String>>(message: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
71 |     pub fn not_found<S: Into<String>>(resource: S) -> Self {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: there is an associated function `internal` with a similar name
   |
81 -                 None => Err(crate::shared::errors::AppError::internal_error("注册失败"))
81 +                 None => Err(crate::shared::errors::AppError::internal("注册失败"))
   |

error[E0282]: type annotations needed
   --> src\api\commands\auth_commands.rs:116:35
    |
116 |                     Some(user) => Ok(Some(UserResponseDto::from(user))),
    |                                   ^^ cannot infer type of the type parameter `E` declared on the enum `Result`
    |
   ::: src\api\common.rs:142:21
    |
142 |                 Err(e.to_string())
    |                     - type must be known at this point
    |
help: consider specifying the generic arguments
    |
116 |                     Some(user) => Ok::<std::option::Option<api::dto::response::UserResponseDto>, E>(Some(UserResponseDto::from(user))),
    |                                     +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

warning: unused variable: `update_result`
   --> src\api\commands\auth_commands.rs:186:29
    |
186 |                         let update_result = state.user_service.update_password(&context, &user_id, &new_password).await?;
    |                             ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_update_result`

error[E0599]: no method named `id` found for struct `user::User` in the current scope
  --> src\api\handlers\middleware.rs:60:37
   |
60 |                     .with_user(user.id().clone())
   |                                     ^^-- help: remove the arguments
   |                                     |
   |                                     field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `id` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `username` found for struct `user::User` in the current scope
  --> src\api\handlers\middleware.rs:61:65
   |
61 |                     .with_metadata("username".to_string(), user.username().clone());
   |                                                                 ^^^^^^^^-- help: remove the arguments
   |                                                                 |
   |                                                                 field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `username` not found for this struct

error[E0599]: no method named `id` found for struct `user::User` in the current scope
  --> src\api\handlers\middleware.rs:65:37
   |
65 |                     user_id = %user.id(),
   |                                     ^^-- help: remove the arguments
   |                                     |
   |                                     field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `id` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

warning: unused variable: `token`
   --> src\api\handlers\websocket_handler.rs:320:46
    |
320 |             WebSocketMessage::Authenticate { token } => {
    |                                              ^^^^^ help: try ignoring the field: `token: _`

error[E0308]: mismatched types
   --> src\infrastructure\database\connection.rs:136:19
    |
136 |             idle: self.pool.num_idle(),
    |                   ^^^^^^^^^^^^^^^^^^^^ expected `u32`, found `usize`

error[E0308]: mismatched types
   --> src\infrastructure\database\connection.rs:137:38
    |
137 |             used: self.pool.size() - self.pool.num_idle(),
    |                                      ^^^^^^^^^^^^^^^^^^^^ expected `u32`, found `usize`

error[E0277]: cannot subtract `usize` from `u32`
   --> src\infrastructure\database\connection.rs:137:36
    |
137 |             used: self.pool.size() - self.pool.num_idle(),
    |                                    ^ no implementation for `u32 - usize`
    |
    = help: the trait `Sub<usize>` is not implemented for `u32`
    = help: the following other types implement trait `Sub<Rhs>`:
              `&u32` implements `Sub<u32>`
              `&u32` implements `Sub`
              `u32` implements `Sub<&u32>`
              `u32` implements `Sub<zerocopy::byteorder::U32<O>>`
              `u32` implements `Sub`

error[E0599]: no method named `get` found for struct `SqliteRow` in the current scope
   --> src\infrastructure\database\connection.rs:201:44
    |
201 |             sqlite_version: sqlite_version.get("version"),
    |                                            ^^^ method not found in `SqliteRow`
    |
   ::: C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\sqlx-core-0.8.6\src\row.rs:68:8
    |
68  |     fn get<'r, T, I>(&'r self, index: I) -> T
    |        --- the method is available for `SqliteRow` here
    |
    = help: items from traits can only be used if the trait is in scope
help: trait `Row` which provides `get` is implemented but not in scope; perhaps you want to import it
    |
4   + use sqlx::Row;
    |

warning: unused variable: `exclude_id`
   --> src\infrastructure\database\validators\project_validator.rs:187:9
    |
187 |         exclude_id: Option<&str>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_exclude_id`

warning: unused variable: `user_id`
   --> src\infrastructure\database\validators\area_validator.rs:237:9
    |
237 |         user_id: &str,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `exclude_id`
   --> src\infrastructure\database\validators\area_validator.rs:238:9
    |
238 |         exclude_id: Option<&str>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_exclude_id`

warning: unused variable: `user_id`
   --> src\infrastructure\database\validators\area_validator.rs:318:9
    |
318 |         user_id: &str,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `exclude_id`
   --> src\infrastructure\database\validators\area_validator.rs:319:9
    |
319 |         exclude_id: Option<&str>,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_exclude_id`

error[E0277]: the trait bound `impl Layer<Registry>: Layer<Layered<EnvFilter, Registry>>` is not satisfied
    --> src\infrastructure\config\logging.rs:86:34
     |
86   |                 Ok(registry.with(console_layer))
     |                             ---- ^^^^^^^^^^^^^ the trait `__tracing_subscriber_Layer<Layered<EnvFilter, Registry>>` is not implemented for `impl __tracing_subscriber_Layer<Registry>`
     |                             |
     |                             required by a bound introduced by this call
     |
     = help: the following other types implement trait `__tracing_subscriber_Layer<S>`:
               Box<(dyn __tracing_subscriber_Layer<S> + Send + Sync + 'static)>
               Box<L>
               DynFilterFn<S, F, R>
               EnvFilter
               FilterFn<F>
               Filtered<L, F, S>
               Identity
               Layered<A, B, S>
             and 6 others
note: required by a bound in `with`
    --> C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tracing-subscriber-0.3.19\src\layer\mod.rs:1504:12
     |
1502 |     fn with<L>(self, layer: L) -> Layered<L, Self>
     |        ---- required by a bound in this associated function
1503 |     where
1504 |         L: Layer<Self>,
     |            ^^^^^^^^^^^ required by this bound in `__tracing_subscriber_SubscriberExt::with`
     = note: the full name for the type has been written to 'D:\0DevelopeRepository\RustProgram\PaoLife\src-tauri\target\debug\deps\paolife.long-type-9283322967770791699.txt'
     = note: consider using `--verbose` to print the full type name to the console

error[E0277]: the trait bound `impl Layer<Registry>: Layer<Layered<EnvFilter, Registry>>` is not satisfied
  --> src\infrastructure\config\logging.rs:80:69
   |
80 |     fn create_subscriber(&self, env_filter: EnvFilter) -> AppResult<impl Subscriber + Send + Sync> {
   |                                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ the trait `__tracing_subscriber_Layer<Layered<EnvFilter, Registry>>` is not implemented for `impl __tracing_subscriber_Layer<Registry>`
   |
   = help: the following other types implement trait `__tracing_subscriber_Layer<S>`:
             Box<(dyn __tracing_subscriber_Layer<S> + Send + Sync + 'static)>
             Box<L>
             DynFilterFn<S, F, R>
             EnvFilter
             FilterFn<F>
             Filtered<L, F, S>
             Identity
             Layered<A, B, S>
           and 6 others
   = note: required for `Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>` to implement `tracing::Subscriber`
   = note: the full name for the type has been written to 'D:\0DevelopeRepository\RustProgram\PaoLife\src-tauri\target\debug\deps\paolife.long-type-9283322967770791699.txt'
   = note: consider using `--verbose` to print the full type name to the console

error[E0308]: mismatched types
    --> src\infrastructure\config\logging.rs:90:34
     |
90   |                 Ok(registry.with(file_layer))
     |                             ---- ^^^^^^^^^^ expected opaque type, found a different opaque type
     |                             |
     |                             arguments to this method are incorrect
...
101  |     fn create_console_layer(&self) -> AppResult<impl Layer<Registry>> {
     |                                                 -------------------- the expected opaque type
...
137  |     fn create_file_layer(&self) -> AppResult<impl Layer<Registry>> {
     |                                              -------------------- the found opaque type
     |
     = note: expected opaque type `impl __tracing_subscriber_Layer<Registry>`
                found opaque type `impl __tracing_subscriber_Layer<Registry>`
     = note: distinct uses of `impl Trait` result in different opaque types
help: the return type of this call is `impl __tracing_subscriber_Layer<Registry>` due to the type of the argument passed
    --> src\infrastructure\config\logging.rs:90:20
     |
90   |                 Ok(registry.with(file_layer))
     |                    ^^^^^^^^^^^^^^----------^
     |                                  |
     |                                  this argument influences the return type of `with`
note: method defined here
    --> C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tracing-subscriber-0.3.19\src\layer\mod.rs:1502:8
     |
1502 |     fn with<L>(self, layer: L) -> Layered<L, Self>
     |        ^^^^

error[E0277]: the trait bound `impl Layer<Registry>: Layer<Layered<EnvFilter, Registry>>` is not satisfied
    --> src\infrastructure\config\logging.rs:90:34
     |
90   |                 Ok(registry.with(file_layer))
     |                             ---- ^^^^^^^^^^ the trait `__tracing_subscriber_Layer<Layered<EnvFilter, Registry>>` is not implemented for `impl __tracing_subscriber_Layer<Registry>`
     |                             |
     |                             required by a bound introduced by this call
     |
     = help: the following other types implement trait `__tracing_subscriber_Layer<S>`:
               Box<(dyn __tracing_subscriber_Layer<S> + Send + Sync + 'static)>
               Box<L>
               DynFilterFn<S, F, R>
               EnvFilter
               FilterFn<F>
               Filtered<L, F, S>
               Identity
               Layered<A, B, S>
             and 6 others
note: required by a bound in `with`
    --> C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tracing-subscriber-0.3.19\src\layer\mod.rs:1504:12
     |
1502 |     fn with<L>(self, layer: L) -> Layered<L, Self>
     |        ---- required by a bound in this associated function
1503 |     where
1504 |         L: Layer<Self>,
     |            ^^^^^^^^^^^ required by this bound in `__tracing_subscriber_SubscriberExt::with`
     = note: the full name for the type has been written to 'D:\0DevelopeRepository\RustProgram\PaoLife\src-tauri\target\debug\deps\paolife.long-type-9283322967770791699.txt'
     = note: consider using `--verbose` to print the full type name to the console

error[E0277]: the trait bound `impl Layer<Registry>: Layer<Layered<EnvFilter, Registry>>` is not satisfied
    --> src\infrastructure\config\logging.rs:95:34
     |
95   |                 Ok(registry.with(console_layer).with(file_layer))
     |                             ---- ^^^^^^^^^^^^^ the trait `__tracing_subscriber_Layer<Layered<EnvFilter, Registry>>` is not implemented for `impl __tracing_subscriber_Layer<Registry>`
     |                             |
     |                             required by a bound introduced by this call
     |
     = help: the following other types implement trait `__tracing_subscriber_Layer<S>`:
               Box<(dyn __tracing_subscriber_Layer<S> + Send + Sync + 'static)>
               Box<L>
               DynFilterFn<S, F, R>
               EnvFilter
               FilterFn<F>
               Filtered<L, F, S>
               Identity
               Layered<A, B, S>
             and 6 others
note: required by a bound in `with`
    --> C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tracing-subscriber-0.3.19\src\layer\mod.rs:1504:12
     |
1502 |     fn with<L>(self, layer: L) -> Layered<L, Self>
     |        ---- required by a bound in this associated function
1503 |     where
1504 |         L: Layer<Self>,
     |            ^^^^^^^^^^^ required by this bound in `__tracing_subscriber_SubscriberExt::with`
     = note: the full name for the type has been written to 'D:\0DevelopeRepository\RustProgram\PaoLife\src-tauri\target\debug\deps\paolife.long-type-9283322967770791699.txt'
     = note: consider using `--verbose` to print the full type name to the console

error[E0599]: the method `with` exists for struct `Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>`, but its trait bounds were not satisfied
  --> src\infrastructure\config\logging.rs:95:49
   |
95 |                 Ok(registry.with(console_layer).with(file_layer))
   |                                                 ^^^^ method cannot be called due to unsatisfied trait bounds
   |
  ::: C:\Users\<USER>\.cargo\registry\src\rsproxy.cn-e3de039b2554c837\tracing-subscriber-0.3.19\src\layer\layered.rs:22:1
   |
22 | pub struct Layered<L, I, S = I> {
   | ------------------------------- doesn't satisfy `_: Subscriber` or `_: __tracing_subscriber_SubscriberExt`
   |
   = note: the following trait bounds were not satisfied:
           `Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>: tracing::Subscriber`
           which is required by `Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>: SubscriberExt`
           `&Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>: tracing::Subscriber`
           which is required by `&Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>: SubscriberExt`
           `&mut Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>: tracing::Subscriber`
           which is required by `&mut Layered<impl __tracing_subscriber_Layer<Registry>, Layered<EnvFilter, Registry>>: SubscriberExt`

error[E0382]: borrow of moved value: `user`
   --> src\application\services\user_service.rs:50:43
    |
39  |             Ok(user) => {
    |                ---- move occurs because `user` has type `user::User`, which does not implement the `Copy` trait
...
48  |                 Ok(ServiceResult::new(user)
    |                                       ---- value moved here
49  |                     .with_execution_time(execution_time)
50  |                     .with_affected_entity(user.id.clone()))
    |                                           ^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
48  |                 Ok(ServiceResult::new(user.clone())
    |                                           ++++++++

error[E0382]: borrow of moved value: `user`
   --> src\application\services\user_service.rs:179:43
    |
168 |             Ok(user) => {
    |                ---- move occurs because `user` has type `user::User`, which does not implement the `Copy` trait
...
177 |                 Ok(ServiceResult::new(user)
    |                                       ---- value moved here
178 |                     .with_execution_time(execution_time)
179 |                     .with_affected_entity(user.id.clone()))
    |                                           ^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
177 |                 Ok(ServiceResult::new(user.clone())
    |                                           ++++++++

error[E0382]: borrow of moved value: `project`
   --> src\application\services\project_service.rs:59:43
    |
48  |             Ok(project) => {
    |                ------- move occurs because `project` has type `Project`, which does not implement the `Copy` trait
...
57  |                 Ok(ServiceResult::new(project)
    |                                       ------- value moved here
58  |                     .with_execution_time(execution_time)
59  |                     .with_affected_entity(project.id.clone()))
    |                                           ^^^^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
57  |                 Ok(ServiceResult::new(project.clone())
    |                                              ++++++++

error[E0382]: borrow of moved value: `project`
   --> src\application\services\project_service.rs:159:43
    |
148 |             Ok(project) => {
    |                ------- move occurs because `project` has type `Project`, which does not implement the `Copy` trait
...
157 |                 Ok(ServiceResult::new(project)
    |                                       ------- value moved here
158 |                     .with_execution_time(execution_time)
159 |                     .with_affected_entity(project.id.clone()))
    |                                           ^^^^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
157 |                 Ok(ServiceResult::new(project.clone())
    |                                              ++++++++

error[E0382]: borrow of moved value: `status`
   --> src\application\services\project_service.rs:329:70
    |
299 |         status: ProjectStatus,
    |         ------ move occurs because `status` has type `ProjectStatus`, which does not implement the `Copy` trait
...
318 |         let (result, execution_time) = ServiceUtils::measure_time(async {
    |                                                                   ----- value moved here
319 |             self.project_repository.update_status(id, status).await
    |                                                       ------ variable moved due to use in coroutine
...
329 |                     Some(&format!("Updated project status to: {:?}", status)),
    |                                                                      ^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `update_status` to borrow instead if owning the value isn't necessary
   --> src\domain\repositories\project_repository.rs:56:58
    |
56  |     async fn update_status(&self, id: &EntityId, status: ProjectStatus) -> AppResult<()>;
    |              ------------- in this method                ^^^^^^^^^^^^^ this parameter takes ownership of the value
    = note: this error originates in the macro `$crate::__export::format_args` which comes from the expansion of the macro `format` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider cloning the value if the performance cost is acceptable
    |
319 |             self.project_repository.update_status(id, status.clone()).await
    |                                                             ++++++++

error[E0382]: borrow of moved value: `task`
   --> src\application\services\task_service.rs:53:43
    |
42  |             Ok(task) => {
    |                ---- move occurs because `task` has type `Task`, which does not implement the `Copy` trait
...
51  |                 Ok(ServiceResult::new(task)
    |                                       ---- value moved here
52  |                     .with_execution_time(execution_time)
53  |                     .with_affected_entity(task.id.clone()))
    |                                           ^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
51  |                 Ok(ServiceResult::new(task.clone())
    |                                           ++++++++

error[E0382]: borrow of moved value: `task`
   --> src\application\services\task_service.rs:157:43
    |
146 |             Ok(task) => {
    |                ---- move occurs because `task` has type `Task`, which does not implement the `Copy` trait
...
155 |                 Ok(ServiceResult::new(task)
    |                                       ---- value moved here
156 |                     .with_execution_time(execution_time)
157 |                     .with_affected_entity(task.id.clone()))
    |                                           ^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
155 |                 Ok(ServiceResult::new(task.clone())
    |                                           ++++++++

error[E0609]: no field `created_by` on type `Task`
   --> src\application\services\task_service.rs:236:26
    |
236 |         if existing_task.created_by != *user_id && existing_task.assigned_to.as_ref() != Some(user_id) {
    |                          ^^^^^^^^^^ unknown field
    |
help: a field with a similar name exists
    |
236 -         if existing_task.created_by != *user_id && existing_task.assigned_to.as_ref() != Some(user_id) {
236 +         if existing_task.created_at != *user_id && existing_task.assigned_to.as_ref() != Some(user_id) {
    |

error[E0599]: no method named `update_completion` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\task_service.rs:249:34
    |
249 |             self.task_repository.update_completion(id, 100).await?;
    |                                  ^^^^^^^^^^^^^^^^^
    |
help: there is a method `update_completion_percentage` with a similar name
    |
249 |             self.task_repository.update_completion_percentage(id, 100).await?;
    |                                                   +++++++++++

error[E0382]: borrow of moved value: `status`
   --> src\application\services\task_service.rs:388:67
    |
356 |         status: TaskStatus,
    |         ------ move occurs because `status` has type `TaskStatus`, which does not implement the `Copy` trait
...
377 |         let (result, execution_time) = ServiceUtils::measure_time(async {
    |                                                                   ----- value moved here
378 |             self.task_repository.update_status(id, status).await
    |                                                    ------ variable moved due to use in coroutine
...
388 |                     Some(&format!("Updated task status to: {:?}", status)),
    |                                                                   ^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `update_status` to borrow instead if owning the value isn't necessary
   --> src\domain\repositories\task_repository.rs:68:58
    |
68  |     async fn update_status(&self, id: &EntityId, status: TaskStatus) -> AppResult<()>;
    |              ------------- in this method                ^^^^^^^^^^ this parameter takes ownership of the value
    = note: this error originates in the macro `$crate::__export::format_args` which comes from the expansion of the macro `format` (in Nightly builds, run with -Z macro-backtrace for more info)
help: consider cloning the value if the performance cost is acceptable
    |
378 |             self.task_repository.update_status(id, status.clone()).await
    |                                                          ++++++++

error[E0382]: borrow of moved value: `area`
   --> src\application\services\area_service.rs:58:43
    |
47  |             Ok(area) => {
    |                ---- move occurs because `area` has type `Area`, which does not implement the `Copy` trait
...
56  |                 Ok(ServiceResult::new(area)
    |                                       ---- value moved here
57  |                     .with_execution_time(execution_time)
58  |                     .with_affected_entity(area.id.clone()))
    |                                           ^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
56  |                 Ok(ServiceResult::new(area.clone())
    |                                           ++++++++

error[E0382]: borrow of moved value: `area`
   --> src\application\services\area_service.rs:158:43
    |
147 |             Ok(area) => {
    |                ---- move occurs because `area` has type `Area`, which does not implement the `Copy` trait
...
156 |                 Ok(ServiceResult::new(area)
    |                                       ---- value moved here
157 |                     .with_execution_time(execution_time)
158 |                     .with_affected_entity(area.id.clone()))
    |                                           ^^^^^^^ value borrowed here after move
    |
note: consider changing this parameter type in method `new` to borrow instead if owning the value isn't necessary
   --> src\application\services\mod.rs:118:22
    |
118 |     pub fn new(data: T) -> Self {
    |            ---       ^ this parameter takes ownership of the value
    |            |
    |            in this method
help: consider cloning the value if the performance cost is acceptable
    |
156 |                 Ok(ServiceResult::new(area.clone())
    |                                           ++++++++

error[E0560]: struct `CreateUserData` has no field named `created_by`
   --> src\application\services\auth_service.rs:103:13
    |
103 |             created_by: "system".to_string(),
    |             ^^^^^^^^^^ `CreateUserData` does not have this field
    |
    = note: all struct fields are already assigned

error[E0599]: no method named `create_user` found for struct `Arc<(dyn UserRepository + 'static)>` in the current scope
   --> src\application\services\auth_service.rs:138:41
    |
138 |         let user = self.user_repository.create_user(final_data, user_id).await?;
    |                                         ^^^^^^^^^^^
    |
help: there is a method `create` with a similar name, but with different arguments
   --> src\domain\repositories\user_repository.rs:13:5
    |
13  |     async fn create(&self, data: CreateUserData) -> AppResult<User>;
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `is_active` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:156:18
    |
156 |         if !user.is_active() {
    |                  ^^^^^^^^^-- help: remove the arguments
    |                  |
    |                  field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `is_active` not found for this struct

error[E0599]: no method named `password_hash` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:161:58
    |
161 |         if !self.verify_password(&request.password, user.password_hash())? {
    |                                                          ^^^^^^^^^^^^^-- help: remove the arguments
    |                                                          |
    |                                                          field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `password_hash` not found for this struct

error[E0599]: no method named `id` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:166:60
    |
166 |         let access_token = self.generate_access_token(user.id())?;
    |                                                            ^^-- help: remove the arguments
    |                                                            |
    |                                                            field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `id` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `id`, perhaps you need to implement one of them:
            candidate #1: `IsMenuItem`
            candidate #2: `SpanData`
            candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `id` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:170:51
    |
170 |             Some(self.generate_refresh_token(user.id())?)
    |                                                   ^^-- help: remove the arguments
    |                                                   |
    |                                                   field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `id` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `id`, perhaps you need to implement one of them:
            candidate #1: `IsMenuItem`
            candidate #2: `SpanData`
            candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `id` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:176:53
    |
176 |         self.user_repository.update_last_login(user.id()).await?;
    |                                                     ^^-- help: remove the arguments
    |                                                     |
    |                                                     field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `id` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `id`, perhaps you need to implement one of them:
            candidate #1: `IsMenuItem`
            candidate #2: `SpanData`
            candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `id` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:179:29
    |
179 |             user_id = %user.id(),
    |                             ^^-- help: remove the arguments
    |                             |
    |                             field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `id` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `id`, perhaps you need to implement one of them:
            candidate #1: `IsMenuItem`
            candidate #2: `SpanData`
            candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `username` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:180:30
    |
180 |             username = %user.username(),
    |                              ^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `username` not found for this struct

error[E0599]: no method named `is_active` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:221:18
    |
221 |         if !user.is_active() {
    |                  ^^^^^^^^^-- help: remove the arguments
    |                  |
    |                  field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `is_active` not found for this struct

error[E0599]: no method named `is_active` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:250:18
    |
250 |         if !user.is_active() {
    |                  ^^^^^^^^^-- help: remove the arguments
    |                  |
    |                  field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `is_active` not found for this struct

error[E0308]: mismatched types
   --> src\application\services\auth_service.rs:264:52
    |
264 |         let user = self.user_repository.find_by_id(user_id).await?
    |                                         ---------- ^^^^^^^ expected `&String`, found `&str`
    |                                         |
    |                                         arguments to this method are incorrect
    |
    = note: expected reference `&std::string::String`
               found reference `&str`
note: method defined here
   --> src\domain\repositories\user_repository.rs:16:14
    |
16  |     async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<User>>;
    |              ^^^^^^^^^^        --

error[E0599]: no method named `password_hash` found for struct `user::User` in the current scope
   --> src\application\services\auth_service.rs:268:66
    |
268 |         if !self.verify_password(&request.current_password, user.password_hash())? {
    |                                                                  ^^^^^^^^^^^^^-- help: remove the arguments
    |                                                                  |
    |                                                                  field, not a method
    |
   ::: src\domain\entities\user.rs:11:1
    |
11  | pub struct User {
    | --------------- method `password_hash` not found for this struct

error[E0308]: mismatched types
   --> src\application\services\auth_service.rs:279:46
    |
279 |         self.user_repository.update_password(user_id, &new_password_hash).await?;
    |                              --------------- ^^^^^^^ expected `&String`, found `&str`
    |                              |
    |                              arguments to this method are incorrect
    |
    = note: expected reference `&std::string::String`
               found reference `&str`
note: method defined here
   --> src\domain\repositories\user_repository.rs:52:14
    |
52  |     async fn update_password(&self, id: &EntityId, password_hash: &str) -> AppResult<()>;
    |              ^^^^^^^^^^^^^^^        --

error[E0599]: no method named `exists` found for struct `Arc<(dyn UserRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:192:34
    |
192 |         if !self.user_repository.exists(user_id).await? {
    |                                  ^^^^^^
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `exists`, perhaps you need to implement it:
            candidate #1: `tantivy::Directory`
help: there is a method `email_exists` with a similar name
    |
192 |         if !self.user_repository.email_exists(user_id).await? {
    |                                   ++++++

error[E0599]: no method named `count_by_user` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:227:54
    |
227 |         let total_projects = self.project_repository.count_by_user(user_id).await?;
    |                                                      ^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`

error[E0599]: no method named `count_active_by_user` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:228:55
    |
228 |         let active_projects = self.project_repository.count_active_by_user(user_id).await?;
    |                                                       ^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`

error[E0599]: no method named `count_completed_by_user` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:229:58
    |
229 |         let completed_projects = self.project_repository.count_completed_by_user(user_id).await?;
    |                                                          ^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`

error[E0599]: no method named `count_by_user` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:232:48
    |
232 |         let total_tasks = self.task_repository.count_by_user(user_id).await?;
    |                                                ^^^^^^^^^^^^^ method not found in `Arc<(dyn TaskRepository + 'static)>`

error[E0599]: no method named `count_completed_by_user` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:233:52
    |
233 |         let completed_tasks = self.task_repository.count_completed_by_user(user_id).await?;
    |                                                    ^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn TaskRepository + 'static)>`

error[E0599]: no method named `count_by_user` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:243:48
    |
243 |         let total_areas = self.area_repository.count_by_user(user_id).await?;
    |                                                ^^^^^^^^^^^^^ method not found in `Arc<(dyn AreaRepository + 'static)>`

error[E0599]: no method named `count_active_by_user` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:244:49
    |
244 |         let active_areas = self.area_repository.count_active_by_user(user_id).await?;
    |                                                 ^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn AreaRepository + 'static)>`

error[E0599]: no method named `count_by_status` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:261:49
    |
261 |         let by_status = self.project_repository.count_by_status(user_id).await?;
    |                                                 ^^^^^^^^^^^^^^^
    |
help: there is a method `find_by_status` with a similar name, but with different arguments
   --> src\domain\repositories\project_repository.rs:38:5
    |
38  |     async fn find_by_status(&self, status: ProjectStatus, params: QueryParams) -> AppResult<Vec<Project>>;
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `count_by_priority` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:264:51
    |
264 |         let by_priority = self.project_repository.count_by_priority(user_id).await?;
    |                                                   ^^^^^^^^^^^^^^^^^
    |
help: there is a method `find_by_priority` with a similar name, but with different arguments
   --> src\domain\repositories\project_repository.rs:41:5
    |
41  |     async fn find_by_priority(&self, priority: Priority, params: QueryParams) -> AppResult<Vec<Project>>;
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `count_by_area` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:267:47
    |
267 |         let by_area = self.project_repository.count_by_area(user_id).await?;
    |                                               ^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`

error[E0599]: no method named `get_average_progress` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:270:56
    |
270 |         let average_progress = self.project_repository.get_average_progress(user_id).await?;
    |                                                        ^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`

error[E0599]: no method named `count_overdue` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:273:53
    |
273 |         let overdue_count = self.project_repository.count_overdue(user_id).await?;
    |                                                     ^^^^^^^^^^^^^
    |
help: there is a method `find_overdue` with a similar name
    |
273 -         let overdue_count = self.project_repository.count_overdue(user_id).await?;
273 +         let overdue_count = self.project_repository.find_overdue(user_id).await?;
    |

error[E0599]: no method named `get_hours_summary` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:286:37
    |
286 |             self.project_repository.get_hours_summary(user_id).await?;
    |                                     ^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`

error[E0599]: no method named `count_by_status` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:303:46
    |
303 |         let by_status = self.task_repository.count_by_status(user_id).await?;
    |                                              ^^^^^^^^^^^^^^^
    |
help: there is a method `find_by_status` with a similar name, but with different arguments
   --> src\domain\repositories\task_repository.rs:44:5
    |
44  |     async fn find_by_status(&self, status: TaskStatus, params: QueryParams) -> AppResult<Vec<Task>>;
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `count_by_priority` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:306:48
    |
306 |         let by_priority = self.task_repository.count_by_priority(user_id).await?;
    |                                                ^^^^^^^^^^^^^^^^^
    |
help: there is a method `find_by_priority` with a similar name, but with different arguments
   --> src\domain\repositories\task_repository.rs:47:5
    |
47  |     async fn find_by_priority(&self, priority: Priority, params: QueryParams) -> AppResult<Vec<Task>>;
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `count_by_project` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:309:47
    |
309 |         let by_project = self.task_repository.count_by_project(user_id).await?;
    |                                               ^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn TaskRepository + 'static)>`

error[E0599]: no method named `get_average_completion` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:312:55
    |
312 |         let average_completion = self.task_repository.get_average_completion(user_id).await?;
    |                                                       ^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn TaskRepository + 'static)>`

error[E0599]: no method named `count_overdue` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:315:50
    |
315 |         let overdue_count = self.task_repository.count_overdue(user_id).await?;
    |                                                  ^^^^^^^^^^^^^
    |
help: there is a method `find_overdue` with a similar name
    |
315 -         let overdue_count = self.task_repository.count_overdue(user_id).await?;
315 +         let overdue_count = self.task_repository.find_overdue(user_id).await?;
    |

error[E0599]: no method named `get_time_summary` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:328:34
    |
328 |             self.task_repository.get_time_summary(user_id).await?;
    |                                  ^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn TaskRepository + 'static)>`

error[E0599]: no method named `count_by_user` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:344:48
    |
344 |         let total_areas = self.area_repository.count_by_user(user_id).await?;
    |                                                ^^^^^^^^^^^^^ method not found in `Arc<(dyn AreaRepository + 'static)>`

error[E0599]: no method named `count_active_by_user` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:345:49
    |
345 |         let active_areas = self.area_repository.count_active_by_user(user_id).await?;
    |                                                 ^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn AreaRepository + 'static)>`

error[E0599]: no method named `count_with_projects` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:346:56
    |
346 |         let areas_with_projects = self.area_repository.count_with_projects(user_id).await?;
    |                                                        ^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn AreaRepository + 'static)>`

error[E0599]: no method named `count_with_tasks` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:347:53
    |
347 |         let areas_with_tasks = self.area_repository.count_with_tasks(user_id).await?;
    |                                                     ^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn AreaRepository + 'static)>`

error[E0599]: no method named `get_activity_stats` found for struct `Arc<(dyn AreaRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:350:51
    |
350 |         let activity_stats = self.area_repository.get_activity_stats(user_id).await?;
    |                                                   ^^^^^^^^^^^^^^^^^^
    |
help: there is a method `get_stats` with a similar name
    |
350 -         let activity_stats = self.area_repository.get_activity_stats(user_id).await?;
350 +         let activity_stats = self.area_repository.get_stats(user_id).await?;
    |

warning: unused variable: `user_id`
   --> src\application\services\analytics_service.rs:365:43
    |
365 |     async fn get_recent_activities(&self, user_id: &str, limit: usize) -> AppResult<Vec<RecentActivity>> {
    |                                           ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `limit`
   --> src\application\services\analytics_service.rs:365:58
    |
365 |     async fn get_recent_activities(&self, user_id: &str, limit: usize) -> AppResult<Vec<RecentActivity>> {
    |                                                          ^^^^^ help: if this is intentional, prefix it with an underscore: `_limit`

error[E0599]: no method named `find_with_deadlines_between` found for struct `Arc<(dyn ProjectRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:409:14
    |
408 |           let project_deadlines = self.project_repository
    |  _________________________________-
409 | |             .find_with_deadlines_between(user_id, today, end_date).await?;
    | |             -^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn ProjectRepository + 'static)>`
    | |_____________|
    |

error[E0599]: no method named `find_with_deadlines_between` found for struct `Arc<(dyn TaskRepository + 'static)>` in the current scope
   --> src\application\services\analytics_service.rs:428:14
    |
427 |           let task_deadlines = self.task_repository
    |  ______________________________-
428 | |             .find_with_deadlines_between(user_id, today, end_date).await?;
    | |             -^^^^^^^^^^^^^^^^^^^^^^^^^^^ method not found in `Arc<(dyn TaskRepository + 'static)>`
    | |_____________|
    |

warning: unused variable: `user_id`
   --> src\application\services\analytics_service.rs:454:9
    |
454 |         user_id: &str,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `start_date`
   --> src\application\services\analytics_service.rs:455:9
    |
455 |         start_date: NaiveDate,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_start_date`

warning: unused variable: `end_date`
   --> src\application\services\analytics_service.rs:456:9
    |
456 |         end_date: NaiveDate,
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_end_date`

warning: unused variable: `user_id`
   --> src\application\services\analytics_service.rs:466:9
    |
466 |         user_id: &str,
    |         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `start_date`
   --> src\application\services\analytics_service.rs:467:9
    |
467 |         start_date: NaiveDate,
    |         ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_start_date`

warning: unused variable: `end_date`
   --> src\application\services\analytics_service.rs:468:9
    |
468 |         end_date: NaiveDate,
    |         ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_end_date`

warning: unused variable: `user_id`
   --> src\application\services\analytics_service.rs:476:41
    |
476 |     async fn get_monthly_summary(&self, user_id: &str, months: usize) -> AppResult<Vec<MonthlySummary>> {
    |                                         ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

warning: unused variable: `months`
   --> src\application\services\analytics_service.rs:476:56
    |
476 |     async fn get_monthly_summary(&self, user_id: &str, months: usize) -> AppResult<Vec<MonthlySummary>> {
    |                                                        ^^^^^^ help: if this is intentional, prefix it with an underscore: `_months`

warning: unused variable: `user_id`
   --> src\application\services\analytics_service.rs:483:48
    |
483 |     async fn calculate_efficiency_score(&self, user_id: &str) -> AppResult<f64> {
    |                                                ^^^^^^^ help: if this is intentional, prefix it with an underscore: `_user_id`

error[E0599]: no method named `pool` found for struct `connection::DatabaseConnection` in the current scope
  --> src\application\services\service_container.rs:51:37
   |
51 |         let db_pool = db_connection.pool().clone();
   |                                     ^^^^ private field, not a method
   |
  ::: src\infrastructure\database\connection.rs:11:1
   |
11 | pub struct DatabaseConnection {
   | ----------------------------- method `pool` not found for this struct
   |
help: there is a method `get_pool` with a similar name
   |
51 |         let db_pool = db_connection.get_pool().clone();
   |                                     ++++

error[E0061]: this function takes 1 argument but 3 arguments were supplied
  --> src\application\services\service_container.rs:71:40
   |
71 |         let project_service = Arc::new(ProjectService::new(
   |                                        ^^^^^^^^^^^^^^^^^^^
72 |             project_repository.clone(),
73 |             area_repository.clone(),
   |             ----------------------- unexpected argument #2 of type `Arc<dyn AreaRepository>`
74 |             user_repository.clone(),
   |             ----------------------- unexpected argument #3 of type `Arc<dyn UserRepository>`
   |
note: associated function defined here
  --> src\application\services\project_service.rs:18:12
   |
18 |     pub fn new(project_repository: Arc<dyn ProjectRepository>) -> Self {
   |            ^^^
help: remove the extra arguments
   |
72 -             project_repository.clone(),
73 -             area_repository.clone(),
72 +             project_repository.clone(),
   |

error[E0061]: this function takes 1 argument but 4 arguments were supplied
  --> src\application\services\service_container.rs:77:37
   |
77 |         let task_service = Arc::new(TaskService::new(
   |                                     ^^^^^^^^^^^^^^^^
78 |             task_repository.clone(),
79 |             project_repository.clone(),
   |             -------------------------- unexpected argument #2 of type `Arc<dyn ProjectRepository>`
80 |             area_repository.clone(),
   |             ----------------------- unexpected argument #3 of type `Arc<dyn AreaRepository>`
81 |             user_repository.clone(),
   |             ----------------------- unexpected argument #4 of type `Arc<dyn UserRepository>`
   |
note: associated function defined here
  --> src\application\services\task_service.rs:18:12
   |
18 |     pub fn new(task_repository: Arc<dyn TaskRepository>) -> Self {
   |            ^^^
help: remove the extra arguments
   |
78 -             task_repository.clone(),
79 -             project_repository.clone(),
78 +             task_repository.clone(),
   |

error[E0061]: this function takes 1 argument but 2 arguments were supplied
  --> src\application\services\service_container.rs:84:37
   |
84 |         let area_service = Arc::new(AreaService::new(
   |                                     ^^^^^^^^^^^^^^^^
85 |             area_repository.clone(),
86 |             user_repository.clone(),
   |             ----------------------- unexpected argument #2 of type `Arc<dyn UserRepository>`
   |
note: associated function defined here
  --> src\application\services\area_service.rs:18:12
   |
18 |     pub fn new(area_repository: Arc<dyn AreaRepository>) -> Self {
   |            ^^^
help: remove the extra argument
   |
85 -             area_repository.clone(),
86 -             user_repository.clone(),
85 +             area_repository.clone(),
   |

error[E0308]: arguments to this function are incorrect
  --> src\lib.rs:64:21
   |
64 |     let api_state = ApiState::new(
   |                     ^^^^^^^^^^^^^
   |
note: expected `Arc<UserService>`, found `UserService`
  --> src\lib.rs:65:9
   |
65 |         user_service,
   |         ^^^^^^^^^^^^
   = note: expected struct `Arc<user_service::UserService>`
              found struct `user_service::UserService`
note: expected `Arc<ProjectService>`, found `ProjectService`
  --> src\lib.rs:66:9
   |
66 |         project_service,
   |         ^^^^^^^^^^^^^^^
   = note: expected struct `Arc<project_service::ProjectService>`
              found struct `project_service::ProjectService`
note: expected `Arc<TaskService>`, found `TaskService`
  --> src\lib.rs:67:9
   |
67 |         task_service,
   |         ^^^^^^^^^^^^
   = note: expected struct `Arc<task_service::TaskService>`
              found struct `task_service::TaskService`
note: expected `Arc<AreaService>`, found `AreaService`
  --> src\lib.rs:68:9
   |
68 |         area_service,
   |         ^^^^^^^^^^^^
   = note: expected struct `Arc<area_service::AreaService>`
              found struct `area_service::AreaService`
note: associated function defined here
  --> src\api\mod.rs:30:12
   |
30 |     pub fn new(
   |            ^^^
31 |         user_service: Arc<UserService>,
   |         ------------------------------
32 |         project_service: Arc<ProjectService>,
   |         ------------------------------------
33 |         task_service: Arc<TaskService>,
   |         ------------------------------
34 |         area_service: Arc<AreaService>,
   |         ------------------------------
help: call `Into::into` on this expression to convert `user_service::UserService` into `Arc<user_service::UserService>`
   |
65 |         user_service.into(),
   |                     +++++++
help: call `Into::into` on this expression to convert `project_service::ProjectService` into `Arc<project_service::ProjectService>`
   |
66 |         project_service.into(),
   |                        +++++++
help: call `Into::into` on this expression to convert `task_service::TaskService` into `Arc<task_service::TaskService>`
   |
67 |         task_service.into(),
   |                     +++++++
help: call `Into::into` on this expression to convert `area_service::AreaService` into `Arc<area_service::AreaService>`
   |
68 |         area_service.into(),
   |                     +++++++

error[E0026]: variant `AppError::Unauthorized` does not have a field named `message`
   --> src\api\commands\mod.rs:128:61
    |
128 |             crate::shared::errors::AppError::Unauthorized { message } => {
    |                                                             ^^^^^^^
    |                                                             |
    |                                                             variant `AppError::Unauthorized` does not have this field
    |                                                             help: `AppError::Unauthorized` has a field named `action`

error[E0027]: pattern does not mention field `action`
   --> src\api\commands\mod.rs:128:13
    |
128 |             crate::shared::errors::AppError::Unauthorized { message } => {
    |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ missing field `action`
    |
help: include the missing field in the pattern
    |
128 |             crate::shared::errors::AppError::Unauthorized { message, action } => {
    |                                                                    ++++++++
help: if you don't care about this missing field, you can explicitly ignore it
    |
128 |             crate::shared::errors::AppError::Unauthorized { message, action: _ } => {
    |                                                                    +++++++++++
help: or always ignore missing fields here
    |
128 |             crate::shared::errors::AppError::Unauthorized { message, .. } => {
    |                                                                    ++++

error[E0599]: no variant named `Forbidden` found for enum `AppError`
   --> src\api\commands\mod.rs:131:46
    |
131 |             crate::shared::errors::AppError::Forbidden { message } => {
    |                                              ^^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Forbidden` not found here

error[E0599]: no variant named `NotImplemented` found for enum `AppError`
   --> src\api\commands\mod.rs:140:46
    |
140 |             crate::shared::errors::AppError::NotImplemented { message } => {
    |                                              ^^^^^^^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `NotImplemented` not found here

error[E0026]: variant `AppError::Validation` does not have a field named `field_errors`
  --> src\api\handlers\error_handler.rs:45:45
   |
45 |             AppError::Validation { message, field_errors } => {
   |                                             ^^^^^^^^^^^^ variant `AppError::Validation` does not have this field

error[E0026]: variant `AppError::NotFound` does not have a field named `message`
  --> src\api\handlers\error_handler.rs:53:34
   |
53 |             AppError::NotFound { message } => {
   |                                  ^^^^^^^
   |                                  |
   |                                  variant `AppError::NotFound` does not have this field
   |                                  help: `AppError::NotFound` has a field named `resource`

error[E0027]: pattern does not mention field `resource`
  --> src\api\handlers\error_handler.rs:53:13
   |
53 |             AppError::NotFound { message } => {
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ missing field `resource`
   |
help: include the missing field in the pattern
   |
53 |             AppError::NotFound { message, resource } => {
   |                                         ++++++++++
help: if you don't care about this missing field, you can explicitly ignore it
   |
53 |             AppError::NotFound { message, resource: _ } => {
   |                                         +++++++++++++
help: or always ignore missing fields here
   |
53 |             AppError::NotFound { message, .. } => {
   |                                         ++++

error[E0026]: variant `AppError::Unauthorized` does not have a field named `message`
  --> src\api\handlers\error_handler.rs:61:38
   |
61 |             AppError::Unauthorized { message } => {
   |                                      ^^^^^^^
   |                                      |
   |                                      variant `AppError::Unauthorized` does not have this field
   |                                      help: `AppError::Unauthorized` has a field named `action`

error[E0027]: pattern does not mention field `action`
  --> src\api\handlers\error_handler.rs:61:13
   |
61 |             AppError::Unauthorized { message } => {
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ missing field `action`
   |
help: include the missing field in the pattern
   |
61 |             AppError::Unauthorized { message, action } => {
   |                                             ++++++++
help: if you don't care about this missing field, you can explicitly ignore it
   |
61 |             AppError::Unauthorized { message, action: _ } => {
   |                                             +++++++++++
help: or always ignore missing fields here
   |
61 |             AppError::Unauthorized { message, .. } => {
   |                                             ++++

error[E0599]: no variant named `Forbidden` found for enum `AppError`
  --> src\api\handlers\error_handler.rs:69:23
   |
69 |             AppError::Forbidden { message } => {
   |                       ^^^^^^^^^ variant not found in `AppError`
   |
  ::: src\shared\errors.rs:10:1
   |
10 | pub enum AppError {
   | ----------------- variant `Forbidden` not found here

error[E0599]: no variant named `Conflict` found for enum `AppError`
  --> src\api\handlers\error_handler.rs:77:23
   |
77 |             AppError::Conflict { message } => {
   |                       ^^^^^^^^ variant not found in `AppError`
   |
  ::: src\shared\errors.rs:10:1
   |
10 | pub enum AppError {
   | ----------------- variant `Conflict` not found here

error[E0599]: no variant named `External` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:109:23
    |
109 |             AppError::External { message, source } => {
    |                       ^^^^^^^^
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `External` not found here
    |
help: there is a variant with a similar name
    |
109 -             AppError::External { message, source } => {
109 +             AppError::Internal { message, source } => {
    |

error[E0599]: no variant named `Forbidden` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:231:55
    |
231 |             AppError::Unauthorized { .. } | AppError::Forbidden { .. } => {
    |                                                       ^^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Forbidden` not found here

error[E0599]: no variant named `External` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:263:23
    |
263 |             AppError::External { .. } => {
    |                       ^^^^^^^^
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `External` not found here
    |
help: there is a variant with a similar name
    |
263 -             AppError::External { .. } => {
263 +             AppError::Internal { .. } => {
    |

error[E0599]: no variant named `Conflict` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:271:23
    |
271 |             AppError::Conflict { .. } => {
    |                       ^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Conflict` not found here

error[E0599]: no variant named `Forbidden` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:287:23
    |
287 |             AppError::Forbidden { .. } => 403,
    |                       ^^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Forbidden` not found here

error[E0599]: no variant named `Conflict` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:289:23
    |
289 |             AppError::Conflict { .. } => 409,
    |                       ^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Conflict` not found here

error[E0599]: no variant named `External` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:293:23
    |
293 |             AppError::External { .. } => 502,
    |                       ^^^^^^^^
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `External` not found here
    |
help: there is a variant with a similar name
    |
293 -             AppError::External { .. } => 502,
293 +             AppError::Internal { .. } => 502,
    |

error[E0599]: no variant named `Forbidden` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:303:25
    |
303 |             | AppError::Forbidden { .. }
    |                         ^^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Forbidden` not found here

error[E0599]: no variant named `Conflict` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:304:25
    |
304 |             | AppError::Conflict { .. } => true,
    |                         ^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Conflict` not found here

error[E0599]: no variant named `External` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:308:25
    |
308 |             | AppError::External { .. } => false,
    |                         ^^^^^^^^
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `External` not found here
    |
help: there is a variant with a similar name
    |
308 -             | AppError::External { .. } => false,
308 +             | AppError::Internal { .. } => false,
    |

error[E0599]: no variant named `Forbidden` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:355:23
    |
355 |             AppError::Forbidden { .. } => "FORBIDDEN",
    |                       ^^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Forbidden` not found here

error[E0599]: no variant named `Conflict` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:356:23
    |
356 |             AppError::Conflict { .. } => "CONFLICT",
    |                       ^^^^^^^^ variant not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `Conflict` not found here

error[E0599]: no variant named `External` found for enum `AppError`
   --> src\api\handlers\error_handler.rs:360:23
    |
360 |             AppError::External { .. } => "EXTERNAL_ERROR",
    |                       ^^^^^^^^
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant `External` not found here
    |
help: there is a variant with a similar name
    |
360 -             AppError::External { .. } => "EXTERNAL_ERROR",
360 +             AppError::Internal { .. } => "EXTERNAL_ERROR",
    |

error[E0599]: no method named `id` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:28:22
   |
28 |             id: self.id().clone(),
   |                      ^^-- help: remove the arguments
   |                      |
   |                      field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `id` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `username` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:29:28
   |
29 |             username: self.username().clone(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `username` not found for this struct

error[E0599]: no method named `email` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:30:25
   |
30 |             email: self.email().clone(),
   |                         ^^^^^-- help: remove the arguments
   |                         |
   |                         field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `email` not found for this struct

error[E0599]: no method named `full_name` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:31:29
   |
31 |             full_name: self.full_name().clone(),
   |                             ^^^^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `full_name` not found for this struct

error[E0599]: no method named `avatar_url` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:32:30
   |
32 |             avatar_url: self.avatar_url().clone(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `avatar_url` not found for this struct

error[E0599]: no method named `timezone` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:33:28
   |
33 |             timezone: self.timezone().clone(),
   |                            ^^^^^^^^ field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `timezone` not found for this struct
   |
help: remove the arguments
   |
33 -             timezone: self.timezone().clone(),
33 +             timezone: self.timezone.clone(),
   |
help: some of the expressions' fields have a method of the same name
   |
33 |             timezone: self.created_at.timezone().clone(),
   |                            +++++++++++
33 |             timezone: self.updated_at.timezone().clone(),
   |                            +++++++++++

error[E0599]: no method named `language` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:34:28
   |
34 |             language: self.language().clone(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `language` not found for this struct

error[E0599]: no method named `is_active` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:35:29
   |
35 |             is_active: self.is_active(),
   |                             ^^^^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `is_active` not found for this struct

error[E0599]: no method named `created_at` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:36:30
   |
36 |             created_at: self.created_at(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `created_at` not found for this struct

error[E0599]: no method named `updated_at` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:37:30
   |
37 |             updated_at: self.updated_at(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `updated_at` not found for this struct
   |
help: there is a method `update` with a similar name, but with different arguments
  --> src\domain\entities\user.rs:93:5
   |
93 |     pub fn update(&mut self, data: UpdateUserData) -> AppResult<()> {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `last_login_at` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:38:33
   |
38 |             last_login_at: self.last_login_at(),
   |                                 ^^^^^^^^^^^^^-- help: remove the arguments
   |                                 |
   |                                 field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `last_login_at` not found for this struct

error[E0599]: no method named `id` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:51:22
   |
51 |             id: user.id().clone(),
   |                      ^^-- help: remove the arguments
   |                      |
   |                      field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `id` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `username` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:52:28
   |
52 |             username: user.username().clone(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `username` not found for this struct

error[E0599]: no method named `email` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:53:25
   |
53 |             email: user.email().clone(),
   |                         ^^^^^-- help: remove the arguments
   |                         |
   |                         field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `email` not found for this struct

error[E0599]: no method named `full_name` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:54:29
   |
54 |             full_name: user.full_name().clone(),
   |                             ^^^^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `full_name` not found for this struct

error[E0599]: no method named `avatar_url` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:55:30
   |
55 |             avatar_url: user.avatar_url().clone(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `avatar_url` not found for this struct

error[E0599]: no method named `timezone` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:56:28
   |
56 |             timezone: user.timezone().clone(),
   |                            ^^^^^^^^ field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `timezone` not found for this struct
   |
help: remove the arguments
   |
56 -             timezone: user.timezone().clone(),
56 +             timezone: user.timezone.clone(),
   |
help: some of the expressions' fields have a method of the same name
   |
56 |             timezone: user.created_at.timezone().clone(),
   |                            +++++++++++
56 |             timezone: user.updated_at.timezone().clone(),
   |                            +++++++++++

error[E0599]: no method named `language` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:57:28
   |
57 |             language: user.language().clone(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `language` not found for this struct

error[E0599]: no method named `is_active` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:58:29
   |
58 |             is_active: user.is_active(),
   |                             ^^^^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `is_active` not found for this struct

error[E0599]: no method named `created_at` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:59:30
   |
59 |             created_at: user.created_at(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `created_at` not found for this struct

error[E0599]: no method named `updated_at` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:60:30
   |
60 |             updated_at: user.updated_at(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `updated_at` not found for this struct
   |
help: there is a method `update` with a similar name, but with different arguments
  --> src\domain\entities\user.rs:93:5
   |
93 |     pub fn update(&mut self, data: UpdateUserData) -> AppResult<()> {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `last_login_at` found for struct `user::User` in the current scope
  --> src\api\dto\response.rs:61:33
   |
61 |             last_login_at: user.last_login_at(),
   |                                 ^^^^^^^^^^^^^-- help: remove the arguments
   |                                 |
   |                                 field, not a method
   |
  ::: src\domain\entities\user.rs:11:1
   |
11 | pub struct User {
   | --------------- method `last_login_at` not found for this struct

error[E0599]: no method named `id` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:91:22
   |
91 |             id: self.id().clone(),
   |                      ^^-- help: remove the arguments
   |                      |
   |                      field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `id` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `name` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:92:24
   |
92 |             name: self.name().clone(),
   |                        ^^^^-- help: remove the arguments
   |                        |
   |                        field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `name` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `name`, perhaps you need to implement one of them:
           candidate #1: `Service`
           candidate #2: `Column`
           candidate #3: `Plugin`
           candidate #4: `TypeInfo`
           candidate #5: `sqlx_core::any::connection::backend::AnyConnectionBackend`
           candidate #6: `tauri::Resource`
           candidate #7: `tokio_rustls::rustls::crypto::SupportedKxGroup`

error[E0599]: no method named `description` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:93:31
   |
93 |             description: self.description().clone(),
   |                               ^^^^^^^^^^^-- help: remove the arguments
   |                               |
   |                               field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `description` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following trait defines an item `description`, perhaps you need to implement it:
           candidate #1: `StdError`

error[E0599]: no method named `status` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:94:26
   |
94 |             status: self.status().to_string(),
   |                          ^^^^^^-- help: remove the arguments
   |                          |
   |                          field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `status` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following trait defines an item `status`, perhaps you need to implement it:
           candidate #1: `futures_task::spawn::Spawn`

error[E0599]: no method named `progress` found for struct `Project` in the current scope
   --> src\api\dto\response.rs:95:28
    |
95  |             progress: self.progress(),
    |                            ^^^^^^^^-- help: remove the arguments
    |                            |
    |                            field, not a method
    |
   ::: src\domain\entities\project.rs:11:1
    |
11  | pub struct Project {
    | ------------------ method `progress` not found for this struct
    |
help: there is a method `update_progress` with a similar name, but with different arguments
   --> src\domain\entities\project.rs:185:5
    |
185 |     pub fn update_progress(&mut self, new_progress: u8) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `priority` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:96:28
   |
96 |             priority: self.priority().to_string(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `priority` not found for this struct

error[E0599]: no method named `start_date` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:97:30
   |
97 |             start_date: self.start_date(),
   |                              ^^^^^^^^^^ field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `start_date` not found for this struct
   |
help: remove the arguments
   |
97 -             start_date: self.start_date(),
97 +             start_date: self.start_date,
   |
help: there is a method `start` with a similar name
   |
97 -             start_date: self.start_date(),
97 +             start_date: self.start(),
   |

error[E0599]: no method named `deadline` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:98:28
   |
98 |             deadline: self.deadline(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `deadline` not found for this struct
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `deadline`, perhaps you need to implement one of them:
           candidate #1: `crossbeam_channel::select::SelectHandle`
           candidate #2: `futures_intrusive::timer::timer::LocalTimer`
           candidate #3: `futures_intrusive::timer::timer::Timer`

error[E0599]: no method named `estimated_hours` found for struct `Project` in the current scope
  --> src\api\dto\response.rs:99:35
   |
99 |             estimated_hours: self.estimated_hours(),
   |                                   ^^^^^^^^^^^^^^^-- help: remove the arguments
   |                                   |
   |                                   field, not a method
   |
  ::: src\domain\entities\project.rs:11:1
   |
11 | pub struct Project {
   | ------------------ method `estimated_hours` not found for this struct

error[E0599]: no method named `actual_hours` found for struct `Project` in the current scope
   --> src\api\dto\response.rs:100:32
    |
100 |             actual_hours: self.actual_hours(),
    |                                ^^^^^^^^^^^^-- help: remove the arguments
    |                                |
    |                                field, not a method
    |
   ::: src\domain\entities\project.rs:11:1
    |
11  | pub struct Project {
    | ------------------ method `actual_hours` not found for this struct

error[E0599]: no method named `area_id` found for struct `Project` in the current scope
   --> src\api\dto\response.rs:101:27
    |
101 |             area_id: self.area_id().clone(),
    |                           ^^^^^^^-- help: remove the arguments
    |                           |
    |                           field, not a method
    |
   ::: src\domain\entities\project.rs:11:1
    |
11  | pub struct Project {
    | ------------------ method `area_id` not found for this struct

error[E0599]: no method named `created_by` found for struct `Project` in the current scope
   --> src\api\dto\response.rs:103:30
    |
103 |             created_by: self.created_by().clone(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\project.rs:11:1
    |
11  | pub struct Project {
    | ------------------ method `created_by` not found for this struct

error[E0599]: no method named `created_at` found for struct `Project` in the current scope
   --> src\api\dto\response.rs:104:30
    |
104 |             created_at: self.created_at(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\project.rs:11:1
    |
11  | pub struct Project {
    | ------------------ method `created_at` not found for this struct

error[E0599]: no method named `updated_at` found for struct `Project` in the current scope
   --> src\api\dto\response.rs:105:30
    |
105 |             updated_at: self.updated_at(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\project.rs:11:1
    |
11  | pub struct Project {
    | ------------------ method `updated_at` not found for this struct
    |
help: there is a method `update` with a similar name, but with different arguments
   --> src\domain\entities\project.rs:105:5
    |
105 |     pub fn update(&mut self, data: UpdateProjectData) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: `ProjectStatus` doesn't implement `std::fmt::Display`
    --> src\api\dto\response.rs:122:36
     |
122  |             status: project.status.to_string(),
     |                                    ^^^^^^^^^ method cannot be called on `ProjectStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:20:1
     |
20   | pub enum ProjectStatus {
     | ---------------------- method `to_string` not found for this enum because it doesn't satisfy `ProjectStatus: ToString` or `ProjectStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `ProjectStatus: std::fmt::Display`
             which is required by `ProjectStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: `Priority` doesn't implement `std::fmt::Display`
    --> src\api\dto\response.rs:124:40
     |
124  |             priority: project.priority.to_string(),
     |                                        ^^^^^^^^^ method cannot be called on `Priority` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:83:1
     |
83   | pub enum Priority {
     | ----------------- method `to_string` not found for this enum because it doesn't satisfy `Priority: ToString` or `Priority: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `Priority: std::fmt::Display`
             which is required by `Priority: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0308]: mismatched types
   --> src\api\dto\response.rs:125:25
    |
125 |             start_date: project.start_date.map(|d| d.to_string()),
    |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Option<NaiveDate>`, found `Option<String>`
    |
    = note: expected enum `std::option::Option<NaiveDate>`
               found enum `std::option::Option<std::string::String>`

error[E0308]: mismatched types
   --> src\api\dto\response.rs:126:23
    |
126 |             deadline: project.deadline.map(|d| d.to_string()),
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Option<NaiveDate>`, found `Option<String>`
    |
    = note: expected enum `std::option::Option<NaiveDate>`
               found enum `std::option::Option<std::string::String>`

error[E0599]: no method named `id` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:170:22
    |
170 |             id: self.id().clone(),
    |                      ^^-- help: remove the arguments
    |                      |
    |                      field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `id` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `id`, perhaps you need to implement one of them:
            candidate #1: `IsMenuItem`
            candidate #2: `SpanData`
            candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `title` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:171:25
    |
171 |             title: self.title().clone(),
    |                         ^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `title` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `title`, perhaps you need to implement one of them:
            candidate #1: `tauri_runtime::WindowDispatch`
            candidate #2: `tauri_runtime::window::WindowBuilder`

error[E0599]: no method named `description` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:172:31
    |
172 |             description: self.description().clone(),
    |                               ^^^^^^^^^^^-- help: remove the arguments
    |                               |
    |                               field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `description` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `description`, perhaps you need to implement it:
            candidate #1: `StdError`

error[E0599]: no method named `status` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:173:26
    |
173 |             status: self.status().to_string(),
    |                          ^^^^^^-- help: remove the arguments
    |                          |
    |                          field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `status` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `status`, perhaps you need to implement it:
            candidate #1: `futures_task::spawn::Spawn`

error[E0599]: no method named `priority` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:174:28
    |
174 |             priority: self.priority().to_string(),
    |                            ^^^^^^^^-- help: remove the arguments
    |                            |
    |                            field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `priority` not found for this struct

error[E0599]: no method named `parent_task_id` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:175:34
    |
175 |             parent_task_id: self.parent_task_id().clone(),
    |                                  ^^^^^^^^^^^^^^-- help: remove the arguments
    |                                  |
    |                                  field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `parent_task_id` not found for this struct

error[E0599]: no method named `project_id` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:176:30
    |
176 |             project_id: self.project_id().clone(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `project_id` not found for this struct

error[E0599]: no method named `area_id` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:178:27
    |
178 |             area_id: self.area_id().clone(),
    |                           ^^^^^^^-- help: remove the arguments
    |                           |
    |                           field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `area_id` not found for this struct

error[E0599]: no method named `assigned_to` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:180:31
    |
180 |             assigned_to: self.assigned_to().clone(),
    |                               ^^^^^^^^^^^-- help: remove the arguments
    |                               |
    |                               field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `assigned_to` not found for this struct

error[E0599]: no method named `due_date` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:182:28
    |
182 |             due_date: self.due_date(),
    |                            ^^^^^^^^-- help: remove the arguments
    |                            |
    |                            field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `due_date` not found for this struct
    |
help: there is a method `update` with a similar name, but with different arguments
   --> src\domain\entities\task.rs:113:5
    |
113 |     pub fn update(&mut self, data: UpdateTaskData) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `estimated_minutes` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:183:37
    |
183 |             estimated_minutes: self.estimated_minutes(),
    |                                     ^^^^^^^^^^^^^^^^^-- help: remove the arguments
    |                                     |
    |                                     field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `estimated_minutes` not found for this struct

error[E0599]: no method named `actual_minutes` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:184:34
    |
184 |             actual_minutes: self.actual_minutes(),
    |                                  ^^^^^^^^^^^^^^-- help: remove the arguments
    |                                  |
    |                                  field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `actual_minutes` not found for this struct

error[E0599]: no method named `completion_percentage` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:185:41
    |
185 |             completion_percentage: self.completion_percentage(),
    |                                         ^^^^^^^^^^^^^^^^^^^^^-- help: remove the arguments
    |                                         |
    |                                         field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `completion_percentage` not found for this struct
    |
help: there is a method `update_completion_percentage` with a similar name, but with different arguments
   --> src\domain\entities\task.rs:216:5
    |
216 |     pub fn update_completion_percentage(&mut self, percentage: u8) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `created_by` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:186:30
    |
186 |             created_by: self.created_by().clone(),
    |                              ^^^^^^^^^^ method not found in `Task`
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `created_by` not found for this struct

error[E0599]: no method named `created_at` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:187:30
    |
187 |             created_at: self.created_at(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `created_at` not found for this struct

error[E0599]: no method named `updated_at` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:188:30
    |
188 |             updated_at: self.updated_at(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `updated_at` not found for this struct
    |
help: there is a method `update` with a similar name, but with different arguments
   --> src\domain\entities\task.rs:113:5
    |
113 |     pub fn update(&mut self, data: UpdateTaskData) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `completed_at` found for struct `Task` in the current scope
   --> src\api\dto\response.rs:189:32
    |
189 |             completed_at: self.completed_at(),
    |                                ^^^^^^^^^^^^ field, not a method
    |
   ::: src\domain\entities\task.rs:11:1
    |
11  | pub struct Task {
    | --------------- method `completed_at` not found for this struct
    |
help: remove the arguments
    |
189 -             completed_at: self.completed_at(),
189 +             completed_at: self.completed_at,
    |
help: there is a method `complete` with a similar name
    |
189 -             completed_at: self.completed_at(),
189 +             completed_at: self.complete(),
    |

error[E0599]: `TaskStatus` doesn't implement `std::fmt::Display`
    --> src\api\dto\response.rs:206:33
     |
206  |             status: task.status.to_string(),
     |                                 ^^^^^^^^^ method cannot be called on `TaskStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:53:1
     |
53   | pub enum TaskStatus {
     | ------------------- method `to_string` not found for this enum because it doesn't satisfy `TaskStatus: ToString` or `TaskStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `TaskStatus: std::fmt::Display`
             which is required by `TaskStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: `Priority` doesn't implement `std::fmt::Display`
    --> src\api\dto\response.rs:207:37
     |
207  |             priority: task.priority.to_string(),
     |                                     ^^^^^^^^^ method cannot be called on `Priority` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:83:1
     |
83   | pub enum Priority {
     | ----------------- method `to_string` not found for this enum because it doesn't satisfy `Priority: ToString` or `Priority: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `Priority: std::fmt::Display`
             which is required by `Priority: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0308]: mismatched types
   --> src\api\dto\response.rs:215:23
    |
215 |             due_date: task.due_date.map(|d| d.to_string()),
    |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Option<NaiveDate>`, found `Option<String>`
    |
    = note: expected enum `std::option::Option<NaiveDate>`
               found enum `std::option::Option<std::string::String>`

error[E0560]: struct `api::dto::response::TaskResponseDto` has no field named `sort_order`
   --> src\api\dto\response.rs:219:13
    |
219 |             sort_order: task.sort_order,
    |             ^^^^^^^^^^ `api::dto::response::TaskResponseDto` does not have this field
    |
    = note: available fields are: `created_by`

error[E0599]: no method named `id` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:248:22
    |
248 |             id: self.id().clone(),
    |                      ^^-- help: remove the arguments
    |                      |
    |                      field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `id` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `id`, perhaps you need to implement one of them:
            candidate #1: `IsMenuItem`
            candidate #2: `SpanData`
            candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `name` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:249:24
    |
249 |             name: self.name().clone(),
    |                        ^^^^-- help: remove the arguments
    |                        |
    |                        field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `name` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `name`, perhaps you need to implement one of them:
            candidate #1: `Service`
            candidate #2: `Column`
            candidate #3: `Plugin`
            candidate #4: `TypeInfo`
            candidate #5: `sqlx_core::any::connection::backend::AnyConnectionBackend`
            candidate #6: `tauri::Resource`
            candidate #7: `tokio_rustls::rustls::crypto::SupportedKxGroup`

error[E0599]: no method named `description` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:250:31
    |
250 |             description: self.description().clone(),
    |                               ^^^^^^^^^^^-- help: remove the arguments
    |                               |
    |                               field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `description` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `description`, perhaps you need to implement it:
            candidate #1: `StdError`

error[E0599]: no method named `color` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:251:25
    |
251 |             color: self.color().clone(),
    |                         ^^^^^ method not found in `Area`
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `color` not found for this struct

error[E0599]: no method named `icon` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:252:24
    |
252 |             icon: self.icon().clone(),
    |                        ^^^^ method not found in `Area`
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `icon` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `icon`, perhaps you need to implement it:
            candidate #1: `tauri_runtime::window::WindowBuilder`

error[E0599]: no method named `is_active` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:253:29
    |
253 |             is_active: self.is_active(),
    |                             ^^^^^^^^^ field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `is_active` not found for this struct
    |
help: remove the arguments
    |
253 -             is_active: self.is_active(),
253 +             is_active: self.is_active,
    |
help: there is a method `is_archived` with a similar name
    |
253 -             is_active: self.is_active(),
253 +             is_active: self.is_archived(),
    |

error[E0599]: no method named `created_by` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:254:30
    |
254 |             created_by: self.created_by().clone(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `created_by` not found for this struct

error[E0599]: no method named `created_at` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:255:30
    |
255 |             created_at: self.created_at(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `created_at` not found for this struct

error[E0599]: no method named `updated_at` found for struct `Area` in the current scope
   --> src\api\dto\response.rs:256:30
    |
256 |             updated_at: self.updated_at(),
    |                              ^^^^^^^^^^-- help: remove the arguments
    |                              |
    |                              field, not a method
    |
   ::: src\domain\entities\area.rs:11:1
    |
11  | pub struct Area {
    | --------------- method `updated_at` not found for this struct
    |
help: there is a method `update` with a similar name, but with different arguments
   --> src\domain\entities\area.rs:89:5
    |
89  |     pub fn update(&mut self, data: UpdateAreaData) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0063]: missing field `created_by` in initializer of `api::dto::response::AreaResponseDto`
   --> src\api\dto\response.rs:269:9
    |
269 |         AreaResponseDto {
    |         ^^^^^^^^^^^^^^^ missing `created_by`

error[E0609]: no field `created_by` on type `&CreateUserData`
  --> src\infrastructure\database\validators\user_validator.rs:87:57
   |
87 |             || ValidationUtils::validate_required(&data.created_by, "创建者"),
   |                                                         ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `username`, `email`, `password`, `full_name`, `timezone`, `language`

error[E0609]: no field `created_by` on type `CreateUserData`
   --> src\infrastructure\database\validators\user_validator.rs:116:14
    |
116 |         data.created_by = ValidationUtils::sanitize_string(data.created_by);
    |              ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `username`, `email`, `password`, `full_name`, `timezone`, `language`

error[E0609]: no field `created_by` on type `CreateUserData`
   --> src\infrastructure\database\validators\user_validator.rs:116:65
    |
116 |         data.created_by = ValidationUtils::sanitize_string(data.created_by);
    |                                                                 ^^^^^^^^^^ unknown field
    |
    = note: available fields are: `username`, `email`, `password`, `full_name`, `timezone`, `language`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:130:21
    |
130 |         if let Some(Some(email)) = &data.email {
    |                     ^^^^^^^^^^^    ----------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0609]: no field `password_hash` on type `&UpdateUserData`
   --> src\infrastructure\database\validators\user_validator.rs:140:44
    |
140 |         if let Some(password_hash) = &data.password_hash {
    |                                            ^^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:148:21
    |
148 |         if let Some(Some(full_name)) = &data.full_name {
    |                     ^^^^^^^^^^^^^^^    --------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:156:21
    |
156 |         if let Some(Some(avatar_url)) = &data.avatar_url {
    |                     ^^^^^^^^^^^^^^^^    ---------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:195:21
    |
195 |         if let Some(Some(email)) = data.email {
    |                     ^^^^^^^^^^^    ---------- this expression has type `std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:196:31
    |
196 |             data.email = Some(Some(ValidationUtils::normalize_email(email)));
    |                          ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                          |
    |                          arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\user_validator.rs:196:26
    |
196 |             data.email = Some(Some(ValidationUtils::normalize_email(email)));
    |                          ^^^^^---------------------------------------------^
    |                               |
    |                               this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:201:77
    |
201 |             data.full_name = Some(ValidationUtils::sanitize_optional_string(full_name));
    |                                   ----------------------------------------- ^^^^^^^^^ expected `Option<String>`, found `String`
    |                                   |
    |                                   arguments to this function are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: associated function defined here
   --> src\infrastructure\database\validators\mod.rs:179:12
    |
179 |     pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^ ---------------------
help: try wrapping the expression in `Some`
    |
201 |             data.full_name = Some(ValidationUtils::sanitize_optional_string(Some(full_name)));
    |                                                                             +++++         +

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:201:35
    |
201 |             data.full_name = Some(ValidationUtils::sanitize_optional_string(full_name));
    |                              ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                              |
    |                              arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\user_validator.rs:201:30
    |
201 |             data.full_name = Some(ValidationUtils::sanitize_optional_string(full_name));
    |                              ^^^^^----------------------------------------------------^
    |                                   |
    |                                   this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an `Option::None`
    |
201 |             data.full_name = Some(ValidationUtils::sanitize_optional_string(full_name).expect("REASON"));
    |                                                                                       +++++++++++++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:206:78
    |
206 |             data.avatar_url = Some(ValidationUtils::sanitize_optional_string(avatar_url));
    |                                    ----------------------------------------- ^^^^^^^^^^ expected `Option<String>`, found `String`
    |                                    |
    |                                    arguments to this function are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: associated function defined here
   --> src\infrastructure\database\validators\mod.rs:179:12
    |
179 |     pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^ ---------------------
help: try wrapping the expression in `Some`
    |
206 |             data.avatar_url = Some(ValidationUtils::sanitize_optional_string(Some(avatar_url)));
    |                                                                              +++++          +

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\user_validator.rs:206:36
    |
206 |             data.avatar_url = Some(ValidationUtils::sanitize_optional_string(avatar_url));
    |                               ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                               |
    |                               arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\user_validator.rs:206:31
    |
206 |             data.avatar_url = Some(ValidationUtils::sanitize_optional_string(avatar_url));
    |                               ^^^^^-----------------------------------------------------^
    |                                    |
    |                                    this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an `Option::None`
    |
206 |             data.avatar_url = Some(ValidationUtils::sanitize_optional_string(avatar_url).expect("REASON"));
    |                                                                                         +++++++++++++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\project_validator.rs:103:21
    |
103 |         if let Some(Some(description)) = &data.description {
    |                     ^^^^^^^^^^^^^^^^^    ----------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0599]: no method named `flatten` found for enum `std::option::Option<NaiveDate>` in the current scope
   --> src\infrastructure\database\validators\project_validator.rs:127:37
    |
127 |                     data.start_date.flatten(),
    |                                     ^^^^^^^ `std::option::Option<NaiveDate>` is not an iterator
    |
help: call `.into_iter()` first
    |
127 |                     data.start_date.into_iter().flatten(),
    |                                     ++++++++++++

error[E0599]: no method named `flatten` found for enum `std::option::Option<NaiveDate>` in the current scope
   --> src\infrastructure\database\validators\project_validator.rs:128:35
    |
128 |                     data.deadline.flatten(),
    |                                   ^^^^^^^ `std::option::Option<NaiveDate>` is not an iterator
    |
help: call `.into_iter()` first
    |
128 |                     data.deadline.into_iter().flatten(),
    |                                   ++++++++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\project_validator.rs:136:21
    |
136 |         if let Some(Some(estimated_hours)) = data.estimated_hours {
    |                     ^^^^^^^^^^^^^^^^^^^^^    -------------------- this expression has type `std::option::Option<u32>`
    |                     |
    |                     expected `u32`, found `Option<_>`
    |
    = note: expected type `u32`
               found enum `std::option::Option<_>`

error[E0609]: no field `actual_hours` on type `&UpdateProjectData`
   --> src\infrastructure\database\validators\project_validator.rs:149:42
    |
149 |         if let Some(actual_hours) = data.actual_hours {
    |                                          ^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `name`, `description`, `status`, `progress`, `priority` ... and 4 others

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\project_validator.rs:172:79
    |
172 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                     ----------------------------------------- ^^^^^^^^^^^ expected `Option<String>`, found `String`
    |                                     |
    |                                     arguments to this function are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: associated function defined here
   --> src\infrastructure\database\validators\mod.rs:179:12
    |
179 |     pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^ ---------------------
help: try wrapping the expression in `Some`
    |
172 |             data.description = Some(ValidationUtils::sanitize_optional_string(Some(description)));
    |                                                                               +++++           +

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\project_validator.rs:172:37
    |
172 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                                |
    |                                arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\project_validator.rs:172:32
    |
172 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                ^^^^^------------------------------------------------------^
    |                                     |
    |                                     this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an `Option::None`
    |
172 |             data.description = Some(ValidationUtils::sanitize_optional_string(description).expect("REASON"));
    |                                                                                           +++++++++++++++++

error[E0308]: mismatched types
  --> src\infrastructure\database\validators\task_validator.rs:95:21
   |
95 |         if let Some(Some(description)) = &data.description {
   |                     ^^^^^^^^^^^^^^^^^    ----------------- this expression has type `&std::option::Option<std::string::String>`
   |                     |
   |                     expected `String`, found `Option<_>`
   |
   = note: expected struct `std::string::String`
                found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\task_validator.rs:116:21
    |
116 |         if let Some(Some(estimated_minutes)) = data.estimated_minutes {
    |                     ^^^^^^^^^^^^^^^^^^^^^^^    ---------------------- this expression has type `std::option::Option<u32>`
    |                     |
    |                     expected `u32`, found `Option<_>`
    |
    = note: expected type `u32`
               found enum `std::option::Option<_>`

error[E0609]: no field `actual_minutes` on type `&UpdateTaskData`
   --> src\infrastructure\database\validators\task_validator.rs:129:44
    |
129 |         if let Some(actual_minutes) = data.actual_minutes {
    |                                            ^^^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `title`, `description`, `status`, `priority`, `parent_task_id` ... and 7 others

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\task_validator.rs:152:79
    |
152 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                     ----------------------------------------- ^^^^^^^^^^^ expected `Option<String>`, found `String`
    |                                     |
    |                                     arguments to this function are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: associated function defined here
   --> src\infrastructure\database\validators\mod.rs:179:12
    |
179 |     pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^ ---------------------
help: try wrapping the expression in `Some`
    |
152 |             data.description = Some(ValidationUtils::sanitize_optional_string(Some(description)));
    |                                                                               +++++           +

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\task_validator.rs:152:37
    |
152 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                                |
    |                                arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\task_validator.rs:152:32
    |
152 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                ^^^^^------------------------------------------------------^
    |                                     |
    |                                     this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an `Option::None`
    |
152 |             data.description = Some(ValidationUtils::sanitize_optional_string(description).expect("REASON"));
    |                                                                                           +++++++++++++++++

error[E0609]: no field `sort_order` on type `&CreateAreaData`
  --> src\infrastructure\database\validators\area_validator.rs:67:22
   |
67 |                 data.sort_order,
   |                      ^^^^^^^^^^ unknown field
   |
   = note: available fields are: `name`, `description`, `standard`, `icon_name`, `color_hex`, `created_by`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:148:21
    |
148 |         if let Some(Some(description)) = &data.description {
    |                     ^^^^^^^^^^^^^^^^^    ----------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:156:21
    |
156 |         if let Some(Some(standard)) = &data.standard {
    |                     ^^^^^^^^^^^^^^    -------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:164:21
    |
164 |         if let Some(Some(icon_name)) = &data.icon_name {
    |                     ^^^^^^^^^^^^^^^    --------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:177:21
    |
177 |         if let Some(Some(color_hex)) = &data.color_hex {
    |                     ^^^^^^^^^^^^^^^    --------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:208:79
    |
208 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                     ----------------------------------------- ^^^^^^^^^^^ expected `Option<String>`, found `String`
    |                                     |
    |                                     arguments to this function are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: associated function defined here
   --> src\infrastructure\database\validators\mod.rs:179:12
    |
179 |     pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^ ---------------------
help: try wrapping the expression in `Some`
    |
208 |             data.description = Some(ValidationUtils::sanitize_optional_string(Some(description)));
    |                                                                               +++++           +

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:208:37
    |
208 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                                |
    |                                arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\area_validator.rs:208:32
    |
208 |             data.description = Some(ValidationUtils::sanitize_optional_string(description));
    |                                ^^^^^------------------------------------------------------^
    |                                     |
    |                                     this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an `Option::None`
    |
208 |             data.description = Some(ValidationUtils::sanitize_optional_string(description).expect("REASON"));
    |                                                                                           +++++++++++++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:213:76
    |
213 |             data.standard = Some(ValidationUtils::sanitize_optional_string(standard));
    |                                  ----------------------------------------- ^^^^^^^^ expected `Option<String>`, found `String`
    |                                  |
    |                                  arguments to this function are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: associated function defined here
   --> src\infrastructure\database\validators\mod.rs:179:12
    |
179 |     pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
    |            ^^^^^^^^^^^^^^^^^^^^^^^^ ---------------------
help: try wrapping the expression in `Some`
    |
213 |             data.standard = Some(ValidationUtils::sanitize_optional_string(Some(standard)));
    |                                                                            +++++        +

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:213:34
    |
213 |             data.standard = Some(ValidationUtils::sanitize_optional_string(standard));
    |                             ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                             |
    |                             arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\area_validator.rs:213:29
    |
213 |             data.standard = Some(ValidationUtils::sanitize_optional_string(standard));
    |                             ^^^^^---------------------------------------------------^
    |                                  |
    |                                  this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^
help: consider using `Option::expect` to unwrap the `std::option::Option<std::string::String>` value, panicking if the value is an `Option::None`
    |
213 |             data.standard = Some(ValidationUtils::sanitize_optional_string(standard).expect("REASON"));
    |                                                                                     +++++++++++++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:217:21
    |
217 |         if let Some(Some(icon_name)) = data.icon_name {
    |                     ^^^^^^^^^^^^^^^    -------------- this expression has type `std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:218:35
    |
218 |             data.icon_name = Some(Some(ValidationUtils::sanitize_string(icon_name)));
    |                              ---- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `String`, found `Option<String>`
    |                              |
    |                              arguments to this enum variant are incorrect
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<std::string::String>`
help: the type constructed contains `std::option::Option<std::string::String>` due to the type of the argument passed
   --> src\infrastructure\database\validators\area_validator.rs:218:30
    |
218 |             data.icon_name = Some(Some(ValidationUtils::sanitize_string(icon_name)));
    |                              ^^^^^-------------------------------------------------^
    |                                   |
    |                                   this argument influences the type of `Some`
note: tuple variant defined here
   --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\option.rs:599:5
    |
599 |     Some(#[stable(feature = "rust1", since = "1.0.0")] T),
    |     ^^^^

error[E0308]: mismatched types
   --> src\infrastructure\database\validators\area_validator.rs:222:21
    |
222 |         if let Some(Some(color_hex)) = data.color_hex {
    |                     ^^^^^^^^^^^^^^^    -------------- this expression has type `std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
  --> src\infrastructure\database\repositories\user_repository_impl.rs:85:58
   |
85 |             Some(model) => Ok(Some(UserMapper::to_domain(&model)?)),
   |                                    --------------------- ^^^^^^ expected `UserModel`, found `&_`
   |                                    |
   |                                    arguments to this function are incorrect
   |
   = note: expected struct `user_model::UserModel`
           found reference `&_`
note: associated function defined here
  --> src\infrastructure\database\mappers\user_mapper.rs:13:12
   |
13 |     pub fn to_domain(model: UserModel) -> AppResult<User> {
   |            ^^^^^^^^^ ----------------
help: consider removing the borrow
   |
85 -             Some(model) => Ok(Some(UserMapper::to_domain(&model)?)),
85 +             Some(model) => Ok(Some(UserMapper::to_domain(model)?)),
   |

error[E0308]: mismatched types
   --> src\infrastructure\database\repositories\user_repository_impl.rs:101:58
    |
101 |             Some(model) => Ok(Some(UserMapper::to_domain(&model)?)),
    |                                    --------------------- ^^^^^^ expected `UserModel`, found `&_`
    |                                    |
    |                                    arguments to this function are incorrect
    |
    = note: expected struct `user_model::UserModel`
            found reference `&_`
note: associated function defined here
   --> src\infrastructure\database\mappers\user_mapper.rs:13:12
    |
13  |     pub fn to_domain(model: UserModel) -> AppResult<User> {
    |            ^^^^^^^^^ ----------------
help: consider removing the borrow
    |
101 -             Some(model) => Ok(Some(UserMapper::to_domain(&model)?)),
101 +             Some(model) => Ok(Some(UserMapper::to_domain(model)?)),
    |

error[E0308]: mismatched types
   --> src\infrastructure\database\repositories\user_repository_impl.rs:117:58
    |
117 |             Some(model) => Ok(Some(UserMapper::to_domain(&model)?)),
    |                                    --------------------- ^^^^^^ expected `UserModel`, found `&_`
    |                                    |
    |                                    arguments to this function are incorrect
    |
    = note: expected struct `user_model::UserModel`
            found reference `&_`
note: associated function defined here
   --> src\infrastructure\database\mappers\user_mapper.rs:13:12
    |
13  |     pub fn to_domain(model: UserModel) -> AppResult<User> {
    |            ^^^^^^^^^ ----------------
help: consider removing the borrow
    |
117 -             Some(model) => Ok(Some(UserMapper::to_domain(&model)?)),
117 +             Some(model) => Ok(Some(UserMapper::to_domain(model)?)),
    |

error[E0609]: no field `sorts` on type `QueryParams`
   --> src\infrastructure\database\repositories\user_repository_impl.rs:217:70
    |
217 |         let order_clause = DatabaseUtils::build_order_clause(&params.sorts.unwrap_or_default());
    |                                                                      ^^^^^ unknown field
    |
help: a field with a similar name exists
    |
217 -         let order_clause = DatabaseUtils::build_order_clause(&params.sorts.unwrap_or_default());
217 +         let order_clause = DatabaseUtils::build_order_clause(&params.sort.unwrap_or_default());
    |

error[E0308]: mismatched types
   --> src\infrastructure\database\repositories\user_repository_impl.rs:228:48
    |
228 |             .map(|model| UserMapper::to_domain(&model))
    |                          --------------------- ^^^^^^ expected `UserModel`, found `&UserModel`
    |                          |
    |                          arguments to this function are incorrect
    |
note: associated function defined here
   --> src\infrastructure\database\mappers\user_mapper.rs:13:12
    |
13  |     pub fn to_domain(model: UserModel) -> AppResult<User> {
    |            ^^^^^^^^^ ----------------
help: consider removing the borrow
    |
228 -             .map(|model| UserMapper::to_domain(&model))
228 +             .map(|model| UserMapper::to_domain(model))
    |

error[E0308]: mismatched types
  --> src\infrastructure\database\repositories\project_repository_impl.rs:77:61
   |
77 |             Some(model) => Ok(Some(ProjectMapper::to_domain(&model)?)),
   |                                    ------------------------ ^^^^^^ expected `ProjectModel`, found `&_`
   |                                    |
   |                                    arguments to this function are incorrect
   |
   = note: expected struct `project_model::ProjectModel`
           found reference `&_`
note: associated function defined here
  --> src\infrastructure\database\mappers\project_mapper.rs:13:12
   |
13 |     pub fn to_domain(model: ProjectModel) -> AppResult<Project> {
   |            ^^^^^^^^^ -------------------
help: consider removing the borrow
   |
77 -             Some(model) => Ok(Some(ProjectMapper::to_domain(&model)?)),
77 +             Some(model) => Ok(Some(ProjectMapper::to_domain(model)?)),
   |

error[E0599]: the method `to_string` exists for reference `&ProjectStatus`, but its trait bounds were not satisfied
    --> src\infrastructure\database\repositories\project_repository_impl.rs:146:38
     |
146  |             query_params.push(status.to_string());
     |                                      ^^^^^^^^^ method cannot be called on `&ProjectStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:20:1
     |
20   | pub enum ProjectStatus {
     | ---------------------- doesn't satisfy `ProjectStatus: ToString` or `ProjectStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `ProjectStatus: std::fmt::Display`
             which is required by `ProjectStatus: ToString`
             `&ProjectStatus: std::fmt::Display`
             which is required by `&ProjectStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: the method `to_string` exists for reference `&ProjectStatus`, but its trait bounds were not satisfied
    --> src\infrastructure\database\repositories\project_repository_impl.rs:223:38
     |
223  |             query_params.push(status.to_string());
     |                                      ^^^^^^^^^ method cannot be called on `&ProjectStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:20:1
     |
20   | pub enum ProjectStatus {
     | ---------------------- doesn't satisfy `ProjectStatus: ToString` or `ProjectStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `ProjectStatus: std::fmt::Display`
             which is required by `ProjectStatus: ToString`
             `&ProjectStatus: std::fmt::Display`
             which is required by `&ProjectStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: `ProjectStatus` doesn't implement `std::fmt::Display`
    --> src\infrastructure\database\repositories\project_repository_impl.rs:575:26
     |
575  |             .bind(status.to_string())
     |                          ^^^^^^^^^ method cannot be called on `ProjectStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:20:1
     |
20   | pub enum ProjectStatus {
     | ---------------------- method `to_string` not found for this enum because it doesn't satisfy `ProjectStatus: ToString` or `ProjectStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `ProjectStatus: std::fmt::Display`
             which is required by `ProjectStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: the method `to_string` exists for reference `&TaskStatus`, but its trait bounds were not satisfied
    --> src\infrastructure\database\repositories\task_repository_impl.rs:180:38
     |
180  |             query_params.push(status.to_string());
     |                                      ^^^^^^^^^ method cannot be called on `&TaskStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:53:1
     |
53   | pub enum TaskStatus {
     | ------------------- doesn't satisfy `TaskStatus: ToString` or `TaskStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `TaskStatus: std::fmt::Display`
             which is required by `TaskStatus: ToString`
             `&TaskStatus: std::fmt::Display`
             which is required by `&TaskStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: the method `to_string` exists for reference `&TaskStatus`, but its trait bounds were not satisfied
    --> src\infrastructure\database\repositories\task_repository_impl.rs:267:38
     |
267  |             query_params.push(status.to_string());
     |                                      ^^^^^^^^^ method cannot be called on `&TaskStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:53:1
     |
53   | pub enum TaskStatus {
     | ------------------- doesn't satisfy `TaskStatus: ToString` or `TaskStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `TaskStatus: std::fmt::Display`
             which is required by `TaskStatus: ToString`
             `&TaskStatus: std::fmt::Display`
             which is required by `&TaskStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: `TaskStatus` doesn't implement `std::fmt::Display`
    --> src\infrastructure\database\repositories\task_repository_impl.rs:878:26
     |
878  |             .bind(status.to_string())
     |                          ^^^^^^^^^ method cannot be called on `TaskStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:53:1
     |
53   | pub enum TaskStatus {
     | ------------------- method `to_string` not found for this enum because it doesn't satisfy `TaskStatus: ToString` or `TaskStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `TaskStatus: std::fmt::Display`
             which is required by `TaskStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0599]: no variant or associated item named `not_implemented` found for enum `AppError` in the current scope
   --> src\infrastructure\database\repositories\task_repository_impl.rs:924:23
    |
924 |         Err(AppError::not_implemented("任务依赖功能暂未实现"))
    |                       ^^^^^^^^^^^^^^^ variant or associated item not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant or associated item `not_implemented` not found for this enum
    |
note: if you're trying to build a new `AppError` consider using one of the following associated functions:
      AppError::database
      AppError::validation
      AppError::business_logic
      AppError::not_found
      and 4 others
   --> src\shared\errors.rs:50:5
    |
50  |     pub fn database<S: Into<String>>(message: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57  |     pub fn validation<S: Into<String>>(message: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
64  |     pub fn business_logic<S: Into<String>>(message: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
71  |     pub fn not_found<S: Into<String>>(resource: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no variant or associated item named `not_implemented` found for enum `AppError` in the current scope
   --> src\infrastructure\database\repositories\task_repository_impl.rs:929:23
    |
929 |         Err(AppError::not_implemented("任务依赖功能暂未实现"))
    |                       ^^^^^^^^^^^^^^^ variant or associated item not found in `AppError`
    |
   ::: src\shared\errors.rs:10:1
    |
10  | pub enum AppError {
    | ----------------- variant or associated item `not_implemented` not found for this enum
    |
note: if you're trying to build a new `AppError` consider using one of the following associated functions:
      AppError::database
      AppError::validation
      AppError::business_logic
      AppError::not_found
      and 4 others
   --> src\shared\errors.rs:50:5
    |
50  |     pub fn database<S: Into<String>>(message: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
57  |     pub fn validation<S: Into<String>>(message: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
64  |     pub fn business_logic<S: Into<String>>(message: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
71  |     pub fn not_found<S: Into<String>>(resource: S) -> Self {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0061]: this function takes 1 argument but 12 arguments were supplied
  --> src\infrastructure\database\mappers\user_mapper.rs:14:9
   |
14 |         User::new(
   |         ^^^^^^^^^
15 |             model.id,
   |             -------- expected `CreateUserData`, found `String`
   |
note: associated function defined here
  --> src\domain\entities\user.rs:59:12
   |
59 |     pub fn new(data: CreateUserData) -> AppResult<Self> {
   |            ^^^ --------------------
help: remove the extra arguments
   |
15 -             model.id,
15 +             /* CreateUserData */,
   |

error[E0599]: no method named `id` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:33:22
   |
33 |             id: user.id().clone(),
   |                      ^^-- help: remove the arguments
   |                      |
   |                      field, not a method
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `username` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:34:28
   |
34 |             username: user.username().clone(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method

error[E0599]: no method named `email` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:35:25
   |
35 |             email: user.email().clone(),
   |                         ^^^^^-- help: remove the arguments
   |                         |
   |                         field, not a method

error[E0599]: no method named `password_hash` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:36:33
   |
36 |             password_hash: user.password_hash().clone(),
   |                                 ^^^^^^^^^^^^^-- help: remove the arguments
   |                                 |
   |                                 field, not a method

error[E0599]: no method named `full_name` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:37:29
   |
37 |             full_name: user.full_name().clone(),
   |                             ^^^^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method

error[E0599]: no method named `avatar_url` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:38:30
   |
38 |             avatar_url: user.avatar_url().clone(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method

error[E0599]: no method named `timezone` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:39:28
   |
39 |             timezone: user.timezone().clone(),
   |                            ^^^^^^^^ field, not a method
   |
help: remove the arguments
   |
39 -             timezone: user.timezone().clone(),
39 +             timezone: user.timezone.clone(),
   |
help: some of the expressions' fields have a method of the same name
   |
39 |             timezone: user.created_at.timezone().clone(),
   |                            +++++++++++
39 |             timezone: user.updated_at.timezone().clone(),
   |                            +++++++++++

error[E0599]: no method named `language` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:40:28
   |
40 |             language: user.language().clone(),
   |                            ^^^^^^^^-- help: remove the arguments
   |                            |
   |                            field, not a method

error[E0599]: no method named `is_active` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:41:29
   |
41 |             is_active: user.is_active(),
   |                             ^^^^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method

error[E0599]: no method named `created_at` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:42:30
   |
42 |             created_at: user.created_at(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method

error[E0599]: no method named `updated_at` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:43:30
   |
43 |             updated_at: user.updated_at(),
   |                              ^^^^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method
   |
help: there is a method `update` with a similar name, but with different arguments
  --> src\domain\entities\user.rs:93:5
   |
93 |     pub fn update(&mut self, data: UpdateUserData) -> AppResult<()> {
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `last_login_at` found for reference `&user::User` in the current scope
  --> src\infrastructure\database\mappers\user_mapper.rs:44:33
   |
44 |             last_login_at: user.last_login_at(),
   |                                 ^^^^^^^^^^^^^-- help: remove the arguments
   |                                 |
   |                                 field, not a method

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:66:38
    |
66  |             model = model.with_email(email);
    |                           ---------- ^^^^^ expected `Option<String>`, found `String`
    |                           |
    |                           arguments to this method are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: method defined here
   --> src\infrastructure\database\models\user_model.rs:173:12
    |
173 |     pub fn with_email(mut self, email: Option<String>) -> Self {
    |            ^^^^^^^^^^           ---------------------
help: try wrapping the expression in `Some`
    |
66  |             model = model.with_email(Some(email));
    |                                      +++++     +

error[E0609]: no field `password_hash` on type `UpdateUserData`
  --> src\infrastructure\database\mappers\user_mapper.rs:69:43
   |
69 |         if let Some(password_hash) = data.password_hash {
   |                                           ^^^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:74:42
    |
74  |             model = model.with_full_name(full_name);
    |                           -------------- ^^^^^^^^^ expected `Option<String>`, found `String`
    |                           |
    |                           arguments to this method are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: method defined here
   --> src\infrastructure\database\models\user_model.rs:185:12
    |
185 |     pub fn with_full_name(mut self, full_name: Option<String>) -> Self {
    |            ^^^^^^^^^^^^^^           -------------------------
help: try wrapping the expression in `Some`
    |
74  |             model = model.with_full_name(Some(full_name));
    |                                          +++++         +

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:78:43
    |
78  |             model = model.with_avatar_url(avatar_url);
    |                           --------------- ^^^^^^^^^^ expected `Option<String>`, found `String`
    |                           |
    |                           arguments to this method are incorrect
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
note: method defined here
   --> src\infrastructure\database\models\user_model.rs:191:12
    |
191 |     pub fn with_avatar_url(mut self, avatar_url: Option<String>) -> Self {
    |            ^^^^^^^^^^^^^^^           --------------------------
help: try wrapping the expression in `Some`
    |
78  |             model = model.with_avatar_url(Some(avatar_url));
    |                                           +++++          +

error[E0609]: no field `is_active` on type `UpdateUserData`
  --> src\infrastructure\database\mappers\user_mapper.rs:89:39
   |
89 |         if let Some(is_active) = data.is_active {
   |                                       ^^^^^^^^^ unknown field
   |
   = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0609]: no field `last_login_at` on type `UpdateUserData`
  --> src\infrastructure\database\mappers\user_mapper.rs:93:43
   |
93 |         if let Some(last_login_at) = data.last_login_at {
   |                                           ^^^^^^^^^^^^^ unknown field
   |
   = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:209:21
    |
209 |         if let Some(Some(email)) = &data.email {
    |                     ^^^^^^^^^^^    ----------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0609]: no field `password_hash` on type `&UpdateUserData`
   --> src\infrastructure\database\mappers\user_mapper.rs:216:44
    |
216 |         if let Some(password_hash) = &data.password_hash {
    |                                            ^^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:223:21
    |
223 |         if let Some(Some(full_name)) = &data.full_name {
    |                     ^^^^^^^^^^^^^^^    --------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:263:25
    |
263 |         data.timezone = data.timezone.unwrap_or_else(|| "UTC".to_string());
    |         -------------   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Option<String>`, found `String`
    |         |
    |         expected due to the type of this binding
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
263 |         data.timezone = Some(data.timezone.unwrap_or_else(|| "UTC".to_string()));
    |                         +++++                                                  +

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:264:25
    |
264 |         data.language = data.language.unwrap_or_else(|| "en".to_string());
    |         -------------   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ expected `Option<String>`, found `String`
    |         |
    |         expected due to the type of this binding
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
264 |         data.language = Some(data.language.unwrap_or_else(|| "en".to_string()));
    |                         +++++                                                 +

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:271:21
    |
271 |         if let Some(Some(email)) = data.email {
    |                     ^^^^^^^^^^^    ---------- this expression has type `std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:275:21
    |
275 |         if let Some(Some(full_name)) = data.full_name {
    |                     ^^^^^^^^^^^^^^^    -------------- this expression has type `std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:292:21
    |
292 |         if let Some(Some(email)) = &update_data.email {
    |                     ^^^^^^^^^^^    ------------------ this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0599]: no method named `email` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:293:25
    |
293 |             if original.email().as_ref() != Some(email) {
    |                         ^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0609]: no field `password_hash` on type `&UpdateUserData`
   --> src\infrastructure\database\mappers\user_mapper.rs:298:51
    |
298 |         if let Some(password_hash) = &update_data.password_hash {
    |                                                   ^^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0599]: no method named `password_hash` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:299:25
    |
299 |             if original.password_hash() != password_hash {
    |                         ^^^^^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:304:21
    |
304 |         if let Some(Some(full_name)) = &update_data.full_name {
    |                     ^^^^^^^^^^^^^^^    ---------------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0599]: no method named `full_name` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:305:25
    |
305 |             if original.full_name().as_ref() != Some(full_name) {
    |                         ^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\user_mapper.rs:310:21
    |
310 |         if let Some(Some(avatar_url)) = &update_data.avatar_url {
    |                     ^^^^^^^^^^^^^^^^    ----------------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0599]: no method named `avatar_url` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:311:25
    |
311 |             if original.avatar_url().as_ref() != Some(avatar_url) {
    |                         ^^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0599]: no method named `timezone` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:317:25
    |
317 |             if original.timezone() != timezone {
    |                         ^^^^^^^^ field, not a method
    |
help: remove the arguments
    |
317 -             if original.timezone() != timezone {
317 +             if original.timezone != timezone {
    |
help: some of the expressions' fields have a method of the same name
    |
317 |             if original.created_at.timezone() != timezone {
    |                         +++++++++++
317 |             if original.updated_at.timezone() != timezone {
    |                         +++++++++++

error[E0599]: no method named `language` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:323:25
    |
323 |             if original.language() != language {
    |                         ^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0609]: no field `is_active` on type `&UpdateUserData`
   --> src\infrastructure\database\mappers\user_mapper.rs:328:46
    |
328 |         if let Some(is_active) = update_data.is_active {
    |                                              ^^^^^^^^^ unknown field
    |
    = note: available fields are: `email`, `full_name`, `avatar_url`, `timezone`, `language`

error[E0599]: no method named `is_active` found for reference `&user::User` in the current scope
   --> src\infrastructure\database\mappers\user_mapper.rs:329:25
    |
329 |             if original.is_active() != is_active {
    |                         ^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0061]: this function takes 1 argument but 15 arguments were supplied
  --> src\infrastructure\database\mappers\project_mapper.rs:14:9
   |
14 |         Project::new(
   |         ^^^^^^^^^^^^
15 |             model.id,
   |             -------- expected `CreateProjectData`, found `String`
   |
note: associated function defined here
  --> src\domain\entities\project.rs:72:12
   |
72 |     pub fn new(data: CreateProjectData) -> AppResult<Self> {
   |            ^^^ -----------------------
help: remove the extra arguments
   |
15 -             model.id,
15 +             /* CreateProjectData */,
   |

error[E0599]: no method named `id` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:36:25
   |
36 |             id: project.id().clone(),
   |                         ^^-- help: remove the arguments
   |                         |
   |                         field, not a method
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `id`, perhaps you need to implement one of them:
           candidate #1: `IsMenuItem`
           candidate #2: `SpanData`
           candidate #3: `muda::IsMenuItem`

error[E0599]: no method named `name` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:37:27
   |
37 |             name: project.name().clone(),
   |                           ^^^^-- help: remove the arguments
   |                           |
   |                           field, not a method
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `name`, perhaps you need to implement one of them:
           candidate #1: `Service`
           candidate #2: `Column`
           candidate #3: `Plugin`
           candidate #4: `TypeInfo`
           candidate #5: `sqlx_core::any::connection::backend::AnyConnectionBackend`
           candidate #6: `tauri::Resource`
           candidate #7: `tokio_rustls::rustls::crypto::SupportedKxGroup`

error[E0599]: no method named `description` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:38:34
   |
38 |             description: project.description().clone(),
   |                                  ^^^^^^^^^^^-- help: remove the arguments
   |                                  |
   |                                  field, not a method
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the trait `StdError` defines an item `description`, but is explicitly unimplemented

error[E0599]: no method named `status` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:39:29
   |
39 |             status: project.status().to_string(),
   |                             ^^^^^^-- help: remove the arguments
   |                             |
   |                             field, not a method
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following trait defines an item `status`, perhaps you need to implement it:
           candidate #1: `futures_task::spawn::Spawn`

error[E0599]: no method named `progress` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:40:31
    |
40  |             progress: project.progress(),
    |                               ^^^^^^^^-- help: remove the arguments
    |                               |
    |                               field, not a method
    |
help: there is a method `update_progress` with a similar name, but with different arguments
   --> src\domain\entities\project.rs:185:5
    |
185 |     pub fn update_progress(&mut self, new_progress: u8) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `priority` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:41:31
   |
41 |             priority: project.priority().to_i32(),
   |                               ^^^^^^^^-- help: remove the arguments
   |                               |
   |                               field, not a method

error[E0599]: no method named `start_date` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:42:33
   |
42 |             start_date: project.start_date(),
   |                                 ^^^^^^^^^^ field, not a method
   |
help: remove the arguments
   |
42 -             start_date: project.start_date(),
42 +             start_date: project.start_date,
   |
help: there is a method `start` with a similar name
   |
42 -             start_date: project.start_date(),
42 +             start_date: project.start(),
   |

error[E0599]: no method named `deadline` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:43:31
   |
43 |             deadline: project.deadline(),
   |                               ^^^^^^^^-- help: remove the arguments
   |                               |
   |                               field, not a method
   |
   = help: items from traits can only be used if the trait is implemented and in scope
   = note: the following traits define an item `deadline`, perhaps you need to implement one of them:
           candidate #1: `crossbeam_channel::select::SelectHandle`
           candidate #2: `futures_intrusive::timer::timer::LocalTimer`
           candidate #3: `futures_intrusive::timer::timer::Timer`

error[E0599]: no method named `estimated_hours` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:44:38
   |
44 |             estimated_hours: project.estimated_hours(),
   |                                      ^^^^^^^^^^^^^^^-- help: remove the arguments
   |                                      |
   |                                      field, not a method

error[E0599]: no method named `actual_hours` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:45:35
   |
45 |             actual_hours: project.actual_hours(),
   |                                   ^^^^^^^^^^^^-- help: remove the arguments
   |                                   |
   |                                   field, not a method

error[E0599]: no method named `area_id` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:46:30
   |
46 |             area_id: project.area_id().clone(),
   |                              ^^^^^^^-- help: remove the arguments
   |                              |
   |                              field, not a method

error[E0599]: no method named `created_by` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:47:33
   |
47 |             created_by: project.created_by().clone(),
   |                                 ^^^^^^^^^^-- help: remove the arguments
   |                                 |
   |                                 field, not a method

error[E0599]: no method named `created_at` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:48:33
   |
48 |             created_at: project.created_at(),
   |                                 ^^^^^^^^^^-- help: remove the arguments
   |                                 |
   |                                 field, not a method

error[E0599]: no method named `updated_at` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:49:33
    |
49  |             updated_at: project.updated_at(),
    |                                 ^^^^^^^^^^-- help: remove the arguments
    |                                 |
    |                                 field, not a method
    |
help: there is a method `update` with a similar name, but with different arguments
   --> src\domain\entities\project.rs:105:5
    |
105 |     pub fn update(&mut self, data: UpdateProjectData) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `archived_at` found for reference `&Project` in the current scope
  --> src\infrastructure\database\mappers\project_mapper.rs:50:34
   |
50 |             archived_at: project.archived_at(),
   |                                  ^^^^^^^^^^^ field, not a method
   |
help: remove the arguments
   |
50 -             archived_at: project.archived_at(),
50 +             archived_at: project.archived_at,
   |
help: there is a method `archive` with a similar name
   |
50 -             archived_at: project.archived_at(),
50 +             archived_at: project.archive(),
   |

error[E0599]: no method named `to_i32` found for enum `std::option::Option` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:60:27
    |
60  |             data.priority.to_i32(),
    |                           ^^^^^^ method not found in `std::option::Option<Priority>`
    |
note: the method `to_i32` exists on the type `Priority`
   --> src\shared\types.rs:113:5
    |
113 |     pub fn to_i32(&self) -> i32 {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^
help: consider using `Option::expect` to unwrap the `Priority` value, panicking if the value is an `Option::None`
    |
60  |             data.priority.expect("REASON").to_i32(),
    |                          +++++++++++++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:74:48
    |
74  |             model = model.with_estimated_hours(estimated_hours);
    |                           -------------------- ^^^^^^^^^^^^^^^ expected `i32`, found `u32`
    |                           |
    |                           arguments to this method are incorrect
    |
note: method defined here
   --> src\infrastructure\database\models\project_model.rs:251:12
    |
251 |     pub fn with_estimated_hours(mut self, hours: i32) -> Self {
    |            ^^^^^^^^^^^^^^^^^^^^           ----------
help: you can convert a `u32` to an `i32` and panic if the converted value doesn't fit
    |
74  |             model = model.with_estimated_hours(estimated_hours.try_into().unwrap());
    |                                                               ++++++++++++++++++++

error[E0308]: mismatched types
  --> src\infrastructure\database\mappers\project_mapper.rs:89:33
   |
89 |             model.description = description;
   |             -----------------   ^^^^^^^^^^^ expected `Option<String>`, found `String`
   |             |
   |             expected due to the type of this binding
   |
   = note: expected enum `std::option::Option<std::string::String>`
            found struct `std::string::String`
help: try wrapping the expression in `Some`
   |
89 |             model.description = Some(description);
   |                                 +++++           +

error[E0599]: `ProjectStatus` doesn't implement `std::fmt::Display`
    --> src\infrastructure\database\mappers\project_mapper.rs:93:46
     |
93   |             model = model.with_status(status.to_string());
     |                                              ^^^^^^^^^ method cannot be called on `ProjectStatus` due to unsatisfied trait bounds
     |
    ::: src\shared\types.rs:20:1
     |
20   | pub enum ProjectStatus {
     | ---------------------- method `to_string` not found for this enum because it doesn't satisfy `ProjectStatus: ToString` or `ProjectStatus: std::fmt::Display`
     |
     = note: the following trait bounds were not satisfied:
             `ProjectStatus: std::fmt::Display`
             which is required by `ProjectStatus: ToString`
note: the trait `std::fmt::Display` must be implemented
    --> C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib/rustlib/src/rust\library\core\src\fmt\mod.rs:1003:1
     |
1003 | pub trait Display: PointeeSized {
     | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
     = help: items from traits can only be used if the trait is implemented and in scope
     = note: the following trait defines an item `to_string`, perhaps you need to implement it:
             candidate #1: `ToString`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:97:41
    |
97  |             model = model.with_progress(progress);
    |                           ------------- ^^^^^^^^ expected `i32`, found `u8`
    |                           |
    |                           arguments to this method are incorrect
    |
note: method defined here
   --> src\infrastructure\database\models\project_model.rs:288:12
    |
288 |     pub fn with_progress(mut self, progress: i32) -> Self {
    |            ^^^^^^^^^^^^^           -------------
help: you can convert a `u8` to an `i32`
    |
97  |             model = model.with_progress(progress.into());
    |                                                 +++++++

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:105:32
    |
105 |             model.start_date = start_date;
    |             ----------------   ^^^^^^^^^^ expected `Option<NaiveDate>`, found `NaiveDate`
    |             |
    |             expected due to the type of this binding
    |
    = note: expected enum `std::option::Option<NaiveDate>`
             found struct `NaiveDate`
help: try wrapping the expression in `Some`
    |
105 |             model.start_date = Some(start_date);
    |                                +++++          +

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:109:30
    |
109 |             model.deadline = deadline;
    |             --------------   ^^^^^^^^ expected `Option<NaiveDate>`, found `NaiveDate`
    |             |
    |             expected due to the type of this binding
    |
    = note: expected enum `std::option::Option<NaiveDate>`
             found struct `NaiveDate`
help: try wrapping the expression in `Some`
    |
109 |             model.deadline = Some(deadline);
    |                              +++++        +

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:113:37
    |
113 |             model.estimated_hours = estimated_hours;
    |             ---------------------   ^^^^^^^^^^^^^^^ expected `Option<i32>`, found `u32`
    |             |
    |             expected due to the type of this binding
    |
    = note: expected enum `std::option::Option<i32>`
               found type `u32`

error[E0609]: no field `actual_hours` on type `UpdateProjectData`
   --> src\infrastructure\database\mappers\project_mapper.rs:116:42
    |
116 |         if let Some(actual_hours) = data.actual_hours {
    |                                          ^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `name`, `description`, `status`, `progress`, `priority` ... and 4 others

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:121:29
    |
121 |             model.area_id = area_id;
    |             -------------   ^^^^^^^ expected `Option<String>`, found `String`
    |             |
    |             expected due to the type of this binding
    |
    = note: expected enum `std::option::Option<std::string::String>`
             found struct `std::string::String`
help: try wrapping the expression in `Some`
    |
121 |             model.area_id = Some(area_id);
    |                             +++++       +

error[E0609]: no field `archived` on type `UpdateProjectData`
   --> src\infrastructure\database\mappers\project_mapper.rs:124:17
    |
124 |         if data.archived {
    |                 ^^^^^^^^ unknown field
    |
    = note: available fields are: `name`, `description`, `status`, `progress`, `priority` ... and 4 others

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:257:21
    |
257 |         if let Some(Some(description)) = &data.description {
    |                     ^^^^^^^^^^^^^^^^^    ----------------- this expression has type `&std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:271:21
    |
271 |         if let Some(Some(estimated_hours)) = data.estimated_hours {
    |                     ^^^^^^^^^^^^^^^^^^^^^    -------------------- this expression has type `std::option::Option<u32>`
    |                     |
    |                     expected `u32`, found `Option<_>`
    |
    = note: expected type `u32`
               found enum `std::option::Option<_>`

error[E0609]: no field `actual_hours` on type `&UpdateProjectData`
   --> src\infrastructure\database\mappers\project_mapper.rs:277:42
    |
277 |         if let Some(actual_hours) = data.actual_hours {
    |                                          ^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `name`, `description`, `status`, `progress`, `priority` ... and 4 others

error[E0308]: mismatched types
   --> src\infrastructure\database\mappers\project_mapper.rs:305:21
    |
305 |         if let Some(Some(description)) = data.description {
    |                     ^^^^^^^^^^^^^^^^^    ---------------- this expression has type `std::option::Option<std::string::String>`
    |                     |
    |                     expected `String`, found `Option<_>`
    |
    = note: expected struct `std::string::String`
                 found enum `std::option::Option<_>`

error[E0599]: no method named `name` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:315:25
    |
315 |             if original.name() != name {
    |                         ^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `name`, perhaps you need to implement one of them:
            candidate #1: `Service`
            candidate #2: `Column`
            candidate #3: `Plugin`
            candidate #4: `TypeInfo`
            candidate #5: `sqlx_core::any::connection::backend::AnyConnectionBackend`
            candidate #6: `tauri::Resource`
            candidate #7: `tokio_rustls::rustls::crypto::SupportedKxGroup`

error[E0599]: no method named `description` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:321:25
    |
321 |             if original.description() != description {
    |                         ^^^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the trait `StdError` defines an item `description`, but is explicitly unimplemented

error[E0599]: no method named `status` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:327:25
    |
327 |             if original.status() != status {
    |                         ^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following trait defines an item `status`, perhaps you need to implement it:
            candidate #1: `futures_task::spawn::Spawn`

error[E0599]: no method named `progress` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:333:25
    |
333 |             if original.progress() != progress {
    |                         ^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method
    |
help: there is a method `update_progress` with a similar name, but with different arguments
   --> src\domain\entities\project.rs:185:5
    |
185 |     pub fn update_progress(&mut self, new_progress: u8) -> AppResult<()> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no method named `priority` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:339:25
    |
339 |             if original.priority() != priority {
    |                         ^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0599]: no method named `start_date` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:345:25
    |
345 |             if original.start_date() != start_date {
    |                         ^^^^^^^^^^ field, not a method
    |
help: remove the arguments
    |
345 -             if original.start_date() != start_date {
345 +             if original.start_date != start_date {
    |
help: there is a method `start` with a similar name
    |
345 -             if original.start_date() != start_date {
345 +             if original.start() != start_date {
    |

error[E0599]: no method named `deadline` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:351:25
    |
351 |             if original.deadline() != deadline {
    |                         ^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `deadline`, perhaps you need to implement one of them:
            candidate #1: `crossbeam_channel::select::SelectHandle`
            candidate #2: `futures_intrusive::timer::timer::LocalTimer`
            candidate #3: `futures_intrusive::timer::timer::Timer`

error[E0599]: no method named `estimated_hours` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:357:25
    |
357 |             if original.estimated_hours() != estimated_hours {
    |                         ^^^^^^^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0609]: no field `actual_hours` on type `&UpdateProjectData`
   --> src\infrastructure\database\mappers\project_mapper.rs:362:49
    |
362 |         if let Some(actual_hours) = update_data.actual_hours {
    |                                                 ^^^^^^^^^^^^ unknown field
    |
    = note: available fields are: `name`, `description`, `status`, `progress`, `priority` ... and 4 others

error[E0599]: no method named `actual_hours` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:363:25
    |
363 |             if original.actual_hours() != actual_hours {
    |                         ^^^^^^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0599]: no method named `area_id` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:369:25
    |
369 |             if original.area_id() != area_id {
    |                         ^^^^^^^-- help: remove the arguments
    |                         |
    |                         field, not a method

error[E0609]: no field `archived` on type `&UpdateProjectData`
   --> src\infrastructure\database\mappers\project_mapper.rs:374:24
    |
374 |         if update_data.archived && original.archived_at().is_none() {
    |                        ^^^^^^^^ unknown field
    |
    = note: available fields are: `name`, `description`, `status`, `progress`, `priority` ... and 4 others

error[E0599]: no method named `archived_at` found for reference `&Project` in the current scope
   --> src\infrastructure\database\mappers\project_mapper.rs:374:45
    |
374 |         if update_data.archived && original.archived_at().is_none() {
    |                                             ^^^^^^^^^^^ field, not a method
    |
help: remove the arguments
    |
374 -         if update_data.archived && original.archived_at().is_none() {
374 +         if update_data.archived && original.archived_at.is_none() {
    |
help: there is a method `archive` with a similar name
    |
374 -         if update_data.archived && original.archived_at().is_none() {
374 +         if update_data.archived && original.archive().is_none() {
    |

error[E0308]: mismatched types
   --> src\infrastructure\config\database.rs:155:19
    |
155 |             idle: pool.num_idle(),
    |                   ^^^^^^^^^^^^^^^ expected `u32`, found `usize`

error[E0599]: the method `async_kind` exists for reference `&impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>>`, but its trait bounds were not satisfied
   --> src\api\commands\user_commands.rs:151:1
    |
151 |   #[command]
    |   ^^^^^^^^^^ method cannot be called due to unsatisfied trait bounds
    |
   ::: src\lib.rs:118:25
    |
118 |           .invoke_handler(tauri::generate_handler![
    |  _________________________-
119 | |             // 基础命令
120 | |             greet,
121 | |             health_check,
...   |
165 | |             api::commands::area_commands::habit_get_streak
166 | |         ])
    | |_________- in this macro invocation
    |
    = note: the following trait bounds were not satisfied:
            `impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>>: IpcResponse`
            which is required by `&impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>>: tauri::ipc::private::ResponseKind`
            `<&impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>> as std::future::Future>::Output = Result<_, _>`
            which is required by `&impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>>: tauri::ipc::private::ResultFutureKind`
            `&impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>>: std::future::Future`
            which is required by `&impl std::future::Future<Output = Result<PagedResult<UserResponse>, std::string::String>>: tauri::ipc::private::ResultFutureKind`
    = note: this error originates in the macro `api::commands::user_commands::__cmd__list_users` which comes from the expansion of the macro `tauri::generate_handler` (in Nightly builds, run with -Z macro-backtrace for more info)

warning: unused import: `Mapper`
 --> src\infrastructure\database\repositories\user_repository_impl.rs:7:60
  |
7 | use crate::infrastructure::database::mappers::{UserMapper, Mapper};
  |                                                            ^^^^^^

warning: unused import: `Mapper`
 --> src\infrastructure\database\repositories\project_repository_impl.rs:7:63
  |
7 | use crate::infrastructure::database::mappers::{ProjectMapper, Mapper};
  |                                                               ^^^^^^

warning: unused import: `Row`
 --> src\infrastructure\database\mod.rs:5:24
  |
5 | use sqlx::{SqlitePool, Row};
  |                        ^^^

warning: unused variable: `required_permission`
   --> src\api\common.rs:185:9
    |
185 |         required_permission: &str,
    |         ^^^^^^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_required_permission`

warning: unused variable: `parent_id`
   --> src\infrastructure\database\validators\task_validator.rs:233:22
    |
233 |         if let (Some(parent_id), Some(task_project), Some(parent_project)) =
    |                      ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_parent_id`

warning: unused variable: `username`
   --> src\infrastructure\database\repositories\user_repository_impl.rs:238:21
    |
238 |         if let Some(username) = &query.username {
    |                     ^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_username`

warning: unused variable: `email`
   --> src\infrastructure\database\repositories\user_repository_impl.rs:242:21
    |
242 |         if let Some(email) = &query.email {
    |                     ^^^^^ help: if this is intentional, prefix it with an underscore: `_email`

warning: unused variable: `is_active`
   --> src\infrastructure\database\repositories\user_repository_impl.rs:246:21
    |
246 |         if let Some(is_active) = query.is_active {
    |                     ^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_is_active`

warning: unused variable: `created_after`
   --> src\infrastructure\database\repositories\user_repository_impl.rs:250:21
    |
250 |         if let Some(created_after) = query.created_after {
    |                     ^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_created_after`

warning: unused variable: `created_before`
   --> src\infrastructure\database\repositories\user_repository_impl.rs:254:21
    |
254 |         if let Some(created_before) = query.created_before {
    |                     ^^^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_created_before`

Some errors have detailed explanations: E0026, E0027, E0053, E0061, E0063, E0106, E0277, E0282, E0308...
For more information about an error, try `rustc --explain E0026`.
warning: `paolife` (lib) generated 90 warnings
error: could not compile `paolife` (lib) due to 465 previous errors; 90 warnings emitted