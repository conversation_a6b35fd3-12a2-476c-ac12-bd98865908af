// API工具函数
// 提供与后端Tauri命令交互的统一接口

import { invoke } from '@tauri-apps/api/core';
import toast from 'solid-toast';

// 通用API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  request_id?: string;
}

// 分页请求参数
export interface PaginationRequest {
  page?: number;
  page_size?: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

// API错误类
export class ApiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// API客户端类
export class ApiClient {
  private static instance: ApiClient;
  private token: string | null = null;

  private constructor() {
    this.token = localStorage.getItem('auth_token');
  }

  static getInstance(): ApiClient {
    if (!ApiClient.instance) {
      ApiClient.instance = new ApiClient();
    }
    return ApiClient.instance;
  }

  // 设置认证令牌
  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  // 获取当前用户ID
  getCurrentUserId(): string | null {
    const userStr = localStorage.getItem('auth_user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        return user.id;
      } catch {
        return null;
      }
    }
    return null;
  }

  // 通用API调用方法
  async call<T>(
    command: string,
    params: Record<string, any> = {},
    showErrorToast = true
  ): Promise<T> {
    try {
      // 添加当前用户ID到参数中
      const currentUserId = this.getCurrentUserId();
      const finalParams = {
        ...params,
        current_user_id: currentUserId,
      };

      const response = await invoke<ApiResponse<T>>(command, finalParams);

      if (response.success) {
        return response.data as T;
      } else {
        const error = new ApiError(
          response.error || '操作失败',
          'API_ERROR',
          response
        );
        
        if (showErrorToast) {
          toast.error(error.message);
        }
        
        throw error;
      }
    } catch (error) {
      console.error(`API call failed: ${command}`, error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      const apiError = new ApiError(
        '网络错误，请稍后重试',
        'NETWORK_ERROR',
        error
      );
      
      if (showErrorToast) {
        toast.error(apiError.message);
      }
      
      throw apiError;
    }
  }

  // 认证相关API
  async login(credentials: { username: string; password: string }) {
    return this.call('login', { request: credentials });
  }

  async register(userData: {
    username: string;
    email?: string;
    password: string;
    full_name?: string;
    timezone?: string;
    language?: string;
  }) {
    return this.call('register', { request: userData });
  }

  async logout() {
    return this.call('logout', {});
  }

  async getCurrentUser() {
    return this.call('get_current_user', {});
  }

  async validateToken(token: string) {
    return this.call('validate_token', { token }, false);
  }

  async changePassword(oldPassword: string, newPassword: string) {
    return this.call('change_password', {
      old_password: oldPassword,
      new_password: newPassword,
    });
  }

  // 项目相关API
  async createProject(projectData: {
    name: string;
    description?: string;
    priority?: string;
    start_date?: string;
    deadline?: string;
    estimated_hours?: number;
    area_id?: string;
  }) {
    return this.call('project_create', { request: projectData });
  }

  async getProject(projectId: string) {
    return this.call('project_get', { project_id: projectId });
  }

  async updateProject(projectId: string, projectData: any) {
    return this.call('project_update', {
      project_id: projectId,
      request: projectData,
    });
  }

  async deleteProject(projectId: string) {
    return this.call('project_delete', { project_id: projectId });
  }

  async listProjects(pagination: PaginationRequest = {}) {
    return this.call<any[]>('project_list', {
      pagination: {
        page: pagination.page || 1,
        page_size: pagination.page_size || 20,
      },
    });
  }

  async updateProjectStatus(projectId: string, status: string) {
    return this.call('project_update_status', {
      project_id: projectId,
      status,
    });
  }

  async getProjectStats() {
    return this.call('project_get_stats', {});
  }

  async archiveProject(projectId: string) {
    return this.call('project_archive', { project_id: projectId });
  }

  async searchProjects(keyword: string, pagination: PaginationRequest = {}) {
    return this.call<any[]>('project_search', {
      search_params: {
        keyword,
        pagination: {
          page: pagination.page || 1,
          page_size: pagination.page_size || 20,
        },
      },
    });
  }

  // 任务相关API
  async createTask(taskData: {
    title: string;
    description?: string;
    priority?: string;
    parent_task_id?: string;
    project_id?: string;
    area_id?: string;
    assigned_to?: string;
    due_date?: string;
    estimated_minutes?: number;
  }) {
    return this.call('task_create', { request: taskData });
  }

  async getTask(taskId: string) {
    return this.call('task_get', { task_id: taskId });
  }

  async updateTask(taskId: string, taskData: any) {
    return this.call('task_update', {
      task_id: taskId,
      request: taskData,
    });
  }

  async deleteTask(taskId: string) {
    return this.call('task_delete', { task_id: taskId });
  }

  async listTasks(pagination: PaginationRequest = {}) {
    return this.call<any[]>('task_list', {
      pagination: {
        page: pagination.page || 1,
        page_size: pagination.page_size || 20,
      },
    });
  }

  async updateTaskStatus(taskId: string, status: string) {
    return this.call('task_update_status', {
      task_id: taskId,
      status,
    });
  }

  async completeTask(taskId: string) {
    return this.call('task_complete', { task_id: taskId });
  }

  async getTaskStats() {
    return this.call('task_get_stats', {});
  }

  async searchTasks(keyword: string, pagination: PaginationRequest = {}) {
    return this.call<any[]>('task_search', {
      search_params: {
        keyword,
        pagination: {
          page: pagination.page || 1,
          page_size: pagination.page_size || 20,
        },
      },
    });
  }

  // 领域相关API
  async createArea(areaData: {
    name: string;
    description?: string;
    color?: string;
    icon?: string;
  }) {
    return this.call('area_create', { request: areaData });
  }

  async getArea(areaId: string) {
    return this.call('area_get', { area_id: areaId });
  }

  async updateArea(areaId: string, areaData: any) {
    return this.call('area_update', {
      area_id: areaId,
      request: areaData,
    });
  }

  async deleteArea(areaId: string) {
    return this.call('area_delete', { area_id: areaId });
  }

  async listAreas(pagination: PaginationRequest = {}) {
    return this.call<any[]>('area_list', {
      pagination: {
        page: pagination.page || 1,
        page_size: pagination.page_size || 20,
      },
    });
  }

  async searchAreas(keyword: string, pagination: PaginationRequest = {}) {
    return this.call<any[]>('area_search', {
      search_params: {
        keyword,
        pagination: {
          page: pagination.page || 1,
          page_size: pagination.page_size || 20,
        },
      },
    });
  }

  // 习惯相关API
  async recordHabit(areaId: string, habitName: string) {
    return this.call('habit_record', {
      area_id: areaId,
      habit_name: habitName,
    });
  }

  async getHabitStreak(areaId: string, habitName: string) {
    return this.call<number>('habit_get_streak', {
      area_id: areaId,
      habit_name: habitName,
    });
  }
}

// 导出单例实例
export const api = ApiClient.getInstance();

// 便捷的API调用函数
export const callApi = <T>(
  command: string,
  params: Record<string, any> = {},
  showErrorToast = true
): Promise<T> => {
  return api.call<T>(command, params, showErrorToast);
};

// 错误处理工具
export const handleApiError = (error: unknown, defaultMessage = '操作失败') => {
  if (error instanceof ApiError) {
    return error.message;
  }
  
  console.error('Unexpected error:', error);
  return defaultMessage;
};

// 重试工具
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  let lastError: unknown;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }
  
  throw lastError;
};
