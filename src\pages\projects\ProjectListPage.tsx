// 项目列表页面
// 显示用户的所有项目，支持筛选、搜索和排序

import { Component, createSignal, createEffect, Show, For } from 'solid-js';
import { useNavigate, useSearchParams } from '@solidjs/router';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  progress: number;
  priority: string;
  start_date?: string;
  deadline?: string;
  estimated_hours?: number;
  actual_hours: number;
  area_id?: string;
  area_name?: string;
  created_at: string;
  updated_at: string;
  task_count?: number;
  completed_task_count?: number;
}

export const ProjectListPage: Component = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [projects, setProjects] = createSignal<Project[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [searchQuery, setSearchQuery] = createSignal(searchParams.search || '');
  const [statusFilter, setStatusFilter] = createSignal(searchParams.status || 'all');
  const [sortBy, setSortBy] = createSignal(searchParams.sort || 'updated_at');
  const [sortOrder, setSortOrder] = createSignal(searchParams.order || 'desc');
  const [currentPage, setCurrentPage] = createSignal(parseInt(searchParams.page || '1'));
  const [totalPages, setTotalPages] = createSignal(1);

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setLoading(true);

      let response;
      if (searchQuery()) {
        // 如果有搜索查询，使用搜索API
        response = await api.searchProjects(searchQuery(), {
          page: currentPage(),
          page_size: 20,
        });
      } else {
        // 否则使用列表API
        response = await api.listProjects({
          page: currentPage(),
          page_size: 20,
        });
      }

      // 新的API返回数组而不是分页对象
      if (Array.isArray(response)) {
        setProjects(response);
        setTotalPages(Math.ceil(response.length / 20)); // 简单计算，实际应该从后端获取总数
      } else {
        setProjects([]);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
      toast.error('加载项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和参数变化时重新加载
  createEffect(() => {
    loadProjects();
  });

  // 更新URL参数
  const updateSearchParams = () => {
    const params: Record<string, string> = {};
    if (searchQuery()) params.search = searchQuery();
    if (statusFilter() !== 'all') params.status = statusFilter();
    if (sortBy() !== 'updated_at') params.sort = sortBy();
    if (sortOrder() !== 'desc') params.order = sortOrder();
    if (currentPage() !== 1) params.page = currentPage().toString();
    
    setSearchParams(params);
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
    updateSearchParams();
  };

  // 处理筛选
  const handleFilter = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1);
    updateSearchParams();
  };

  // 处理排序
  const handleSort = (field: string) => {
    if (sortBy() === field) {
      setSortOrder(sortOrder() === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
    updateSearchParams();
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <MainLayout title="项目管理">
      <div class="space-y-6">
        {/* 页面头部 */}
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">项目列表</h1>
            <p class="mt-2 text-sm text-gray-700">
              管理您的所有项目，跟踪进度和状态
            </p>
          </div>
          <div class="mt-4 sm:mt-0">
            <Button onClick={() => navigate('/projects/new')}>
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              新建项目
            </Button>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex flex-col sm:flex-row gap-4">
            {/* 搜索框 */}
            <div class="flex-1">
              <Input
                type="search"
                placeholder="搜索项目名称或描述..."
                value={searchQuery()}
                onInput={(e) => handleSearch(e.currentTarget.value)}
                leftIcon={() => (
                  <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              />
            </div>

            {/* 状态筛选 */}
            <div class="flex gap-2">
              <button
                class={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  statusFilter() === 'all'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => handleFilter('all')}
              >
                全部
              </button>
              <button
                class={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  statusFilter() === 'active'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => handleFilter('active')}
              >
                进行中
              </button>
              <button
                class={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  statusFilter() === 'completed'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => handleFilter('completed')}
              >
                已完成
              </button>
            </div>
          </div>
        </div>

        {/* 项目列表 */}
        <div class="bg-white shadow rounded-lg">
          <Show
            when={!loading()}
            fallback={
              <div class="p-8 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-2 text-gray-500">加载中...</p>
              </div>
            }
          >
            <Show
              when={projects().length > 0}
              fallback={
                <div class="p-8 text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900">暂无项目</h3>
                  <p class="mt-1 text-sm text-gray-500">开始创建您的第一个项目吧</p>
                  <div class="mt-6">
                    <Button onClick={() => navigate('/projects/new')}>
                      新建项目
                    </Button>
                  </div>
                </div>
              }
            >
              <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('name')}
                      >
                        项目名称
                        <Show when={sortBy() === 'name'}>
                          <span class="ml-1">
                            {sortOrder() === 'asc' ? '↑' : '↓'}
                          </span>
                        </Show>
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        进度
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        优先级
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort('deadline')}
                      >
                        截止日期
                        <Show when={sortBy() === 'deadline'}>
                          <span class="ml-1">
                            {sortOrder() === 'asc' ? '↑' : '↓'}
                          </span>
                        </Show>
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        任务
                      </th>
                      <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">操作</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <For each={projects()}>
                      {(project) => (
                        <tr class="hover:bg-gray-50">
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div>
                                <div class="text-sm font-medium text-gray-900">
                                  <button
                                    type="button"
                                    class="hover:text-blue-600"
                                    onClick={() => navigate(`/projects/${project.id}`)}
                                  >
                                    {project.name}
                                  </button>
                                </div>
                                <Show when={project.description}>
                                  <div class="text-sm text-gray-500 truncate max-w-xs">
                                    {project.description}
                                  </div>
                                </Show>
                                <Show when={project.area_name}>
                                  <div class="text-xs text-blue-600">
                                    {project.area_name}
                                  </div>
                                </Show>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.status)}`}>
                              {project.status === 'active' ? '进行中' :
                               project.status === 'completed' ? '已完成' :
                               project.status === 'on_hold' ? '暂停' :
                               project.status === 'cancelled' ? '已取消' : project.status}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div
                                  class="bg-blue-600 h-2 rounded-full"
                                  style={{ width: `${project.progress}%` }}
                                />
                              </div>
                              <span class="text-sm text-gray-900">{project.progress}%</span>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(project.priority)}`}>
                              {project.priority === 'urgent' ? '紧急' :
                               project.priority === 'high' ? '高' :
                               project.priority === 'medium' ? '中' :
                               project.priority === 'low' ? '低' : project.priority}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {formatDate(project.deadline)}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <Show
                              when={project.task_count !== undefined}
                              fallback="-"
                            >
                              {project.completed_task_count || 0} / {project.task_count || 0}
                            </Show>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => navigate(`/projects/${project.id}`)}
                              >
                                查看
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => navigate(`/projects/${project.id}/edit`)}
                              >
                                编辑
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )}
                    </For>
                  </tbody>
                </table>
              </div>

              {/* 分页 */}
              <Show when={totalPages() > 1}>
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div class="flex-1 flex justify-between sm:hidden">
                    <Button
                      variant="outline"
                      disabled={currentPage() === 1}
                      onClick={() => {
                        setCurrentPage(currentPage() - 1);
                        updateSearchParams();
                      }}
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      disabled={currentPage() === totalPages()}
                      onClick={() => {
                        setCurrentPage(currentPage() + 1);
                        updateSearchParams();
                      }}
                    >
                      下一页
                    </Button>
                  </div>
                  <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p class="text-sm text-gray-700">
                        第 <span class="font-medium">{currentPage()}</span> 页，共{' '}
                        <span class="font-medium">{totalPages()}</span> 页
                      </p>
                    </div>
                    <div>
                      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={currentPage() === 1}
                          onClick={() => {
                            setCurrentPage(currentPage() - 1);
                            updateSearchParams();
                          }}
                        >
                          上一页
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={currentPage() === totalPages()}
                          onClick={() => {
                            setCurrentPage(currentPage() + 1);
                            updateSearchParams();
                          }}
                        >
                          下一页
                        </Button>
                      </nav>
                    </div>
                  </div>
                </div>
              </Show>
            </Show>
          </Show>
        </div>
      </div>
    </MainLayout>
  );
};

export default ProjectListPage;
