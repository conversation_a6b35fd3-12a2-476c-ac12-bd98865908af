# PaoLife 功能清单与页面设计

## 项目概述

PaoLife 是一款基于 P.A.R.A. 方法论的桌面端个人生产力工具，集成了项目管理、领域维护、资源组织和复盘反思功能，旨在帮助用户构建一个高效的"第二大脑"系统。

## 总体导航架构

### 主导航栏（左侧）
- 🏠 **仪表盘** (Dashboard) - 全局概览和快速操作
- 📥 **收件箱** (Inbox) - 信息捕获和处理工作流
- 📋 **项目管理** (Projects) - 有明确目标的短期任务
- 🏠 **领域管理** (Areas) - 需要长期维护的责任领域
- 📚 **资源库** (Resources) - Markdown 知识管理
- 📦 **归档管理** (Archive) - 已完成项目和领域的存储
- 📝 **复盘总结** (Reviews) - 结构化反思和规划
- ⚙️ **设置** (Settings) - 应用配置和个性化

## 详细页面功能设计

### 1. 仪表盘页面 (Dashboard)

#### 页面目的
提供全局概览，让用户快速了解当前最重要的事务和整体进展情况。

#### 核心功能模块

##### 1.1 页面头部区域
- **个性化问候语**：
  - 根据时间显示不同问候
  - 显示用户自定义用户名
  - 当前日期和星期显示
  
- **快速捕捉功能**：
  - 智能输入框，支持快速记录想法和灵感
  - 实时标签解析（输入 `#` 触发标签建议）
  - 标签自动补全（基于预设标签和历史标签）
  - 快捷键支持（Shift+Enter 提交）
  - 自动保存到收件箱并触发跨页面同步

##### 1.2 统计概览卡片
- **项目统计卡片**：
  - 项目总数、进行中项目数量
  - 项目完成率和进度条显示
  - 状态分布（未开始、进行中、有风险、暂停、完成、归档）
  - 点击跳转到项目列表页面

- **领域统计卡片**：
  - 领域总数和活跃领域数量
  - 习惯总数和今日习惯完成率
  - 今日完成的习惯数量统计
  - 点击跳转到领域管理页面

- **任务统计卡片**：
  - 任务总数、活跃任务、已完成任务
  - 今日任务数量（到期/逾期）
  - 逾期任务警告提示
  - 任务完成率统计

- **资源统计卡片**：
  - 资源总数和最近添加数量
  - 按类型分布的资源统计
  - 点击跳转到资源库页面

##### 1.3 标签页切换区域
- **今日任务标签页**：
  - 自动聚合今日到期或逾期的任务
  - 任务完成状态快速切换
  - 逾期任务红色高亮警告
  - 显示任务所属项目
  - 优先级颜色标识
  - 完成/未完成任务数统计

- **即将到期项目标签页**：
  - 显示未来7天内即将截止的项目
  - 项目进度条（基于任务完成率）
  - 剩余天数倒计时显示
  - 项目状态标签
  - 任务完成统计（已完成/总数）
  - 快速跳转到项目详情

##### 1.4 右侧辅助信息区域
- **领域与习惯追踪**：
  - 显示前2个活跃领域
  - 习惯完成率计算（基于频率和时间范围）
  - 最近7天习惯热力图
  - 习惯连续完成天数统计
  - 点击跳转到领域详情

- **收件箱状态**：
  - 未处理项目数量显示
  - 今日新增项目统计
  - 总处理进度概览
  - 快速跳转到收件箱

- **最近活跃项目/领域**：
  - 按更新时间排序的活跃项目
  - 项目/领域状态显示
  - 快速导航链接
  - 最后更新时间

##### 1.5 智能提醒功能
- **周复盘提醒**：
  - 距离上次复盘超过7天时显示
  - 一键启动复盘流程
  - 自动更新复盘时间戳
  - 渐变背景突出显示

#### 技术特性
- **实时数据同步**：通过事件机制确保数据一致性
- **响应式设计**：适配移动端和桌面端
- **国际化支持**：多语言文本切换
- **可访问性**：键盘导航和 ARIA 标签

---

### 2. 收件箱页面 (Inbox)

#### 页面目的
作为信息的第一入口，捕获所有未经组织的灵感、想法、临时笔记，确保"大脑清空"，专注当下。

#### 核心功能模块

##### 2.1 快速捕获功能
- **多模态捕获入口**：
  - 快速捕获对话框，支持多种内容类型
  - 内容类型：笔记、任务、想法、链接、文件
  - 优先级设置（低/中/高）
  - 智能标签系统

- **标签管理系统**：
  - 预设标签和用户自定义标签
  - 标签自动补全和建议
  - 标签颜色和图标自定义
  - 标签使用频率统计

##### 2.2 项目处理工作流
- **智能分类过滤**：
  - 按内容类型过滤（全部/笔记/任务/想法/链接/文件）
  - 按处理状态过滤（全部/已处理/未处理）
  - 全文搜索（内容和标签）
  - 实时过滤结果统计

- **批量处理操作**：
  - 批量选择和操作
  - 批量标记为已处理
  - 批量移动到指定项目/领域
  - 批量删除功能

##### 2.3 转化工作流

- **转化为项目**：
  - 基于收件箱内容创建新项目
    - 项目基本信息设置
    - 关联目标领域
    - 自动生成初始任务
  - 基于收件箱内容更新到已有项目的任务中

- **转化为领域资源**：
  - 移动到指定领域
  - 创建为领域的任务
  - 添加为领域关键指标
  - 设置为习惯跟踪项

- **移动到资源库**：
  - 选择目标文件夹
  - 转换为 Markdown 文件
  - 保留标签和元数据
  - 建立反向链接

##### 2.4 未分配任务管理
- **孤立任务列表**：
  - 显示未分配到项目\领域\资源库的任务
  - 任务状态快速切换
  - 批量分配到项目功能
  - 任务优先级调整

##### 2.5 数据统计面板
- **处理进度概览**：
  - 未处理项目数量
  - 今日处理进度
  - 处理效率趋势
  - 各类型项目分布

#### 技术特性

- **跨页面同步**：自定义事件机制
- **实时搜索**：debounce 优化的搜索功能
- **拖拽排序**：项目优先级调整

---

### 3. 项目管理页面 (Projects)

#### 页面目的
集中管理所有有明确目标和截止日期的短期项目，提供多视图和灵活的项目跟踪功能。

#### 核心功能模块

##### 3.1 项目列表管理
- **多视图模式**：
  - **网格视图**：卡片式项目展示，适合快速浏览
  - **列表视图**：详细信息展示，适合数据分析
  - **看板视图**：按状态分列的拖拽式管理

- **智能筛选系统**：
  - 按项目状态筛选（全部/未开始/进行中/有风险/已暂停/已完成）
  - 按所属领域筛选
  - 按截止日期范围筛选
  - 全文搜索（项目名称、描述、目标）

- **排序选项**：
  - 按名称字母排序
  - 按截止日期排序
  - 按进度排序
  - 按最后更新时间排序

##### 3.2 项目创建和编辑
- **项目基本信息**：
  - 项目名称（必填）
  - 项目描述和目标
  - 项目交付物
  - -开始日期(可编辑,默认`今日`)
  - 截止日期设置
  - 所属领域关联

- **项目状态管理**：
  - 状态标签（未开始/进行中/有风险/已暂停/已完成）
  - 状态颜色标识
  - 状态变更历史
  - 自动状态推断

- **项目目标设置**：
  - 最终成果定义
  - 可量化指标设置
  - 成功标准checklist
  - 里程碑定义

##### 3.3 项目详情页面
- **项目概览面板**：
  - 项目基本信息展示
  - 进度条和完成率
  - 开始\截止\更新日期和倒计时
  - 项目状态历史

- **任务管理系统**：
  - 无限层级子任务支持
  - 任务拖拽排序和层级调整
  - 任务属性设置（截止日期、优先级）
  - 任务状态快速切换
  - 任务完成率自动计算

- **KPI 指标跟踪**：
  - 自定义关键绩效指标
  - 指标当前值和目标值
  - 支持增长型/减少型指标
  - 进度可视化图表
  - 历史数据趋势

- **资源关联管理**：
  - 关联 Markdown 文档
  - 外部链接管理
  - 文件附件上传
  - 双向链接系统

##### 3.4 项目协作功能
- **项目模板系统**：
  - 预设项目模板
  - 自定义模板创建
  - 模板快速应用
  - 模板共享机制

- **项目归档流程**：
  - 完成确认流程
  - 归档前检查清单
  - 归档原因记录
  - 数据保留策略

#### 技术特性
- **拖拽交互**：任务拖拽排序和层级调整
- **实时计算**：项目进度自动更新
- **数据验证**：表单输入验证
- **批量操作**：多选项目管理

---

### 4. 领域管理页面 (Areas)

#### 页面目的
管理需要长期关注和维护的个人或职业责任领域，通过习惯追踪和标准核查确保领域健康发展。

#### 核心功能模块

##### 4.1 领域列表管理
- **领域展示模式**：
  - 网格卡片视图：突出视觉效果
  - 详细列表视图：完整信息展示
  - 紧凑模式：高密度信息

- **领域筛选和搜索**：
  - 按领域状态筛选（活跃/维护/休眠）
  - 按习惯完成率筛选
  - 关键词搜索（名称、标准、描述）
  - 关联项目数量筛选

##### 4.2 领域创建和配置
- **领域基本设置**：
  - 领域名称和图标
  - 领域描述和维护标准
  - 颜色主题设置
  - 优先级权重

- **领域标准定义**：
  - 成功状态描述
  - 维护标准清单
  - 关键指标设定
  - 评估周期设置

##### 4.3 领域详情页面
- **领域概览仪表盘**：
  - 领域健康度评分
  - 习惯完成统计
  - 关联项目进展
  - 关键指标趋势

- **习惯追踪系统**：
  - 习惯创建和配置（频率、目标、类型）
  - 打卡记录和连续统计
  - 习惯完成热力图（日历视图）
  - 习惯完成率趋势分析
  - 习惯提醒和通知

- **习惯类型支持**：
  - 布尔习惯（完成/未完成）
  - 数值习惯（如运动时长、阅读页数）
  - 频率习惯（每日/每周X次/每月）
  - 时间段习惯（工作日/周末）

##### 4.4 关键指标管理
- **领域 KPI 设置**：
  - 自定义指标名称和单位
  - 目标值和当前值
  - 更新频率和数据源
  - 支持增长型/减少型指标
  - 指标权重分配

- **数据可视化**：
  - 指标趋势图表
  - 多指标对比
  - 目标达成进度
  - 历史数据回顾

##### 4.6 项目关联管理
- **领域项目展示**：
  - 显示所有关联项目
  - 项目状态和进度
  - 新建项目快捷入口(默认关联此领域)
  - 项目分配统计

#### 技术特性
- **日历组件**：习惯打卡日历
- **图表可视化**：指标趋势展示
- **数据聚合**：多维度统计分析

---

### 5. 资源库页面 (Resources)

#### 页面目的
提供强大的 Markdown 知识管理系统，支持文件组织、双向链接、全文搜索和知识网络构建。

#### 核心功能模块

##### 5.1 文件系统管理
- **文件树导航**：
  - 层级文件夹结构展示
  - 拖拽文件移动和整理
  - 文件夹创建、重命名、删除
  - 文件快速定位和搜索

- **文件操作功能**：
  - 新建 Markdown 文件
  - 文件重命名和复制
  - 文件删除
  - 批量文件操作

##### 5.2 Markdown 编辑器
- **编辑功能**：
  - 所见即所得编辑体验
  - 实时预览和渲染
  - 语法高亮和自动补全
  - 快捷键支持

- **高级功能**：
  - 代码块语法高亮
  - 数学公式渲染
  - 图片粘贴和上传
  - 表格编辑器
  - 任务列表支持

##### 5.3 双向链接系统
- **链接创建**：
  - `[[页面名称]]` 语法支持
  - 自动链接建议和补全
  - 链接目标自动创建
  - 链接预览功能

- **引用面板**：
  - 反向链接展示
  - 引用上下文预览
  - 引用关系图谱
  - 引用统计分析

##### 5.4 知识图谱
- **可视化展示**：
  - 节点关系图谱
  - 交互式探索界面
  - 链接强度可视化
  - 集群分析展示

- **导航功能**：
  - 图谱缩放和拖拽
  - 节点搜索和定位
  - 路径查找功能
  - 关联度分析

##### 5.5 搜索和发现
- **全文搜索**：
  - 内容全文索引
  - 关键词高亮显示
  - 搜索结果排序
  - 搜索历史记录

- **智能推荐**：
  - 相关文档推荐
  - 基于标签的关联
  - 阅读历史分析
  - 孤立文档发现

##### 5.6 专注模式
- **界面简化**：
  - 隐藏侧边栏和工具栏
  - 全屏编辑体验
  - 减少干扰元素
  - 沉浸式写作环境

#### 技术特性
- **文件系统API**：本地文件操作
- **Markdown解析**：自定义语法扩展
- **全文索引**：本地搜索引擎
- **图形渲染**：网络图谱可视化

---

### 6. 归档管理页面 (Archive)

#### 页面目的
统一管理已完成或不再活跃的项目、领域和资源，保持工作区整洁的同时保留历史数据。

#### 核心功能模块

##### 6.1 归档内容管理
- **归档项目展示**：
  - 按类型分类（项目/领域/资源）
  - 归档时间和原因记录
  - 原始状态信息保留
  - 归档操作历史

- **归档筛选系统**：
  - 按归档类型筛选
  - 按归档时间筛选
  - 按归档原因筛选
  - 全文搜索功能

##### 6.2 归档操作流程
- **自动归档触发**：
  - 项目完成自动提示归档
  - 长期未活跃项目提醒
  - 批量归档建议
  - 归档规则自定义

- **手动归档操作**：
  - 一键归档功能
  - 归档原因选择
  - 归档确认流程
  - 归档后通知

##### 6.3 归档内容查看
- **只读访问模式**：
  - 查看历史数据和内容
  - 任务完成记录查看
  - 文档内容只读预览
  - 关联关系保持

- **历史信息展示**：
  - 完整的历史记录
  - 时间线展示
  - 成就和里程碑
  - 数据统计摘要

##### 6.4 恢复功能
- **归档恢复**：
  - 选择性恢复功能
  - 恢复到原始状态
  - 数据完整性检查
  - 恢复后重新激活

##### 6.5 数据分析
- **归档统计**：
  - 归档项目数量统计
  - 归档趋势分析
  - 项目完成率分析
  - 归档原因分布

#### 技术特性
- **数据备份**：归档前数据备份
- **状态恢复**：完整状态恢复机制

---

### 7. 复盘总结页面 (Reviews)

#### 页面目的
提供结构化的工具和模板，帮助用户进行周期性反思和规划，促进持续改进和成长。

#### 核心功能模块

##### 7.1 复盘类型管理
- **复盘周期设置**：
  - 周复盘（每周进行）
  - 月复盘（每月进行）
  - 季度复盘（每季度进行）
  - 年度复盘（每年进行）
  - 自定义周期复盘

##### 7.2 复盘模板系统
- **预设模板**：
  - 标准周复盘模板
  - GTD 回顾模板
  - OKR 复盘模板
  - 个人成长模板
  - 项目复盘模板

- **自定义模板**：
  - 模板编辑器
  - 模板分享和导入
  - 模板版本管理
  - 模板使用统计

##### 7.3 复盘编辑器
- **结构化编辑**：
  - 基于模板的结构化输入
  - 富文本编辑支持
  - 数据引用和链接
  - 多媒体内容插入

- **智能数据聚合**：
  - 自动抓取周期内完成项目
  - 任务完成统计
  - 习惯执行情况
  - 关键指标变化

##### 7.4 数据可视化
- **进度展示**：
  - 项目完成情况图表
  - 习惯执行热力图
  - KPI 指标趋势
  - 时间分配分析

- **对比分析**：
  - 不同周期数据对比
  - 目标达成率分析
  - 改进趋势展示
  - 问题模式识别

##### 7.5 复盘历史管理
- **历史复盘列表**：
  - 按时间线排序展示
  - 复盘摘要预览
  - 快速搜索和筛选
  - 复盘标签分类

- **复盘洞察**：
  - 长期趋势分析
  - 重复问题识别
  - 改进建议生成
  - 成长轨迹追踪

#### 技术特性
- **模板引擎**：动态模板渲染
- **数据聚合**：跨模块数据统计
- **图表生成**：数据可视化
- **AI辅助**：智能洞察建议

---

### 8. 设置页面 (Settings)

#### 页面目的
提供应用级的配置选项，支持个性化定制、数据管理和系统优化。

#### 核心功能模块

##### 8.1 用户偏好设置
- **基本信息**：
  - 用户信息配置
  - 头像设置
  - 个人简介

- **界面主题**：
  - 浅色/深色/跟随系统
  - 强调色自定义
  - 字体大小调整
  - 界面密度设置

##### 8.2 语言和地区
- **多语言支持**：
  - 中文（默认）
  - English

- **地区设置**：
  - 时区配置
  - 日期格式
  - 数字格式
  - 货币单位

##### 8.3 编辑器配置
- **编辑器偏好**：
  - 编辑器主题(跟随软件主题)
  - 字体和大小设置
  - 行号显示

- **自动保存设置**：
  - 自动保存开关
  - 保存间隔设置
  - 版本历史保留
  - 冲突解决策略

##### 8.4 快捷键管理
- **快捷键配置**：
  - 预设快捷键方案
  - 自定义快捷键绑定
  - 快捷键冲突检测
  - 快捷键帮助文档

- **全局快捷键**：
  - 快速捕获快捷键
  - 全局搜索快捷键
  - 窗口管理快捷键

##### 8.5 数据和存储
- **数据库管理**：
  - 数据库统计信息
  - 数据库优化工具
  - 数据库备份/恢复
  - 数据完整性检查

- **文件管理**：
  - 资源库路径设置
  - 附件存储配置
  - 文件清理工具
  - 存储空间统计

##### 8.6 备份和同步
- **数据备份**：
  - 自动备份设置
  - 手动备份创建
  - 备份文件管理
  - 恢复功能

- **数据导入导出**：
  - 数据导出格式选择
  - 批量导入功能
  - 数据迁移工具
  - 格式转换支持

##### 8.7 高级设置
- **性能优化**：
  - 缓存管理
  - 索引重建
  - 内存使用优化
  - 启动速度优化

- **隐私和安全**：
  - 数据加密设置
  - 访问控制
  - 审计日志
  - 隐私模式

##### 8.8 PARA 方法论设置
- **项目设置**：
  - 默认项目模板
  - 自动归档规则
  - 项目状态定义
  - 完成标准设置

- **复盘设置**：
  - 复盘提醒频率
  - 默认复盘模板
  - 数据聚合范围
  - 复盘触发条件

#### 技术特性
- **配置持久化**：设置本地存储
- **主题系统**：CSS变量动态主题
- **插件系统**：功能模块化扩展
- **配置同步**：跨设备设置同步


### 核心特性
- **响应式设计** - 适配多种屏幕尺寸
- **国际化支持** - 多语言界面
- **离线优先** - 本地数据存储
- **实时同步** - 跨组件数据同步
- **可访问性** - 键盘导航和屏幕阅读器支持
- **性能优化** - 懒加载和虚拟滚动
