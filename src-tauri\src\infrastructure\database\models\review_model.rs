// 复盘系统数据模型
// 对应数据库中的复盘相关表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate};

/// 复盘模板数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ReviewTemplateModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub template_type: String,
    pub is_default: bool,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 复盘模板问题数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ReviewTemplateQuestionModel {
    pub id: String,
    pub template_id: String,
    pub question_text: String,
    pub question_type: String,
    pub sort_order: i32,
    pub is_required: bool,
    pub placeholder_text: Option<String>,
    pub created_at: DateTime<Utc>,
}

/// 复盘记录数据模型
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct ReviewModel {
    pub id: String,
    pub template_id: String,
    pub title: String,
    pub review_period_start: NaiveDate,
    pub review_period_end: NaiveDate,
    pub status: String,
    pub overall_rating: Option<i32>,
    pub key_insights: Option<String>,
    pub action_items: Option<String>, // JSON格式
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 复盘答案数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ReviewAnswerModel {
    pub id: String,
    pub review_id: String,
    pub question_id: String,
    pub answer_text: Option<String>,
    pub answer_number: Option<f64>,
    pub answer_boolean: Option<bool>,
    pub answer_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 复盘模板类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReviewTemplateType {
    Daily,
    Weekly,
    Monthly,
    Quarterly,
    Yearly,
    Project,
}

impl ReviewTemplateType {
    pub fn as_str(&self) -> &'static str {
        match self {
            ReviewTemplateType::Daily => "daily",
            ReviewTemplateType::Weekly => "weekly",
            ReviewTemplateType::Monthly => "monthly",
            ReviewTemplateType::Quarterly => "quarterly",
            ReviewTemplateType::Yearly => "yearly",
            ReviewTemplateType::Project => "project",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "daily" => Some(ReviewTemplateType::Daily),
            "weekly" => Some(ReviewTemplateType::Weekly),
            "monthly" => Some(ReviewTemplateType::Monthly),
            "quarterly" => Some(ReviewTemplateType::Quarterly),
            "yearly" => Some(ReviewTemplateType::Yearly),
            "project" => Some(ReviewTemplateType::Project),
            _ => None,
        }
    }
}

/// 复盘问题类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReviewQuestionType {
    Text,
    Rating,
    Boolean,
    Number,
    Date,
}

impl ReviewQuestionType {
    pub fn as_str(&self) -> &'static str {
        match self {
            ReviewQuestionType::Text => "text",
            ReviewQuestionType::Rating => "rating",
            ReviewQuestionType::Boolean => "boolean",
            ReviewQuestionType::Number => "number",
            ReviewQuestionType::Date => "date",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "text" => Some(ReviewQuestionType::Text),
            "rating" => Some(ReviewQuestionType::Rating),
            "boolean" => Some(ReviewQuestionType::Boolean),
            "number" => Some(ReviewQuestionType::Number),
            "date" => Some(ReviewQuestionType::Date),
            _ => None,
        }
    }
}

/// 复盘状态枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReviewStatus {
    Draft,
    Completed,
    Archived,
}

impl ReviewStatus {
    pub fn as_str(&self) -> &'static str {
        match self {
            ReviewStatus::Draft => "draft",
            ReviewStatus::Completed => "completed",
            ReviewStatus::Archived => "archived",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "draft" => Some(ReviewStatus::Draft),
            "completed" => Some(ReviewStatus::Completed),
            "archived" => Some(ReviewStatus::Archived),
            _ => None,
        }
    }
}

/// 复盘模板统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReviewTemplateStats {
    pub template_id: String,
    pub template_name: String,
    pub template_type: String,
    pub total_questions: i64,
    pub required_questions: i64,
    pub usage_count: i64,
    pub avg_rating: Option<f64>,
    pub completion_rate: f64,
}

/// 复盘统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReviewStats {
    pub total_reviews: i64,
    pub completed_reviews: i64,
    pub draft_reviews: i64,
    pub avg_overall_rating: Option<f64>,
    pub reviews_this_month: i64,
    pub reviews_this_quarter: i64,
    pub completion_rate: f64,
}

/// 复盘趋势数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReviewTrend {
    pub period: String, // 时间周期，如 "2024-01", "2024-Q1"
    pub review_count: i64,
    pub avg_rating: Option<f64>,
    pub completion_rate: f64,
}

/// 复盘洞察数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReviewInsight {
    pub insight_type: String, // 洞察类型：improvement, achievement, pattern
    pub title: String,
    pub description: String,
    pub confidence: f64, // 置信度 0-1
    pub related_reviews: Vec<String>, // 相关复盘ID
}

/// 行动项数据结构（用于JSON序列化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActionItem {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub priority: i32, // 1-5
    pub due_date: Option<NaiveDate>,
    pub status: String, // pending, in_progress, completed, cancelled
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 复盘答案值联合体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReviewAnswerValue {
    Text(String),
    Number(f64),
    Boolean(bool),
    Date(NaiveDate),
    Rating(i32), // 1-10
}

impl ReviewAnswerValue {
    /// 从数据库模型创建答案值
    pub fn from_model(model: &ReviewAnswerModel) -> Option<Self> {
        if let Some(text) = &model.answer_text {
            Some(ReviewAnswerValue::Text(text.clone()))
        } else if let Some(number) = model.answer_number {
            Some(ReviewAnswerValue::Number(number))
        } else if let Some(boolean) = model.answer_boolean {
            Some(ReviewAnswerValue::Boolean(boolean))
        } else if let Some(date) = model.answer_date {
            Some(ReviewAnswerValue::Date(date))
        } else {
            None
        }
    }

    /// 转换为文本表示
    pub fn to_string(&self) -> String {
        match self {
            ReviewAnswerValue::Text(s) => s.clone(),
            ReviewAnswerValue::Number(n) => n.to_string(),
            ReviewAnswerValue::Boolean(b) => b.to_string(),
            ReviewAnswerValue::Date(d) => d.to_string(),
            ReviewAnswerValue::Rating(r) => r.to_string(),
        }
    }
}

/// 复盘完整数据（包含问题和答案）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReviewWithAnswers {
    pub review: ReviewModel,
    pub template: ReviewTemplateModel,
    pub questions: Vec<ReviewTemplateQuestionModel>,
    pub answers: Vec<ReviewAnswerModel>,
}

/// 复盘模板完整数据（包含问题）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReviewTemplateWithQuestions {
    pub template: ReviewTemplateModel,
    pub questions: Vec<ReviewTemplateQuestionModel>,
}

impl ReviewTemplateWithQuestions {
    /// 获取必填问题数量
    pub fn required_questions_count(&self) -> usize {
        self.questions.iter().filter(|q| q.is_required).count()
    }

    /// 获取总问题数量
    pub fn total_questions_count(&self) -> usize {
        self.questions.len()
    }

    /// 按排序顺序获取问题
    pub fn sorted_questions(&self) -> Vec<&ReviewTemplateQuestionModel> {
        let mut questions: Vec<&ReviewTemplateQuestionModel> = self.questions.iter().collect();
        questions.sort_by_key(|q| q.sort_order);
        questions
    }
}
